<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>提现档位管理校验测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-case {
            border: 1px solid #ddd;
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
        }
        .test-case h3 {
            margin-top: 0;
            color: #333;
        }
        .pass {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .fail {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        .test-input {
            margin: 10px 0;
        }
        .test-input label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        .test-input input {
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 3px;
        }
        .error-message {
            color: #721c24;
            font-size: 14px;
            margin-top: 5px;
        }
        .success-message {
            color: #155724;
            font-size: 14px;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <h1>提现档位管理校验规则测试</h1>
    
    <div class="test-case">
        <h3>测试用例说明</h3>
        <p>本页面用于测试提现档位管理的校验规则：</p>
        <ul>
            <li><strong>档位数量限制</strong>：每个币种最多只能设置3个提现档位</li>
            <li><strong>档位金额递增校验</strong>：同一币种内，下方的档位金额必须大于上方的档位金额</li>
            <li><strong>USD币种金额范围校验</strong>：USD档位的金额必须在0.01到999.99之间</li>
            <li><strong>KHR币种金额范围校验</strong>：KHR档位的金额必须在100到9999999之间</li>
        </ul>
    </div>

    <div class="test-case">
        <h3>测试案例 1: USD金额范围校验</h3>
        <div class="test-input">
            <label>USD档位1:</label>
            <input type="text" id="usd1" placeholder="输入0.01-999.99之间的数值">
            <div id="usd1-result"></div>
        </div>
        <div class="test-input">
            <label>测试值:</label>
            <button onclick="testUSDRange('0.005', 'usd1')">0.005 (应该失败)</button>
            <button onclick="testUSDRange('0.01', 'usd1')">0.01 (应该通过)</button>
            <button onclick="testUSDRange('999.99', 'usd1')">999.99 (应该通过)</button>
            <button onclick="testUSDRange('1000', 'usd1')">1000 (应该失败)</button>
        </div>
    </div>

    <div class="test-case">
        <h3>测试案例 2: KHR金额范围校验</h3>
        <div class="test-input">
            <label>KHR档位1:</label>
            <input type="text" id="khr1" placeholder="输入100-9999999之间的数值">
            <div id="khr1-result"></div>
        </div>
        <div class="test-input">
            <label>测试值:</label>
            <button onclick="testKHRRange('99', 'khr1')">99 (应该失败)</button>
            <button onclick="testKHRRange('100', 'khr1')">100 (应该通过)</button>
            <button onclick="testKHRRange('9999999', 'khr1')">9999999 (应该通过)</button>
            <button onclick="testKHRRange('10000000', 'khr1')">10000000 (应该失败)</button>
        </div>
    </div>

    <div class="test-case">
        <h3>测试案例 3: 档位递增校验</h3>
        <div class="test-input">
            <label>USD档位1:</label>
            <input type="text" id="usd-tier1" value="10.00">
        </div>
        <div class="test-input">
            <label>USD档位2:</label>
            <input type="text" id="usd-tier2" placeholder="必须大于10.00">
            <div id="usd-tier2-result"></div>
        </div>
        <div class="test-input">
            <label>USD档位3:</label>
            <input type="text" id="usd-tier3" placeholder="必须大于档位2的值">
            <div id="usd-tier3-result"></div>
        </div>
        <div class="test-input">
            <label>测试值:</label>
            <button onclick="testIncreasing('5.00', 'usd-tier2')">档位2=5.00 (应该失败)</button>
            <button onclick="testIncreasing('15.00', 'usd-tier2')">档位2=15.00 (应该通过)</button>
            <button onclick="testIncreasing('12.00', 'usd-tier3')">档位3=12.00 (应该失败)</button>
            <button onclick="testIncreasing('20.00', 'usd-tier3')">档位3=20.00 (应该通过)</button>
        </div>
    </div>

    <script>
        // 模拟校验规则
        function validateUSDRange(value) {
            const numValue = parseFloat(value);
            if (isNaN(numValue)) {
                return { valid: false, message: '请输入有效的数字' };
            }
            if (numValue < 0.01 || numValue > 999.99) {
                return { valid: false, message: 'USD档位金额必须在0.01到999.99之间' };
            }
            return { valid: true, message: '校验通过' };
        }

        function validateKHRRange(value) {
            const numValue = parseFloat(value);
            if (isNaN(numValue)) {
                return { valid: false, message: '请输入有效的数字' };
            }
            if (numValue < 100 || numValue > 9999999) {
                return { valid: false, message: 'KHR档位金额必须在100到9999999之间' };
            }
            return { valid: true, message: '校验通过' };
        }

        function validateIncreasing(currentValue, previousValue) {
            const current = parseFloat(currentValue);
            const previous = parseFloat(previousValue);
            
            if (isNaN(current) || isNaN(previous)) {
                return { valid: false, message: '请输入有效的数字' };
            }
            
            if (current <= previous) {
                return { valid: false, message: `档位金额必须大于上方档位金额(${previous})` };
            }
            
            return { valid: true, message: '校验通过' };
        }

        function testUSDRange(value, inputId) {
            document.getElementById(inputId).value = value;
            const result = validateUSDRange(value);
            showResult(inputId + '-result', result);
        }

        function testKHRRange(value, inputId) {
            document.getElementById(inputId).value = value;
            const result = validateKHRRange(value);
            showResult(inputId + '-result', result);
        }

        function testIncreasing(value, inputId) {
            document.getElementById(inputId).value = value;
            let previousValue;
            
            if (inputId === 'usd-tier2') {
                previousValue = document.getElementById('usd-tier1').value;
            } else if (inputId === 'usd-tier3') {
                previousValue = document.getElementById('usd-tier2').value;
            }
            
            const result = validateIncreasing(value, previousValue);
            showResult(inputId + '-result', result);
        }

        function showResult(elementId, result) {
            const element = document.getElementById(elementId);
            element.innerHTML = result.message;
            element.className = result.valid ? 'success-message' : 'error-message';
        }

        // 添加实时校验
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('usd1').addEventListener('blur', function() {
                const result = validateUSDRange(this.value);
                showResult('usd1-result', result);
            });

            document.getElementById('khr1').addEventListener('blur', function() {
                const result = validateKHRRange(this.value);
                showResult('khr1-result', result);
            });

            document.getElementById('usd-tier2').addEventListener('blur', function() {
                const previousValue = document.getElementById('usd-tier1').value;
                const result = validateIncreasing(this.value, previousValue);
                showResult('usd-tier2-result', result);
            });

            document.getElementById('usd-tier3').addEventListener('blur', function() {
                const previousValue = document.getElementById('usd-tier2').value;
                const result = validateIncreasing(this.value, previousValue);
                showResult('usd-tier3-result', result);
            });
        });
    </script>
</body>
</html>
