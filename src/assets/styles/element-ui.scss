// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-popper {
  max-width: 60%;
  word-break: break-word;
}
.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0px;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    width: 60px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0px;
    }
  }
}

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
  &.flex-body {
    display: flex;
    flex-direction: column;
    position: absolute;
    // top: 50%;
    left: 50%;
    transform: translateX(-50%);
    max-height: calc(100% - 30px);
    .el-dialog__body {
      word-break: break-word;
      padding: 15px 24px;
      flex: 1;
      overflow: auto;
    }
    .el-dialog__footer {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      height: 80px;
      padding: 0 30px;
      box-shadow: 0px -4px 10px 0px rgba(0, 0, 0, 0.04);
    }
  }
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block
  }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

.el-menu--collapse
  > div
  > .el-submenu
  > .el-submenu__title
  .el-submenu__icon-arrow {
  display: none;
}

.el-dropdown .el-dropdown-link{
  color: var(--el-color-primary) !important;
}
.el-form-item__label {
  word-break: break-word;
}

.el-textarea {
  .el-textarea__inner {
    padding: 5px 15px 20px;
  }
  .el-input__count {
    right: 20px !important;
  }
}
