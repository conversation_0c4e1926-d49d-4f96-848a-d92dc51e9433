<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      v-show="showSearch"
      ref="queryFormRef"
      :inline="true"
      label-width="70px"
    >
      <el-form-item :label="$t('crawlerManage.channelName')" prop="channelName">
        <el-input
          v-model="queryParams.channelName"
          :placeholder="$t('common.pleaseEnter')"
          clearable
          @keyup.enter="handleQuery"
        />
        <!-- <el-select
          v-model="queryParams.channelIds"
          multiple
          filterable
          remote
          reserve-keyword
          :placeholder="$t('common.pleaseEnter')"
          :remote-method="channelNameSearchfun"
          :loading="state.loading"
        >
          <el-option
            v-for="item in state.options"
            :key="item.value"
            :label="item.name"
            :value="item.id"
          >
          </el-option>
        </el-select> -->
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">{{
          $t("common.search")
        }}</el-button>
        <el-button icon="Refresh" @click="resetForm(queryFormRef)">{{
          $t("common.reset")
        }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="showDialog('add')"
          >{{ $t("common.add") }}
        </el-button>
        <el-button
          type="primary"
          icon="Open"
          plain
          @click="changeSwitchStatus()"
          >{{ $t("crawlerManage.switchState") }}</el-button
        >
        <el-button
          type="danger"
          plain
          icon="Delete"
          @click="handleDelete(ids)"
          >{{ $t("common.delete") }}</el-button
        >
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="getListLoading"
      :data="tableDataList"
      @selection-change="handleTableSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column
        :label="$t('crawlerManage.mosappChannelName')"
        align="center"
        prop="channelName"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        :label="$t('crawlerManage.postingRule')"
        align="center"
        prop="releaseRule"
      >
        <template #default="scope">
          <span>{{
            scope.row.releaseRule === 1
              ? $t("crawlerManage.inTime")
              : $t("crawlerManage.regularly")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('crawlerManage.addTime')"
        align="center"
        prop="time"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('crawlerManage.enalbedState')"
        align="center"
        prop="state"
      >
        <template #default="scope">
          <el-switch
            v-model="scope.row.status"
            :before-change="() => changeSwitchStatus(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('crawlerManage.posts')"
        align="center"
        prop="count"
      >
        <template #default="scope">
          <el-button type="text" @click="showDialog('list', scope.row)">{{
            scope.row.releaseCount
          }}</el-button>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('common.operate')"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            type="text"
            icon="Edit"
            @click="showDialog('edit', scope.row)"
            >{{ $t("common.edit") }}</el-button
          >
          <el-button
            type="text"
            icon="Delete"
            @click="handleDelete([scope.row.id])"
            >{{ $t("common.delete") }}</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog
      v-if="state.dialogVisible"
      v-model="state.dialogVisible"
      :width="state.dialogWidth"
      append-to-body
      custom-class="el-dialog-no-header"
    >
      <Add
        v-if="state.dialogType === 'add' || state.dialogType === 'edit'"
        @close="closeDialog"
        :params="selectedRowData"
      />
      <List
        :params="selectedRowData"
        v-if="state.dialogType === 'list'"
        @close="closeDialog"
      />
    </el-dialog>
  </div>
</template>

<script setup name="RobotManage">
import { ref, reactive } from "vue";
import {
  getLists,
  deleteRobot,
  changeStatus,
  channelNameSearch,
} from "@/api/robotManage/index";
import { ElMessage, ElMessageBox } from "element-plus";
import Add from "./components/add.vue";
import List from "./components/list.vue";
import i18n from "../../i18n";

const ids = ref([]);
const showSearch = ref(true);
const selectedRowData = ref({});
const queryParams = ref({
  channelName: "",
  pageNum: 1,
  pageSize: 10,
  channelIds: [],
});
const state = reactive({
  dialogVisible: false,
  dialogType: "",
  dialogWidth: "500px",
  options: [],
  value: [],
  loading: false,
});
const total = ref(0);
const getListLoading = ref(false);
const tableDataList = ref([]);

const showDialog = (dialogType, row) => {
  if (row) selectedRowData.value = { ...row, dialogType };
  else selectedRowData.value = { dialogType };
  state.dialogType = dialogType;
  switch (dialogType) {
    case "add":
    case "edit":
      state.dialogWidth = "700px";
      break;
    case "list":
      state.dialogWidth = "650px";
      break;
    default:
      state.dialogWidth = "500px";
  }

  state.dialogVisible = true;
};
const closeDialog = (type) => {
  state.dialogVisible = false;
  state.dialogType = null;
  if (type === "add" || type === "edit") {
    getList();
  }
};

// 查询
const handleQuery = () => {
  queryParams.value.pageNum=1;
    queryParams.value.pageSize=10;
  getList();
};

const queryFormRef = ref();
// 重置查询
const resetForm = (formEl) => {
  if (!formEl) return;
  console.log(formEl);
  formEl.resetFields();
  getList();
};
const rows = ref([]);
const handleTableSelectionChange = (e) => {
  ids.value = e.map((item) => item.id);
  rows.value = e;
  console.log(ids.value);
};

function getList() {
  getListLoading.value = true;
  getLists(queryParams.value)
    .then((response) => {
      total.value = response.data.total;
      tableDataList.value = response.data.records;
    })
    .finally(() => (getListLoading.value = false));
}

function handleDelete(data) {
  if (data.length === 0) {
    ElMessage.warning(i18n.global.t("common.pleaseSelectDeleteItem"));
    return;
  }
  ElMessageBox.confirm(i18n.global.t("common.confirmDelete"), {
    confirmButtonText: i18n.global.t("common.confirm"),
    cancelButtonText: i18n.global.t("common.cancel"),
    type: "warning",
  })
    .then(() => {
      const params = {
        ids: data,
      };
      deleteRobot(params).then((item) => {
        ElMessage.success(i18n.global.t("common.success"));
        getList();
      });
    })
    .catch(() => {});
}

function changeSwitchStatus(row) {
  return new Promise((resolve, reject) => {
    const params = {
      ids: [],
      status: 1,
    };
    if (row) {
      params.ids = [row.id];
      params.status = Number(!row.status);
    } else {
      if (rows.value.length === 0) {
        ElMessage.warning(i18n.global.t("common.select"));
        return;
      }
      params.ids = ids.value;
      params.status = Number(rows.value.some((i) => !i.status));
    }
    changeStatus(params)
      .then((response) => {
        if (!row) {
          getList();
        }
        ElMessage.success(i18n.global.t("common.success"));
        resolve(true);
      })
      .catch(() => {
        if (row) row.status = !row.status;
        reject(new Error("Error"));
      });
  });
}

function channelNameSearchfun(query) {
  if (!query) {state.options=[]; return; }

  return new Promise((resolve, reject) => {
    const params = {
      pageNum: 1,
      pageSize: 50,
      chatType: 7,
      name: query,
    };
    state.loading = true;
    channelNameSearch(params)
      .then((response) => {
        state.options = response.data.records;
        state.loading = false;
      })
      .catch(() => {
        reject(new Error("Error"));
      });
  });
}
getList();
</script>

<style lang="scss" scoped>
.image-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);

  &:hover {
    border-color: var(--el-color-primary);
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100px;
    height: 100px;
    text-align: center;
  }

  .image {
    width: 100px;
    height: 100px;
    display: block;
  }
}
</style>
