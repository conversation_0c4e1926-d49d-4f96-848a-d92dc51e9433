<template>
  <div>
    <h3 class="dialog-title">{{ $t('crawlerManage.postRecord') }}</h3>
    <el-table
      v-loading="isLoading"
      :data="dataTable"
      style="width: 100%"
      @selection-change="handleTableSelectionChange"
    >
      <el-table-column :label="$t('crawlerManage.postTime')" align="center">
        <template #default="scope">{{ parseTime(scope.row.createTime) }}</template>
      </el-table-column>
      <el-table-column :label="$t('crawlerManage.postState')" align="center">
        <span style="color: #67c23a">{{ $t('crawlerManage.successfully') }}</span>
      </el-table-column>
      <el-table-column :label="$t('common.content')" align="center">
        <template #default="scope">
          <el-image
            v-if="scope.row.fileUrl && getImgUrl(scope.row.fileUrl)"
            class="img-msg"
            :src="getImgUrl(scope.row.fileUrl)"
            :preview-src-list="scope.row.fileUrl.split(',')"
            fit="contain"
          />
          <div v-else-if="scope.row.fileUrl">
            {{ getFileType(scope.row.fileUrl) }}
          </div>
          <div class="text-msg" v-else>{{ scope.row.message }}</div>
          <div>
            <el-button type="text" @click="showDialog('详情', scope.row)"
              >{{ $t('common.viewDetails') }}</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
      :pagerCount="4"
    />
    <el-dialog
      v-if="state.dialogVisible"
      v-model="state.dialogVisible"
      :width="state.dialogWidth"
      append-to-body
      custom-class="el-dialog-no-header"
    >
      <ChannelAdd
        v-if="state.dialogType === '详情'"
        :params="selectedRowData"
        @close="closeDialog"
      />
    </el-dialog>
  </div>
</template>

<script setup>
// 修改密码

import { defineProps, ref, reactive } from 'vue'
import { getSendRecords } from '@/api/channelOperations/channelOperations'
import ChannelAdd from '@/views/channelOperations/components/channelAdd.vue'
const props = defineProps({
  params: Object
})
const isLoading = ref(false)
const total = ref(0)
const dataTable = ref([])
const queryParams = ref({
  pageNum: 1,
  pageSize: 10
})

const getList = () => {
  isLoading.value = true
  getSendRecords({
    ...queryParams.value,
    channelId: props.params.fkConversationId,
    robotId: props.params.id
  })
    .then((response) => {
      total.value = response.data.total
      dataTable.value = response.data.records
    })
    .finally(() => (isLoading.value = false))
}
getList()

const selectedRowData = ref({})
const state = reactive({
  dialogVisible: false,
  dialogType: '',
  dialogWidth: '500px'
})
const showDialog = (dialogType, row) => {
  if (row) selectedRowData.value = { row: row, dialogType }
  state.dialogType = dialogType
  state.dialogWidth = '840px'
  state.dialogVisible = true
}

const closeDialog = (type) => {
  state.dialogVisible = false
  state.dialogType = null
}
function getImgUrl (fileUrls) {
  const urls = fileUrls.split(',')
  const fileType = urls[0].split('.').pop().toLowerCase()
  if (fileType === 'jpg' || fileType === 'jpeg' || fileType === 'png') {
    return urls[0]
  } else {
    return false
  }
}
function getFileType (fileUrls) {
  const urls = fileUrls.split(',')
  const fileType = urls[0].split('.').pop().toLowerCase()
  let messageType = ''
  if (fileType === 'pdf') {
    messageType = 'PDF 文件'
  } else if (fileType === 'jpg' || fileType === 'jpeg' || fileType === 'png') {
    messageType = '图片'
  } else if (fileType === 'mp4') {
    messageType = '视频'
  } else if (fileType === 'doc' || fileType === 'docx') {
    messageType = 'Word 文档'
  } else if (fileType === 'xls' || fileType === 'xlsx') {
    messageType = 'Excel 文件'
  } else if (fileType === 'ppt' || fileType === 'pptx') {
    messageType = 'PowerPoint 文档'
  } else {
    messageType = '其他类型'
  }
  return `【${messageType}】` // 移除最后一个逗号和空格
}
</script>

<style lang="scss" scoped>
:deep(.pagination-container) {
  display: flex;
  justify-content: center;
}
:deep(.pagination-container .el-pagination) {
  position: static;
}
.el-table__cell {
  position: static !important;
}
.img-msg {
  width: 150px;
  height: 100px;
  border-radius: 6px;
  background: #eee;
}
.text-msg {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
</style>
