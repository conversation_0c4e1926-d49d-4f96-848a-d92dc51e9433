<template>
  <div class="add-box">
    <h3 class="dialog-title">
      {{ props.params.dialogType === "add" ? $t('common.add') : $t('common.edit') }}
    </h3>
    <el-form
      ref="formRef"
      :model="formParams"
      :rules="rules"
      label-width="140px"
    >
      <el-form-item :label="$t('crawlerManage.channel')" prop="fkConversationId">
        <el-select
          v-model="formParams.fkConversationId"
          :placeholder="$t('common.select')"
          style="width: 100%"
          @visible-change="(val) => visibleChange(val, 'channel')"
          :teleported="false"
          filterable
          remote
          :remote-method="remoteChannel"
          :loading="
            channelState.tableData.length === 0 && channelState.getListLoading
          "
          v-if="props.params.dialogType === 'add'"
        >
          <div v-loading="channelState.getListLoading">
            <div
              class="infinite-list"
              v-infinite-scroll="loadChannel"
              :infinite-scroll-immediate="false"
            >
              <el-option
                v-for="item in channelState.tableData"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </div>
          </div>
        </el-select>
        <div v-else>{{ formParams.channelName }}</div>
      </el-form-item>
      <el-form-item :label="$t('crawlerManage.source')" prop="tgSpiderConfigId">
        <el-select
          v-model="formParams.tgSpiderConfigId"
          :placeholder="$t('common.select')"
          style="width: 100%"
          @visible-change="(val) => visibleChange(val, 'source')"
          :teleported="false"
          filterable
          remote
          :remote-method="remoteSource"
          :loading="
            sourceState.tableData.length === 0 && sourceState.getListLoading
          "
          v-if="props.params.dialogType === 'add'"
        >
          <div v-loading="sourceState.getListLoading">
            <div
              class="infinite-list"
              v-infinite-scroll="loadSource"
              :infinite-scroll-immediate="false"
            >
              <el-option
                v-for="item in sourceState.tableData"
                :key="item.id"
                :label="item.channelName"
                :value="item.id"
              />
            </div>
          </div>
        </el-select>
        <div v-else>{{ formParams.tgSpiderConfigName }}</div>
      </el-form-item>
      <el-form-item :label="$t('crawlerManage.postingRule')" prop="releaseRule">
        <el-radio-group v-model="formParams.releaseRule">
          <el-radio :label="1">{{$t('crawlerManage.inTime')}}</el-radio>
          <el-radio :label="2">{{$t('crawlerManage.regularly')}}</el-radio>
        </el-radio-group>
      </el-form-item>
      <template v-if="formParams.releaseRule === 2">
        <el-form-item :label="$t('crawlerManage.postDate')" prop="days">
          <el-checkbox-group v-model="formParams.days">
            <el-checkbox
              :label="day.value"
              v-for="day in days"
              :key="day.value"
              name="type"
              >{{ day.label }}</el-checkbox
            >
          </el-checkbox-group>
        </el-form-item>
        <el-form-item :label="$t('crawlerManage.postTime')" prop="times">
          <el-button
            type="primary"
            style="margin-bottom: 10px"
            @click="addTime()"
            >{{$t('crawlerManage.addPostTime')}}</el-button
          >
          <div
            style="width: 500px"
            v-for="(t, i) in formParams.times"
            :key="'t' + t.key"
          >
            <el-form-item
              :prop="'times.' + i + '.value'"
              :rules="{
                required: true,
                message: i18n.global.t('common.select'),
                trigger: 'change',
              }"
            >
              <el-select v-model="t.value" :placeholder="$t('common.select')">
                <el-option
                  v-for="(it) in timeArr"
                  :key="it"
                  :label="it"
                  :value="it"
                  :disabled="disTimes(it)"
                />
              </el-select>
              <el-button
                @click="delTime(i)"
                v-if="i !== 0"
                style="margin-left: 10px"
                icon="CloseBold"
                circle
              />
            </el-form-item>
          </div>
        </el-form-item>
      </template>
      <el-form-item :label="$t('crawlerManage.isEnabled')" prop="status">
        <el-switch
          v-model="formParams.status"
          :active-value="1"
          :inactive-value="0"
        />
      </el-form-item>
    </el-form>
    <div class="footer-options">
      <el-button
        :loading="state.submitLoading"
        type="primary"
        @click="submitForm"
        >{{ $t('common.confirm') }}</el-button
      >
      <el-button @click="emit('close')">{{ $t('common.cancel') }}</el-button>
    </div>
  </div>
</template>

<script setup>
import {
  reactive,
  getCurrentInstance,
  defineEmits,
  ref,
  defineProps
} from 'vue'
import { addRobot, robotDetail, editRobot } from '@/api/robotManage/index'
import { pageChannelList } from '@/api/channelOperations/channelOperations'
import { getConfigList } from '@/api/tgSpider/config'
import { ElMessage } from 'element-plus'
import i18n from "../../../i18n";
// import { sendMessage } from '@/api/consumer/imuser'
const { proxy } = getCurrentInstance()
const rules = reactive({
  fkConversationId: [
    { required: true, message: i18n.global.t('common.select'), trigger: 'change' }
  ],
  tgSpiderConfigId: [
    { required: true, message: i18n.global.t('common.select'), trigger: 'change' }
  ],
  releaseRule: [
    { required: true, message: i18n.global.t('common.select'), trigger: 'change' }
  ],
  days: [{ required: true, message: i18n.global.t('common.select'), trigger: 'change' }],
  times: [{ required: true, message: i18n.global.t('common.select'), trigger: 'change' }]
})

const props = defineProps({
  params: Object
})

const days = [
  {
    value: 1,
    label: i18n.global.t('common.monday')
  },
  {
    value: 2,
    label: i18n.global.t('common.tuesday')
  },
  {
    value: 3,
    label: i18n.global.t('common.wednesday')
  },
  {
    value: 4,
    label: i18n.global.t('common.thursday')
  },
  {
    value: 5,
    label: i18n.global.t('common.friday')
  },
  {
    value: 6,
    label: i18n.global.t('common.saturday')
  },
  {
    value: 7,
    label: i18n.global.t('common.sunday')
  }
]

const timeArr = ref([])
const rangeTime = () => {
  let time = []
  for (let i = 0; i < 24; i++) {
    time = time.concat([
      `${i < 10 ? '0' + i : i}:00`,
      `${i < 10 ? '0' + i : i}:30`
    ])
  }
  timeArr.value = time
}

rangeTime()

const formParams = reactive({
  fkConversationId: '',
  tgSpiderConfigId: '',
  releaseRule: 1,
  days: [],
  times: [
    {
      key: 0,
      value: ''
    }
  ],
  status: 1
})
const state = reactive({
  submitLoading: false
})

const emit = defineEmits(['close'])

const addTime = () => {
  const length = formParams.times.length
  if (length > 7) return
  formParams.times.push({
    key: length,
    value: ''
  })
}
const delTime = (index) => {
  formParams.times.splice(index, 1)
}

const disTimes = (i) => {
  const hasSelect = formParams.times
    .filter((_i, index) => index !== i)
    .map((j) => j.value)
  return hasSelect.includes(i)
}

const submitForm = () => {
  proxy.$refs.formRef.validate((valid) => {
    if (valid) {
      state.submitLoading = true
      const params = {
        ...formParams,
        fkConversationId: Number(formParams.fkConversationId),
        taskRule: []
      }
      if (params.releaseRule === 2) {
        formParams.days.forEach((i) => {
          formParams.times.forEach((j) => {
            params.taskRule.push(i + ',' + j.value)
          })
        })
      } else {
        delete params.taskRule
      }
      delete params.days
      delete params.times
      let method = addRobot
      if (props.params.dialogType === 'edit') {
        method = editRobot
        params.id = props.params.id
      }
      method(params)
        .then((response) => {
          ElMessage.success(i18n.global.t('common.success'))
          emit('close', 'add')
        })
        .finally(() => (state.submitLoading = false))
    }
  })
}

const channelState = reactive({
  getListLoading: false,
  tableData: [],
  pageNum: 1,
  pageSize: 50
})
const channelName = ref('')
const sourceState = reactive({
  getListLoading: false,
  tableData: [],
  pageNum: 1,
  pageSize: 50
})
const sourceName = ref('')
const getChannelList = () => {
  const params = {
    ...channelState,
    input: channelName.value
  }
  if (params.tableData) delete params.tableData
  channelState.getListLoading = true
  pageChannelList(params)
    .then((response) => {
      channelState.tableData = channelState.tableData.concat(
        response.data.records
      )
      channelState.total = response.data.total
    })
    .finally(() => (channelState.getListLoading = false))
}
const loadChannel = () => {
  channelState.pageNum += 1
  getChannelList()
}

const visibleChange = (val, type) => {
  if (!val) return
  if (type === 'channel') {
    channelName.value = ''
    channelState.pageNum = 1
    channelState.pageSize= 100
    channelState.tableData = []
    getChannelList()
  }
  if (type === 'source') {
    sourceName.value = ''
    sourceState.pageNum = 0
    sourceState.tableData = []
    getSourceList()
  }
}
const remoteChannel = (query) => {
  channelName.value = query
  channelState.pageNum = 1
  channelState.tableData = []
  getChannelList()
}

const getSourceList = () => {
  const params = {
    ...sourceState,
    channelName: sourceName.value
  }
  if (params.tableData) delete params.tableData
  sourceState.getListLoading = true
  getConfigList(params)
    .then((response) => {
      sourceState.tableData = sourceState.tableData.concat(
        response.data.records
      )
      sourceState.total = response.data.total
    })
    .finally(() => (sourceState.getListLoading = false))
}
const loadSource = () => {
  sourceState.pageNum += 1
  getSourceList()
}
const remoteSource = (query) => {
  sourceName.value = query
  sourceState.pageNum = 0
  sourceState.tableData = []
  getSourceList()
}

const mapParams = () => {
  if (!props.params.id) return
  robotDetail({
    id: props.params.id
  })
    .then((response) => {
      const {
        fkConversationId,
        channelName,
        tgSpiderConfigName,
        tgSpiderConfigId,
        releaseRule,
        status,
        taskRules
      } = response.data
      formParams.fkConversationId = fkConversationId
      formParams.channelName = channelName
      formParams.tgSpiderConfigName = tgSpiderConfigName
      formParams.tgSpiderConfigId = tgSpiderConfigId
      formParams.releaseRule = releaseRule
      formParams.status = status ? 1 : 0
      const days = []
      let times = []
      if (releaseRule === 2) {
        taskRules.forEach((i) => {
          const item = i.split(',')
          if (!days.includes(Number(item[0]))) days.push(Number(item[0]))
          const has = times.find((j) => j.value === item[1])
          const timsArr = times.map((j) => Number(j.value))
          if (!has && !timsArr.includes(item[1])) {
            const length = times.length
            times.push({
              key: length,
              value: item[1]
            })
          }
        })
      } else {
        times = [
          {
            key: 0,
            value: ''
          }
        ]
      }
      formParams.days = days
      formParams.times = times
    })
    .finally(() => {})
}
mapParams()
</script>

<style lang="scss" scoped>
.add-box {
  :deep(.el-select-dropdown__wrap) {
    max-height: none;
    .el-select-dropdown__item {
      max-width: 545px;
      padding: 0 20px;
    }
  }
  :deep(.el-form-item .el-form-item) {
    margin-bottom: 18px;
  }
}
.infinite-list {
  width: 100%;
  max-height: 330px;
  overflow-y: auto;
  margin-top: 10px;
}
.footer-options {
  text-align: right;
}
</style>
