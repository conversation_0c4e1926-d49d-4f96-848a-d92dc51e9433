<template>
  <div class="login-record">
    <h3 class="dialog-title">{{ $t("userManage.friendList") }}</h3>
    <el-form :model="queryParams" ref="queryFormRef" :inline="true">
      <el-form-item label="">
        <el-select
          style="width: 140px"
          v-model="queryParams.searchKey"
          clearable
          class="m-2"
          :placeholder="$t('common.select')"
        >
          <el-option :label="$t('userManage.nickname')" value="nickName" />
          <el-option :label="$t('common.phoneNumber')" value="phone" />
        </el-select>
        <el-input
          v-model="queryParams.searchValue"
          :placeholder="$t('common.pleaseEnter')"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item :label="$t('common.state')">
        <el-select
          style="width: 150px"
          v-model="queryParams.status"
          clearable
          class="m-2"
          :placeholder="$t('common.pleaseSelectState')"
        >
          <el-option :label="$t('userManage.normal')" :value="0" />
          <el-option :label="$t('common.freeze')" :value="1" />
          <el-option :label="$t('userManage.deactivated')" :value="2" />
        </el-select>
      </el-form-item>

      <el-form-item :label="$t('userManage.appOnlineState')">
        <el-select
          style="width: 150px"
          v-model="queryParams.appOnLineState"
          clearable
          class="m-2"
          :placeholder="$t('common.select')"
        >
          <el-option :label="$t('userManage.online')" :value="1" />
          <el-option :label="$t('userManage.offline')" :value="0" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('userManage.pcOnlineState')">
        <el-select
          style="width: 150px"
          v-model="queryParams.webOnLineState"
          clearable
          class="m-2"
          :placeholder="$t('common.select')"
        >
          <el-option :label="$t('userManage.online')" :value="1" />
          <el-option :label="$t('userManage.offline')" :value="0" />
        </el-select>
      </el-form-item>
      <el-form-item
        :label="$t('userManage.registrationTime')"
        style="width: 308px"
      >
        <el-date-picker
          v-model="queryParams.timeRanges"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          :start-placeholder="$t('common.startDate')"
          :end-placeholder="$t('common.endDate')"
        >
        </el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >{{ $t("common.search") }}
        </el-button>
        <el-button icon="Refresh" @click="resetQuery">{{
          $t("common.reset")
        }}</el-button>
      </el-form-item>
    </el-form>

    <el-table
      :data="state.tableData"
      v-loading="state.getListLoading"
      style="width: 100%"
    >
      <el-table-column label="ID" prop="idNumber" align="center" width="100" />
      <el-table-column
        :label="$t('userManage.nickname')"
        prop="nickname"
        align="center"
        width="100"
      />
      <el-table-column
        :label="$t('common.phoneNumber')"
        prop="phone"
        :show-overflow-tooltip="true"
        width="150"
      >
        <template #default="scope">
          <template v-if="scope.row.dialCode && scope.row.phone">
            <span v-if="scope.row.phone.includes('_')">{{
              `+${scope.row.dialCode} ${scope.row.phone.split("_")[1]}`
            }}</span>
            <span v-else>{{
              `+${scope.row.dialCode} ${scope.row.phone}`
            }}</span>
          </template>
        </template>
      </el-table-column>
      <!--      <el-table-column :label="$t('common.userId')" prop="id" :show-overflow-tooltip="true" width="100" />-->
      <el-table-column
        :label="$t('userManage.country')"
        prop="dialCode"
        width="100"
      >
        <template #default="scope">
          <span>{{
            scope.row.dialCode ? countryCodeText(scope.row) : ""
          }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="姓名" prop="name" width="100">
        <template #default="scope">
          <span>{{ nameTextStr(scope.row) }}</span>
        </template>
      </el-table-column> -->
      <el-table-column
        :label="$t('userManage.avatar')"
        prop="headPortrait"
        width="150"
      >
        <template #default="scope">
          <el-image
            style="width: 60px; height: 60px"
            :src="scope.row.headPortrait"
            :zoom-rate="1.2"
            :preview-src-list="[scope.row.headPortrait]"
            :initial-index="4"
            fit="cover"
          />
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('userManage.friendCount')"
        prop="friendCount"
        align="center"
        width="100"
      >
        <template #default="scope">
          <span v-if="scope.row.friendCount === 0">{{
            scope.row.friendCount
          }}</span>
          <el-button
            v-else
            type="text"
            @click="showFriendsList(scope.row.id)"
            >{{ scope.row.friendCount }}</el-button
          >
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('userManage.registrationTime')"
        align="center"
        prop="createTime"
           width="100"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('userManage.appOnlineState')"
        prop="appOnLineState"
        align="center"
        width="100"
      >
        <template #default="scope">
          <span v-if="scope.row.appOnLineState === 1">{{
            $t("userManage.online")
          }}</span>
          <span v-if="scope.row.appOnLineState === 0">{{
            $t("userManage.offline")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('userManage.pcOnlineState')"
        prop="webOnLineState"
        align="center"
        width="100"
      >
        <template #default="scope">
          <span v-if="scope.row.webOnLineState === 1">{{
            $t("userManage.online")
          }}</span>
          <span v-if="scope.row.webOnLineState === 0">{{
            $t("userManage.offline")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('userManage.lastOnlineTime')"
        prop="lastUpTime"
        align="center"
        width="100"
      >
              <template #default="scope">
          <span>{{ parseTime(scope.row.lastUpTime) }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column
        :label="$t('userManage.reportedTimes')"
        prop="beReportCount"
        align="center"
        width="100"
      /> -->
      <el-table-column
        :label="$t('common.state')"
        prop="status"
        align="center"
        width="100"
      >
        <template #default="scope">
          <span v-if="scope.row.status === 0">{{
            $t("userManage.normal")
          }}</span>
          <span v-if="scope.row.status === 1">{{ $t("common.freeze") }}</span>
          <span style="color: red" v-if="scope.row.status === 2">{{
            $t("userManage.deactivated")
          }}</span>
        </template>
      </el-table-column>
    </el-table>

    <div>
      <pagination
        v-show="state.total > 0"
        :total="state.total"
        v-model:page="state.pageNum"
        v-model:limit="state.pageSize"
        @pagination="getList"
      />
    </div>
        <el-dialog
      v-if="state.moreDialogVisible"
      v-model="state.moreDialogVisible"
      :width="state.moreDialogWidth"
      append-to-body
      custom-class="el-dialog-no-header"
    >
      <FriendList
        v-if="state.moreDialogType === '好友列表'"
        :params="seleectedRowData"
        @close="closeMoreDialog"
      ></FriendList>
    </el-dialog>
  </div>
</template>

<script setup name="loginRecord">
import { reactive, ref } from "vue";
import { getFriend } from "@/api/consumer/imuser";
import { countryCode } from "@/utils/enum.js";
import i18n from "../../../i18n";

const props = defineProps({
  params: Object,
});

const queryParams = ref({
  searchKey: "nickName",
  searchValue: "",
  nickname: null,
  phone: null,
  startTime: null,
  endTime: null,
  accountStatus: null,
  onLineState: null,
  isMember: null,
  timeRanges: [],
});

// 好友列表
const state = reactive({
  tableData: [],
  getListLoading: false,
  total: 100,
  pageNum: 1,
  pageSize: 10,
  id: props.params.id,
});

// 查询数据
function handleQuery() {
  getList();
}
const queryFormRef = ref();
// 重置查询
function resetQuery() {
  if (queryFormRef.value) {
    queryFormRef.value.resetFields();
  }
  queryParams.value.searchValue = "";
  queryParams.value.timeRanges = [];
  queryParams.value.accountStatus = null;
  queryParams.value.onLineState = null;
  queryParams.value.isMember = null;
  queryParams.value.searchKey = null;
  queryParams.value.status = null;
  queryParams.value.appOnLineState = null;
  queryParams.value.webOnLineState = null;

  handleQuery();
}

function countryCodeText(row) {
  const list = countryCode.filter((i) => i.tel === row.dialCode);
  let txt = "";
  if (list.length) {
    txt = i18n.global.locale === "en" ? list[0].en : list[0].name;
  }
  return txt;
}
const seleectedRowData = ref({
  userId: null,
  nickname: null
})

function showFriendsList (userId) {
  seleectedRowData.value.id = userId
  state.moreDialogWidth = '1200px'
  state.moreDialogType = '好友列表'
  state.moreDialogVisible = true
}

function getList() {
  state.getListLoading = true;

  if (queryParams.value.searchKey === "nickName") {
    queryParams.value.nickname = queryParams.value.searchValue;
    queryParams.value.phone = null;
  }
  if (queryParams.value.searchKey === "phone") {
    queryParams.value.nickname = null;
    queryParams.value.phone = queryParams.value.searchValue;
  }
  console.log(queryParams.value.searchValue);
  if (queryParams.value.searchValue === "") {
    queryParams.value.nickname = null;
    queryParams.value.phone = null;
  }
  const [startTime, endTime] = queryParams.value.timeRanges || [];
  queryParams.value.startTime =
    startTime === undefined ? null : +new Date(startTime + " 00:00:00");
  queryParams.value.endTime =
    endTime === undefined ? null : +new Date(endTime + " 23:59:59");

  const params = {
    ...state,
    ...queryParams.value,
  };
  if (params.tableData) delete params.tableData;
  getFriend(params).then((response) => {
    state.tableData = response.data.records;
    state.total = response.data.total;
  });
  state.getListLoading = false;
}
function nameTextStr(row) {
  const a1 = row.firstName ? row.firstName : "";
  const a2 = row.lastName ? row.lastName : "";
  const a3 = a1 && a2 ? "_" : "";
  return a1 + a3 + a2;
}
getList();
</script>

<style lang="scss" scoped>
.login-record {
  position: relative;
  min-height: 500px;
  .footer-options {
    text-align: right;
  }
}
</style>
