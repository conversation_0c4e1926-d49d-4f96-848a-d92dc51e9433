<template>
  <div>
    <h3 class="dialog-title">发送消息</h3>
    <el-form ref="formRef" :model="formParams" :rules="rules" label-width="100px">
      <el-form-item label="接收者ID"> {{state.userId}} </el-form-item>
      <el-form-item label="发送者昵称">
        <el-input :disabled="true" v-model="formParams.sender" type="text"></el-input>
      </el-form-item>
      <el-form-item label="消息内容" prop="msgText">
        <el-input maxlength=300 v-model.trim="formParams.msgText" type="textarea" rows="4"></el-input>
      </el-form-item>
    </el-form>
    <div class="footer-options">
      <el-button :loading="state.submitLoading" type="primary" @click="sendTextMsg">{{ $t('common.confirm') }}</el-button>
      <el-button @click="emit('close')">{{ $t('common.cancel') }}</el-button>
    </div>
  </div>
</template>

<script setup name="resetPassword">
// 修改密码

import { reactive, ref, defineProps, defineEmits } from 'vue'
import { ElMessage } from 'element-plus'
import { sendMessage } from '@/api/consumer/imuser'
import i18n from "../../../i18n";

const props = defineProps({
  params: Object
})

const emit = defineEmits(['close'])

const formRef = ref()

const rules = reactive({
  msgText: [{ required: true, message: i18n.global.t('common.pleaseEnter'), trigger: 'blur' }]
})
const formParams = reactive({
  userId: props.params.userId,
  sender: '客服',
  msgText: ''
})
const state = reactive({
  userId: props.params.userId,
  submitLoading: false
})

function sendTextMsg () {
  if (!formRef.value) return
  formRef.value.validate((valid, fields) => {
    if (valid) {
      sendMessage(formParams)
        .then((response) => {
          ElMessage.success(i18n.global.t('common.success'))
        })
        .finally(() => {
          state.submitLoading = false
          emit('close')
        })
    }
  })
}
</script>

<style lang="scss" scoped>
.footer-options {
  text-align: right;
}
</style>
