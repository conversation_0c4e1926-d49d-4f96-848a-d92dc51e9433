<template>
  <div>
    <h3 class="dialog-title">{{ $t('system.resetPassword') }}</h3>
    <el-form ref="formRef" :model="formParams" :rules="rules">
      <el-form-item :label="$t('login.inputNewPassword')" prop="password">
        <el-input type="password" v-model="formParams.password" :placeholder="$t('common.pleaseEnter')" />
      </el-form-item>
      <el-form-item :label="$t('login.confirmNewPassword')" prop="repassword">
        <el-input type="password" v-model="formParams.repassword" :placeholder="$t('common.pleaseEnter')" />
      </el-form-item>
    </el-form>
    <div class="footer-options">
      <el-button :loading="state.submitLoading" type="primary" @click="resetPsw">{{ $t('common.confirm') }}</el-button>
      <el-button @click="emit('close')">{{ $t('common.cancel') }}</el-button>
    </div>
  </div>
</template>

<script setup name="resetPassword">
// 修改密码

import { reactive, ref, defineProps, defineEmits } from 'vue'
import { ElMessage } from 'element-plus'
import { modifyPassword } from '@/api/consumer/imuser'
import i18n from "../../../i18n";

const props = defineProps({
  params: Object
})

const emit = defineEmits(['close'])

const validatePassWord = (rule, value, callback) => {
  if (value === '') {
    callback(new Error(i18n.global.t('common.pleaseEnterContent')))
  } else {
    // if (
    //   /^(?=.*\d)(?=.*[a-zA-Z])(?=.*[~!@#$%^&*])[\da-zA-Z~!@#$%^&*]{8,}$/.test(
    //     value
    //   )
    // ) {
    return callback()
    // }
    // callback(new Error('请输入8位以上密码,包含数字,大小写字母,特殊符号'))
  }
}

const validateRePassWord = (rule, value, callback) => {
  if (value === '') {
    callback(new Error(i18n.global.t('common.pleaseEnterContent')))
  } else if (value != formParams.password) {
    callback(new Error(i18n.global.t('login.passwordNotSame')))
  }
  return callback()
  // } else {
  //   if (
  //     /^(?=.*\d)(?=.*[a-zA-Z])(?=.*[~!@#$%^&*])[\da-zA-Z~!@#$%^&*]{8,}$/.test(
  //       value
  //     )
  //   ) {
  //     return callback()
  //   }
  //   callback(new Error('请输入8位以上密码,包含数字,大小写字母,特殊符号'))
  // }
}
const formRef = ref()

const rules = reactive({
  password: {
    validator: validatePassWord,
    trigger: 'blur'
  },
  repassword: {
    validator: validateRePassWord,
    trigger: 'blur'
  }
})
const formParams = reactive({
  userId: props.params.userId,
  password: '',
  repassword: ''
})
const state = reactive({
  submitLoading: false
})

function resetPsw () {
  if (!formRef.value) return
  formRef.value.validate((valid, fields) => {
    if (valid) {
      console.log(formParams.password)
      modifyPassword(formParams).then((response) => {
        if (response.code === 200) {
          ElMessage.success(i18n.global.t('common.success'))
          emit('close')
        }
      })
    }
  })
}
</script>

<style lang="scss" scoped>
.footer-options {
  text-align: right;
}
</style>
