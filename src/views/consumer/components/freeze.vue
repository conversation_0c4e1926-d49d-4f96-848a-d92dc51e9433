<template>
  <div>
    <h3 class="dialog-title">{{$t('common.freeze')}}</h3>
    <el-form ref="formRef" :model="formParams" :rules="rules">
      <el-form-item :label="$t('userManage.frozenTime')" prop="days">
        <el-select v-model="formParams.days" :placeholder="$t('userManage.selectFrozenTime')" @change="changeFreezeTime">
          <el-option
            v-for="item in state.timeOptions"
            :key="item.type"
            :label="item.label"
            :value="item.type">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('userManage.reason')" prop="freezeReason">
        <el-select v-model="formParams.freezeReason" :placeholder="$t('userManage.reason')">
          <el-option
            v-for="item in state.reasonOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <div class="footer-options">
      <el-button @click="emit('close')">{{ $t('common.cancel') }}</el-button>
      <el-button :loading="state.submitLoading" type="primary" @click="submitHandler">{{ $t('common.confirm') }}</el-button>
    </div>
  </div>
</template>

<script setup name="freeze">
// 修改密码

import { reactive, ref, defineProps, defineEmits } from 'vue'
import { ElMessage } from 'element-plus'
import { userFreeze, groupFreeze } from '@/api/consumer/imuser'
import i18n from "../../../i18n";

const props = defineProps({
  params: Object,
  from: String
})

const emit = defineEmits(['close'])

const formRef = ref()

const rules = reactive({
  days: [{ required: true, message: i18n.global.t('userManage.selectFrozenTime'), trigger: 'change' }],
  freezeReason: [{ required: true, message: i18n.global.t('userManage.selectFrozenTime'), trigger: 'change' }]
})
const formParams = reactive({
  targetId: props.params.userId,
  isFreeze: true,
  time: 0,
  unit: '',
  freezeReason: '',
  freezeTime: '',
  days: ''
})
const state = reactive({
  submitLoading: false,
  timeOptions: [
    { label: i18n.global.t('userManage.threeHours'), type: 1, value: 3, unit: 'hours' },
    { label: i18n.global.t('userManage.twelveHours'), type: 2, value: 12, unit: 'hours' },
    { label: i18n.global.t('userManage.oneDay'), type: 3, value: 1, unit: 'day' },
    { label: i18n.global.t('userManage.threeDays'), type: 4, value: 3, unit: 'day' },
    { label: i18n.global.t('userManage.oneWeek'), type: 5, value: 1, unit: 'week' },
    { label: i18n.global.t('userManage.oneMonth'), type: 7, value: '1', unit: 'month' },
    { label: i18n.global.t('userManage.threeMonths'), type: 8, value: '3', unit: 'month' },
    { label: i18n.global.t('userManage.permanent'), type: 6, value: '', unit: 'forever' }
  ],
  reasonOptions: [
    { label: i18n.global.t('userManage.harassmentAndBullying'), value: 1 },
    { label: i18n.global.t('userManage.inappropriateRemarks'), value: 2 },
    { label: i18n.global.t('userManage.violationOfRegulation'), value: 3 },
    { label: i18n.global.t('userManage.pornographicViolence'), value: 4 },
    { label: i18n.global.t('userManage.other'), value: 5 }
  ]
})

const changeFreezeTime = (val) => {
  console.log(val)
  const obj = state.timeOptions.filter(i => i.type === val)[0]
  formParams.time = obj.value
  formParams.unit = obj.unit
}
function submitHandler () {
  if (!formRef.value) return
  formRef.value.validate((valid, fields) => {
    if (valid) {
      let requestMethod = userFreeze
      if (props.from === 'group') {
        requestMethod = groupFreeze
      }
      requestMethod(formParams).then((response) => {
        if (response.code === 200) {
          ElMessage.success(i18n.global.t('common.success'))
          emit('close', 'init')
        }
      })
    }
  })
}
</script>

<style lang="scss" scoped>
.footer-options {
  text-align: right;
}
</style>
