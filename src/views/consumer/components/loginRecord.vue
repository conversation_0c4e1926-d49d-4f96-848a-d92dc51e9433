<template>
  <div class="login-record">
    <h3 class="dialog-title">{{ $t("userManage.registrationRecord") }}</h3>

    <!-- 功能区 -->
    <!-- <el-row style="height: 30px" :gutter="10" class="mb8"> -->
    <!-- <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="state.selectedIds.length === 0" @click="handleDelete" v-hasPermi="['system:role:remove']">删除</el-button>
      </el-col> -->
    <!-- <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['system:role:export']">{{$t('common.export')}}</el-button>
      </el-col> -->
    <!-- <right-toolbar v-model:showSearch="state.showSearch" @queryTable="getList"></right-toolbar> -->
    <!-- </el-row> -->
    <el-table
      :data="state.tableData"
      v-loading="state.getListLoading"
      style="width: 100%"
    >
      <el-table-column
        prop="onTime"
        :label="$t('userManage.onlineTime')"
        align="center"
        width="180"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.onTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="offTime"
        :label="$t('userManage.offlineTime')"
        align="center"
        width="180"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.offTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="lastActiveIp"
        :label="$t('userManage.loginIp')"
        align="center"
      />
      <el-table-column
        prop="loginAddress"
        :label="$t('userManage.loginIpAddress')"
        align="center"
      />
      <el-table-column
        prop="deviceInfo"
        :label="$t('userManage.loginDevice')"
        align="center"
      />
      <el-table-column
        prop="deviceVersion"
        :label="$t('userManage.systemVersion')"
        align="center"
      />
      <el-table-column
        prop="mosVersion"
        :label="$t('userManage.mosappVersion')"
        align="center"
      />
    </el-table>

    <div>
      <pagination
        v-show="state.total > 0"
        :total="state.total"
        v-model:page="state.pageNum"
        v-model:limit="state.pageSize"
        @pagination="getList"
      />
    </div>
  </div>
</template>

<script setup name="loginRecord">
import { reactive } from "vue";
import { loginHistory } from "@/api/consumer/imuser";

const props = defineProps({
  params: Object,
});

// 登录记录
const state = reactive({
  tableData: [],
  getListLoading: false,
  total: 100,
  pageNum: 1,
  pageSize: 10,
  userId: props.params.userId,
});

function getList() {
  state.getListLoading = true;
  loginHistory(state).then((response) => {
    state.tableData = response.data.records;
    state.total = response.data.total;
  });
  state.getListLoading = false;
}
getList();
</script>

<style lang="scss" scoped>
.login-record {
  position: relative;
  min-height: 500px;
  .footer-options {
    text-align: right;
  }
}
</style>
