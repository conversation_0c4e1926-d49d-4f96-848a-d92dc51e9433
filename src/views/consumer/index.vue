<template>
  <div class="app-container consumer-container">
    <el-form
      :model="queryParams"
      ref="queryFormRef"
      v-show="state.showSearch"
      :inline="true"
    >
      <el-form-item label="">
        <el-select
          style="width: 150px"
          v-model="queryParams.searchKey"
          clearable
          class="m-2"
          :placeholder="$t('common.select')"
        >
          <el-option :label="$t('userManage.nickname')" value="nickName" />
          <el-option :label="$t('userManage.phoneNumber')" value="phone" />
          <el-option label="ID" value="idNumber" />
        </el-select>
        <el-input
          v-model="queryParams.searchValue"
          :placeholder="$t('userManage.pleaseEnter')"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item :label="$t('userManage.state')">
        <el-select
          style="width: 150px"
          v-model="queryParams.status"
          clearable
          class="m-2"
          :placeholder="$t('userManage.pleaseSelectStatus')"
        >
          <el-option :label="$t('userManage.normal')" :value="0" />
          <el-option :label="$t('common.freeze')" :value="1" />
          <el-option :label="$t('userManage.deactivated')" :value="2" />
        </el-select>
      </el-form-item>

      <el-form-item :label="$t('userManage.appOnlineState')">
        <el-select
          style="width: 150px"
          v-model="queryParams.appOnLineState"
          clearable
          class="m-2"
          :placeholder="$t('userManage.pleaseSelectOnlineStatus')"
        >
          <el-option :label="$t('userManage.online')" :value="1" />
          <el-option :label="$t('userManage.offline')" :value="0" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('userManage.pcOnlineState')">
        <el-select
          style="width: 150px"
          v-model="queryParams.webOnLineState"
          clearable
          class="m-2"
          :placeholder="$t('userManage.pleaseSelectOnlineStatus')"
        >
          <el-option :label="$t('userManage.online')" :value="1" />
          <el-option :label="$t('userManage.offline')" :value="0" />
        </el-select>
      </el-form-item>

      <el-form-item
        :label="$t('userManage.registrationTime')"
        style="width: 308px"
      >
        <el-date-picker
          v-model="queryParams.timeRanges"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          :start-placeholder="$t('common.startDate')"
          :end-placeholder="$t('common.endDate')"
        >
        </el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >{{ $t("common.search") }}
        </el-button>
        <el-button icon="Refresh" @click="resetQuery">{{
          $t("common.reset")
        }}</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <!--      <el-col :span="1.5">-->
      <!--        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['system:role:add']">新增-->
      <!--        </el-button>-->
      <!--      </el-col>-->
      <!-- <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="state.selectedIds.length !== 1" @click="handleDelete" v-hasPermi="['system:role:remove']">删除
        </el-button>
      </el-col> -->
      <el-col :span="1.5">
        <!-- <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['user:export']"
          >{{$t('common.export')}}
        </el-button> -->
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['user:add']"
          >{{ $t("common.add") }}</el-button
        >
      </el-col>
      <right-toolbar
        v-model:showSearch="state.showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 表格数据 -->
    <el-table
      v-loading="state.getListLoading"
      :data="state.tableData"
      @selection-change="handleSelectionChange"
    >
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <el-table-column :label="$t('common.userId')" prop="id" align="center" width="100" />
      <el-table-column label="ID" prop="idNumber" align="center" width="100" />
      <el-table-column
        :label="$t('userManage.nickname')"
        prop="nickname"
        align="center"
        width="100"
      />
      <el-table-column
        :label="$t('userManage.phoneNumber')"
        prop="phone"
        :show-overflow-tooltip="true"
        width="150"
      >
        <template #default="scope">
          <template v-if="scope.row.dialCode && scope.row.phone">
            <span v-if="scope.row.phone.includes('_')">{{
              `+${scope.row.dialCode} ${scope.row.phone.split("_")[1]}`
            }}</span>
            <span v-else>{{
              `+${scope.row.dialCode} ${scope.row.phone}`
            }}</span>
          </template>
        </template>
      </el-table-column>
      <!--      <el-table-column :label="$t('common.userId')" prop="id" :show-overflow-tooltip="true" width="100" />-->
      <el-table-column
        :label="$t('userManage.country')"
        prop="dialCode"
        width="100"
      >
        <template #default="scope">
          <span>{{
            scope.row.dialCode ? countryCodeText(scope.row) : ""
          }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="姓名" prop="name" width="100">
        <template #default="scope">
          <span>{{ nameTextStr(scope.row) }}</span>
        </template>
      </el-table-column> -->
      <el-table-column
        :label="$t('userManage.avatar')"
        prop="headPortrait"
        width="150"
      >
        <template #default="scope">
          <el-image
            style="width: 60px; height: 60px"
            :src="scope.row.headPortrait"
            :zoom-rate="1.2"
            :preview-src-list="[scope.row.headPortrait]"
            :initial-index="4"
            fit="cover"
          />
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('userManage.friendCount')"
        prop="friendCount"
        align="center"
        width="100"
      >
        <template #default="scope">
          <span v-if="scope.row.friendCount === 0">{{
            scope.row.friendCount
          }}</span>
          <el-button
            v-else
            type="text"
            @click="showFriendsList(scope.row.id)"
            >{{ scope.row.friendCount }}</el-button
          >
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('userManage.registrationTime')"
        align="center"
        prop="createTime"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('userManage.appOnlineState')"
        prop="appOnLineState"
        align="center"
        width="100"
      >
        <template #default="scope">
          <span v-if="scope.row.appOnLineState === 1">{{
            $t("userManage.online")
          }}</span>
          <span v-if="scope.row.appOnLineState === 0">{{
            $t("userManage.offline")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('userManage.pcOnlineState')"
        prop="webOnLineState"
        align="center"
        width="100"
      >
        <template #default="scope">
          <span v-if="scope.row.webOnLineState === 1">{{
            $t("userManage.online")
          }}</span>
          <span v-if="scope.row.webOnLineState === 0">{{
            $t("userManage.offline")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('userManage.lastOnlineTime')"
        prop="lastUpTime"
        align="center"
        width="100"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.lastUpTime) }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column
        :label="$t('userManage.reportedTimes')"
        prop="beReportCount"
        align="center"
        width="100"
      /> -->
      <el-table-column
        :label="$t('userManage.userType')"
        prop="type"
        align="center"
        width="105"
      >
        <template #default="scope">
          <span v-if="scope.row.type === 'NORMAL'">{{
            $t("userManage.normalUser")
          }}</span>
          <span v-if="scope.row.type === 'GUEST'">{{
            $t("userManage.guest")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('channelReview.status')"
        align="center"
        prop="authStatus"
        width="150px"
      >
        <template #default="scope">
          <div :class="[`auth-status-${scope.row.authStatus}`]">
            <div v-if="scope.row.authType === 1">
              {{ $t("channelReview.personal") }}
            </div>
            <div v-else-if="scope.row.authType === 2">
              {{ $t("channelReview.enterprise") }}
            </div>
            <div v-else>{{ $t("channelReview.weiVerify") }}</div>
            <template v-if="scope.row.authType">
              <div v-if="scope.row.authStatus === 1">
                {{ $t("channelReview.inReview") }}
              </div>
              <div v-if="scope.row.authStatus === 2">
                {{ $t("channelReview.notPassed") }}
              </div>
              <div v-if="scope.row.authStatus === 3">
                {{ $t("channelReview.passed") }}
              </div>
              <div v-if="scope.row.authStatus === 4">
                {{ $t("channelReview.cancelVerify") }}
              </div>
            </template>
          </div>
        </template>
      </el-table-column>

      <el-table-column
        :label="$t('userManage.state')"
        prop="status"
        align="center"
        width="100"
      >
        <template #default="scope">
          <span style="color: red" v-if="scope.row.status === 2">{{
            $t("userManage.deactivated")
          }}</span>
          <span v-else-if="!scope.row.isFreeze">{{
            $t("userManage.normal")
          }}</span>
          <span v-else="scope.row.isFreeze">{{ $t("common.freeze") }}</span>
        </template>
      </el-table-column>

      <el-table-column
        prop="freezeTime"
        align="center"
        width="100"
        :label="$t('userManage.FrozenUntil')"
      >
        <template v-slot="scope">
          <span v-if="scope.row.freezeTime > 3000000000000">{{
            $t("userManage.Forever")
          }}</span>
          <span v-else-if="scope.row.freezeTime">{{
            parseTime(scope.row.freezeTime)
          }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="最后离线时间" prop="lastDownTime" align="center" width="100" /> -->
      <el-table-column
        :label="$t('userManage.operate')"
        align="center"
        width="180"
        fixed="right"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-tooltip
            :content="$t('userManage.registrationRecord')"
            placement="top"
            v-hasPermi="['system:role:remove']"
          >
            <el-button type="text" @click="showLoginRecord(scope.row.id)">{{
              $t("userManage.registrationRecord")
            }}</el-button>
          </el-tooltip>
          <el-button
            v-if="scope.row.authStatus === 3"
            type="text"
            @click="cancelVerify(scope.row.id)"
            >{{ $t("channelReview.cancelVerify") }}</el-button
          >
          <el-button
            type="text"
            @click="showUnFreeze(scope.row.id)"
            v-if="scope.row.isFreeze && scope.row.status != 2"
            >{{ $t("common.unfreeze") }}</el-button
          >
          <el-button
            type="text"
            @click="showFreeze(scope.row.id)"
            v-if="!scope.row.isFreeze && scope.row.status != 2"
            >{{ $t("common.freeze") }}</el-button
          >
          <el-button type="text" @click="showLogoutUser(scope.row.id)">{{
            $t("userManage.logoutUser")
          }}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="state.total > 0"
      :total="state.total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改对话框 -->
    <el-dialog
      :title="state.dialogTitle"
      v-model="state.dialogVisible"
      width="500px"
      append-to-body
    >
      <el-form
        ref="formRef"
        :model="formParams"
        :rules="formRules"
        label-width="120px"
      >
        <el-form-item :label="$t('userManage.firstName')">
          <el-input
            v-model="formParams.firstName"
            :placeholder="$t('common.pleaseEnter')"
          />
        </el-form-item>
        <el-form-item :label="$t('userManage.lastName')">
          <el-input
            v-model="formParams.lastName"
            :placeholder="$t('common.pleaseEnter')"
          />
        </el-form-item>
        <el-form-item :label="$t('common.phoneNumber')" prop="phone">
          <el-input
            v-model="formParams.phone"
            :placeholder="$t('common.pleaseEnter')"
          />
        </el-form-item>
        <el-form-item :label="$t('userManage.dialCode')" prop="dialCode">
          <el-input
            v-model="formParams.dialCode"
            :placeholder="$t('common.pleaseEnter')"
          />
        </el-form-item>
        <el-form-item :label="$t('userManage.password')" prop="password">
          <el-input
            type="password"
            v-model="formParams.password"
            :placeholder="$t('common.pleaseEnter')"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">{{
            $t("common.confirm")
          }}</el-button>
          <el-button @click="cancel">{{ $t("common.cancel") }}</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 更多操作 对话框 -->
    <el-dialog
      v-if="state.moreDialogVisible"
      v-model="state.moreDialogVisible"
      :width="state.moreDialogWidth"
      append-to-body
      custom-class="el-dialog-no-header"
    >
      <ResetPassword
        v-if="state.moreDialogType === '修改密码'"
        :params="seleectedRowData"
        @close="closeMoreDialog"
      ></ResetPassword>
      <LoginRecord
        v-else-if="state.moreDialogType === '登录记录'"
        :params="seleectedRowData"
        @close="closeMoreDialog"
      ></LoginRecord>
      <Bill
        v-else-if="state.moreDialogType === '账单'"
        :params="seleectedRowData"
        @close="closeMoreDialog"
      ></Bill>
      <CustomerServer
        v-else-if="state.moreDialogType === '设置客服'"
        :params="seleectedRowData"
        @close="closeMoreDialog"
      ></CustomerServer>
      <SendMsg
        v-else-if="state.moreDialogType === '发送消息'"
        :params="seleectedRowData"
        @close="closeMoreDialog"
      ></SendMsg>
      <Freeze
        v-else-if="state.moreDialogType === '冻结'"
        :params="seleectedRowData"
        @close="closeMoreDialog"
      ></Freeze>
      <FriendList
        v-else-if="state.moreDialogType === '好友列表'"
        :params="seleectedRowData"
        @close="closeMoreDialog"
      ></FriendList>
    </el-dialog>
  </div>
</template>

<script setup name="Consumer">
import { reactive, ref, getCurrentInstance } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import ResetPassword from "./components/resetPassword.vue";
import LoginRecord from "./components/loginRecord.vue";
import FriendList from "./components/friendList.vue";
import Bill from "./components/bill.vue";
import SendMsg from "./components/sendMsg.vue";
import CustomerServer from "./components/setCustomerServer.vue";
import Freeze from "./components/freeze.vue";
import { countryCode } from "@/utils/enum.js";
import i18n from "../../i18n";
import { cancelAuth } from "@/api/userReview";

import {
  listUser,
  saveUser,
  userBan,
  userFreeze,
  logoutUser,
} from "@/api/consumer/imuser";

const { proxy } = getCurrentInstance();

const queryFormRef = ref();
const formRef = ref();

// 搜索参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  searchKey: "nickName",
  searchValue: "",
  nickname: null,
  phone: null,
  id: null,
  startTime: null,
  endTime: null,
  accountStatus: null,
  onLineState: null,
  isMember: null,
  type: "NORMAL",
  timeRanges: [],
});

const state = reactive({
  showSearch: true,
  getListLoading: true,
  tableData: [],
  total: 0,
  selectedIds: [],
  dialogVisible: false,
  dialogTitle: "",
  moreDialogTitle: "",
  moreDialogVisible: false,
  moreDialogType: "",
  moreDialogWidth: "500px",
});

const validatePhone = (rule, value, callback) => {
  if (value === "") {
    callback(new Error(i18n.global.t("common.pleaseEnterContent")));
  } else {
    if (/\d+/.test(value)) {
      return callback();
    }
    callback(new Error(i18n.global.t("common.formatIncorrect")));
  }
};

const validateDialCode = (rule, value, callback) => {
  if (value === "" || value === undefined) {
    callback(new Error(i18n.global.t("common.cannotBeEmpty")));
  }
  return callback();
};

const validatePassword = (rule, value, callback) => {
  if (value === "") {
    callback(new Error(i18n.global.t("common.pleaseEnterContent")));
  } else {
    // if (
    //   /^(?=.*\d)(?=.*[a-zA-Z])(?=.*[~!@#$%^&*])[\da-zA-Z~!@#$%^&*]{8,}$/.test(
    //     value
    //   )
    // ) {
    return callback();
    // }
    // callback(new Error('请输入8位以上密码,包含数字,大小写字母,特殊符号'))
  }
};

// 新增用户参数
const formParams = ref({
  firstName: "",
  lastName: "",
  dialCode: "",
  phone: "",
  password: "",
  // sex: 1
});

// 充值/提现 金额参数/用户id
const rechargeParams = ref({
  userId: null,
  accountCode: null,
  amount: null,
});

const formRules = reactive({
  dialCode: {
    validator: validateDialCode,
    trigger: "blur",
  },

  phone: {
    validator: validatePhone,
    trigger: "blur",
  },
  password: {
    validator: validatePassword,
    trigger: "blur",
  },
});

// 获取列表数据
function getList() {
  state.getListLoading = true;
  if (queryParams.value.searchKey === "nickName") {
    queryParams.value.nickname = queryParams.value.searchValue;
    queryParams.value.phone = null;
    queryParams.value.idNumber = null;
  }
  if (queryParams.value.searchKey === "phone") {
    queryParams.value.nickname = null;
    queryParams.value.phone = queryParams.value.searchValue;
    queryParams.value.idNumber = null;
  }
  if (queryParams.value.searchKey === "idNumber") {
    queryParams.value.nickname = null;
    queryParams.value.phone = null;
    queryParams.value.idNumber = queryParams.value.searchValue;
  }
  if (queryParams.value.searchValue === "") {
    queryParams.value.nickname = null;
    queryParams.value.phone = null;
    queryParams.value.idNumber = null;
  }
  const [startTime, endTime] = queryParams.value.timeRanges || [];
  queryParams.value.startTime =
    startTime === undefined ? null : +new Date(startTime + " 00:00:00");
  queryParams.value.endTime =
    endTime === undefined ? null : +new Date(endTime + " 23:59:59");
  listUser(queryParams.value)
    .then((res) => {
      state.tableData = res.data.records;
      state.total = res.data.total;
    })
    .finally(() => (state.getListLoading = false));
}

// 查询数据
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

function nameTextStr(row) {
  const a1 = row.firstName ? row.firstName : "";
  const a2 = row.lastName ? row.lastName : "";
  const a3 = a1 && a2 ? "_" : "";
  return a1 + a3 + a2;
}
// 重置查询
function resetQuery() {
  if (queryFormRef.value) {
    queryFormRef.value.resetFields();
  }
  queryParams.value.searchValue = "";
  queryParams.value.timeRanges = [];
  queryParams.value.accountStatus = null;
  queryParams.value.onLineState = null;
  queryParams.value.isMember = null;
  queryParams.value.searchKey = null;
  queryParams.value.status = null;
  queryParams.value.appOnLineState = null;
  queryParams.value.webOnLineState = null;

  handleQuery();
}

// 封禁按钮操作
function handleDelete(row) {
  rechargeParams.value.userId = row;
  ElMessageBox.confirm(i18n.global.t("common.confirmBanUser"), {
    confirmButtonText: i18n.global.t("common.confirm"),
    cancelButtonText: i18n.global.t("common.cancel"),
    type: "warning",
  })
    .then(() => {
      userBan(rechargeParams.value).then((response) => {
        ElMessage({
          type: "success",
          message: i18n.global.t("common.freezeMsg"),
        });
        getList();
      });
    })
    .catch(() => {});
}

// 导出按钮操作
function handleExport() {
  if (queryParams.value.searchKey === "nickName") {
    queryParams.value.nickname = queryParams.value.searchValue;
    queryParams.value.phone = null;
    queryParams.value.id = null;
  }
  if (queryParams.value.searchKey === "phone") {
    queryParams.value.nickname = null;
    queryParams.value.phone = queryParams.value.searchValue;
    queryParams.value.id = null;
  }
  if (queryParams.value.searchKey === "id") {
    queryParams.value.nickname = null;
    queryParams.value.phone = null;
    queryParams.value.id = queryParams.value.searchValue;
  }
  if (queryParams.value.searchValue === "") {
    queryParams.value.nickname = null;
    queryParams.value.phone = null;
    queryParams.value.id = null;
  }
  const [startTime, endTime] = queryParams.value.timeRanges || [];
  queryParams.value.startTime =
    startTime === undefined ? null :  +new Date(startTime + '00:00:00');
  queryParams.value.endTime =
    endTime === undefined ? null :  +new Date(endTime + ' 23:59:59');
  proxy.download(
    "/imUser/exportUser",
    {
      ...queryParams.value,
    },
    `user_${new Date().getTime()}.xlsx`
  );
}

// 多选框选中数据
function handleSelectionChange(selection) {
  state.selectedIds = selection.map((item) => item.id);
  console.log(state.selectedIds);
}

function resetForm() {
  formParams.value = {
    firstName: "",
    lastName: "",
    dialCode: "",
    phone: "",
    password: "",
  };
  if (formRef.value) {
    formRef.value.resetFields();
  }
}

function cancelVerify(id) {
  ElMessageBox.confirm(
    i18n.global.t("common.confirmCancelUserVerify"),
    i18n.global.t("common.tips"),
    {
      confirmButtonText: i18n.global.t("common.confirm"),
      cancelButtonText: i18n.global.t("common.cancel"),
      type: "warning",
    }
  )
    .then(() => {
      const params = {
        userId: id,
      };
      cancelAuth(params).then(() => {
        ElMessage({
          type: "success",
          message: i18n.global.t("common.cancelVerifyMsg"),
        });
        getList();
      });
    })
    .catch(() => {
      // ElMessage.error({
      //   message: '操作失败！'
      // })
    });
}

function handleAdd() {
  resetForm();
  state.dialogTitle = i18n.global.t("common.add");
  state.dialogVisible = true;
}

// 取消按钮
function cancel() {
  state.dialogVisible = false;
}

// 提交
async function submitForm() {
  if (!formRef.value) return;
  formRef.value.validate((valid, fields) => {
    console.log(valid);
    if (valid) {
      saveUser(formParams.value).then((response) => {
        if (response.code === 200) {
          ElMessage.success(i18n.global.t("common.success"));
          state.dialogVisible = false;
          getList();
        }
      });
    }
  });
}

function closeMoreDialog(type) {
  state.moreDialogVisible = false;
  state.moreDialogType = null;
  if (type === "init") {
    getList();
  }
}

function countryCodeText(row) {
  const list = countryCode.filter((i) => i.tel === row.dialCode);
  let txt = "";
  if (list.length) {
    txt = i18n.global.locale === "en" ? list[0].en : list[0].name;
  }
  return txt;
}

// 重置密码对话框
const seleectedRowData = ref({
  userId: null,
  nickname: null,
});

function showResetPswDialog(userId) {
  seleectedRowData.value.userId = userId;
  state.moreDialogType = "修改密码";
  state.moreDialogWidth = "500px";
  state.moreDialogVisible = true;
}

// 更多操作
function handleMore(command, info) {
  switch (command) {
    case "loginRecord":
      showLoginRecord(info.id);
      break;
    case "bill":
      showBill(info);
      break;
    case "setAsService":
      setAsService(info.id);
      break;
    case "sendMsg":
      showSendMsg(info.id);
      break;
    default:
      break;
  }
}

// 登录记录
function showLoginRecord(userId) {
  seleectedRowData.value.userId = userId;
  state.moreDialogWidth = "1000px";
  state.moreDialogType = "登录记录";
  state.moreDialogVisible = true;
}

// 好友列表
function showFriendsList(userId) {
  seleectedRowData.value.id = userId;
  state.moreDialogWidth = "1200px";
  state.moreDialogType = "好友列表";
  state.moreDialogVisible = true;
}

function showBill(info) {
  seleectedRowData.value.userId = info.id;
  seleectedRowData.value.nickname = info.nickname;
  state.moreDialogWidth = "1000px";
  state.moreDialogType = "账单";
  state.moreDialogVisible = true;
}

function setAsService(userid) {
  seleectedRowData.value.userId = userid;
  console.log(seleectedRowData.value.userId);
  state.moreDialogWidth = "600px";
  state.moreDialogType = "设置客服";
  state.moreDialogVisible = true;
}

function showSendMsg(userid) {
  seleectedRowData.value.userId = userid;
  state.moreDialogWidth = "600px";
  state.moreDialogType = "发送消息";
  state.moreDialogVisible = true;
}

function showFreeze(userid) {
  seleectedRowData.value.userId = userid;
  state.moreDialogWidth = "450px";
  state.moreDialogType = "冻结";
  state.moreDialogVisible = true;
}

function showUnFreeze(userid) {
  ElMessageBox.confirm(
    i18n.global.t("common.confirmUnfrozen"),
    i18n.global.t("common.tips"),
    {
      confirmButtonText: i18n.global.t("common.confirm"),
      cancelButtonText: i18n.global.t("common.cancel"),
      type: "warning",
    }
  )
    .then(() => {
      const params = {
        freezeReason: 0,
        isFreeze: false,
        targetId: userid,
        time: 0,
        unit: "",
      };
      userFreeze(params).then((response) => {
        ElMessage({
          type: "success",
          message: i18n.global.t("common.unfreezeMsg"),
        });
        getList();
      });
    })
    .catch(() => {
      // ElMessage.error({
      //   message: '操作失败！'
      // })
    });
}

function showLogoutUser(userid) {
  ElMessageBox.confirm(
    i18n.global.t("common.confirmLogoutUser"),
    i18n.global.t("common.tips"),
    {
      confirmButtonText: i18n.global.t("common.confirm"),
      cancelButtonText: i18n.global.t("common.cancel"),
      type: "warning",
    }
  )
    .then(() => {
      const params = {
        userId: userid,
      };
      logoutUser(params).then((response) => {
        ElMessage({
          type: "success",
          message: i18n.global.t("common.success"),
        });
      });
    })
    .catch(() => {});
}

getList();
</script>
<style>
.consumer-container .el-table .el-table__cell {
  z-index: unset;
}
.el-dialog-no-header .el-table .el-table__cell {
  z-index: unset !important;
}
</style>
