<template>
   <el-form ref="userRef" :model="user" :rules="rules" label-width="80px">
      <el-form-item :label="$t('common.userNickname')" prop="nickName">
         <el-input v-model="user.nickName" maxlength="30" />
      </el-form-item>
      <el-form-item :label="$t('common.phoneNumber')" prop="phonenumber">
         <el-input v-model="user.phonenumber" maxlength="11" />
      </el-form-item>
      <el-form-item :label="$t('homePage.email')" prop="email">
         <el-input v-model="user.email" maxlength="50" />
      </el-form-item>
      <el-form-item :label="$t('homePage.sex')">
         <el-radio-group v-model="user.sex">
            <el-radio label="0">{{$t('homePage.man')}}</el-radio>
            <el-radio label="1">{{$t('homePage.woman')}}</el-radio>
         </el-radio-group>
      </el-form-item>
      <el-form-item>
      <el-button type="primary" @click="submit">{{$t('homePage.save')}}</el-button>
      <el-button type="danger" @click="close">{{$t('homePage.close')}}</el-button>
      </el-form-item>
   </el-form>
</template>

<script setup>
import { updateUserProfile } from '@/api/system/user'
import i18n from "../../../../i18n";

const props = defineProps({
  user: {
    type: Object
  }
})

const { proxy } = getCurrentInstance()

const rules = ref({
  nickName: [{ required: true, message: i18n.global.t('common.cannotBeEmpty'), trigger: 'blur' }],
  email: [{ required: true, message: i18n.global.t('common.cannotBeEmpty'), trigger: 'blur' },
    { type: 'email', message: i18n.global.t('common.formatIncorrect'), trigger: ['blur', 'change'] }],
  phonenumber: [{ required: true, message: i18n.global.t('common.cannotBeEmpty'), trigger: 'blur' },
    { pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: i18n.global.t('common.formatIncorrect'), trigger: 'blur' }]
})

/** 提交按钮 */
function submit () {
  proxy.$refs.userRef.validate(valid => {
    if (valid) {
      updateUserProfile(props.user).then(response => {
        proxy.$modal.msgSuccess(i18n.global.t('common.success'))
      })
    }
  })
};
/** 关闭按钮 */
function close () {
  proxy.$tab.closePage()
};
</script>
