<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--部门数据-->
      <!-- <el-col :span="4" :xs="24">
            <div class="head-container">
               <el-input
                  v-model="deptName"
                  :placeholder="$t('common.pleaseEnter')"
                  clearable
                  prefix-icon="Search"
                  style="margin-bottom: 20px"
               />
            </div>
            <div class="head-container">
               <el-tree
                  :data="deptOptions"
                  :props="{ label: 'label', children: 'children' }"
                  :expand-on-click-node="false"
                  :filter-node-method="filterNode"
                  ref="deptTreeRef"
                  default-expand-all
                  @node-click="handleNodeClick"
               />
            </div>
         </el-col> -->
      <!--用户数据-->
      <el-col :span="24" :xs="24">
        <el-form
          :model="queryParams"
          ref="queryRef"
          :inline="true"
          v-show="showSearch"
          label-width="120px"
        >
          <el-form-item :label="$t('system.userName')" prop="userName">
            <el-input
              v-model="queryParams.userName"
              :placeholder="$t('common.pleaseEnter')"
              clearable
              style="width: 240px"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item :label="$t('common.phoneNumber')" prop="phonenumber">
            <el-input
              v-model="queryParams.phonenumber"
              :placeholder="$t('common.pleaseEnter')"
              clearable
              style="width: 240px"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item :label="$t('common.state')" prop="status">
            <el-select
              v-model="queryParams.status"
              :placeholder="$t('common.select')"
              clearable
              style="width: 240px"
            >
              <el-option
                v-for="dict in sys_normal_disable"
                :key="dict.value"
                :label="$t(dict.label)"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('common.createdTime')" style="width: 308px">
            <el-date-picker
              v-model="dateRange"
              value-format="YYYY-MM-DD"
              type="daterange"
              range-separator="-"
              :start-placeholder="$t('common.startDate')"
              :end-placeholder="$t('common.endDate')"
            ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery"
              >{{ $t('common.search') }}</el-button
            >
            <el-button icon="Refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              plain
              icon="Plus"
              @click="handleAdd"
              v-hasPermi="['system:user:add']"
              >{{ $t('common.add') }}</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="success"
              plain
              icon="Edit"
              :disabled="single"
              @click="handleUpdate"
              v-hasPermi="['system:user:edit']"
              >{{ $t('common.edit') }}</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              plain
              icon="Delete"
              :disabled="multiple"
              @click="handleDelete"
              v-hasPermi="['system:user:remove']"
              >{{ $t('common.delete') }}</el-button
            >
          </el-col>
          <!-- <el-col :span="1.5">
            <el-button
              type="info"
              plain
              icon="Upload"
              @click="handleImport"
              v-hasPermi="['system:user:import']"
              >导入</el-button
            >
          </el-col> -->
          <el-col :span="1.5">
            <el-button
              type="warning"
              plain
              icon="Download"
              @click="handleExport"
              v-hasPermi="['system:user:export']"
              >{{$t('common.export')}}</el-button
            >
          </el-col>
          <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>

        <el-table
          v-loading="loading"
          :data="userList"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="50" align="center" />
          <el-table-column
            :label="$t('system.userID')"
            align="center"
            key="userId"
            prop="userId"
            v-if="columns[0].visible"
          />
          <el-table-column
            :label="$t('system.userName')"
            align="center"
            key="userName"
            prop="userName"
            v-if="columns[1].visible"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            :label="$t('common.userNickname')"
            align="center"
            key="nickName"
            prop="nickName"
            v-if="columns[2].visible"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            :label="$t('system.role')"
            align="center"
            key="status"
            v-if="columns[3].visible"
          >
            <template #default="scope">
              {{
                scope.row.roles.filter(i => i.flag)?.length ? scope.row.roles.filter(i => i.flag)[0].roleName : i18n.global.t('system.notAssigned')
              }}
            </template>
          </el-table-column>
          <!-- <el-table-column
            label="部门"
            align="center"
            key="deptName"
            prop="dept.deptName"
            v-if="columns[3].visible"
            :show-overflow-tooltip="true"
          /> -->
          <el-table-column
            :label="$t('common.phoneNumber')"
            align="center"
            key="phonenumber"
            prop="phonenumber"
            v-if="columns[4].visible"
            width="120"
          />

          <el-table-column
            :label="$t('common.createdTime')"
            align="center"
            prop="createTime"
            v-if="columns[5].visible"
            width="160"
          >
            <template #default="scope">
              <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('system.lastLoginTime')"
            align="center"
            prop="createTime"
            v-if="columns[6].visible"
            width="160"
          >
            <template #default="scope">
              <span>{{
                scope.row.loginDate && parseTime(scope.row.loginDate)
              }}</span>
            </template>
          </el-table-column>

          <el-table-column
            :label="$t('common.state')"
            align="center"
            key="status"
            v-if="columns[7].visible"
          >
            <template #default="scope">
              <el-switch
                v-model="scope.row.status"
                active-value="0"
                inactive-value="1"
                @change="handleStatusChange(scope.row)"
              ></el-switch>
            </template>
          </el-table-column>

          <el-table-column
            :label="$t('common.operate')"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template #default="scope">
              <el-tooltip
                :content="$t('common.edit')"
                placement="top"
                v-if="scope.row.userId !== 1"
              >
                <el-button
                  type="text"
                  icon="Edit"
                  @click="handleUpdate(scope.row)"
                  v-hasPermi="['system:user:edit']"
                ></el-button>
              </el-tooltip>
              <el-tooltip
                :content="$t('common.delete')"
                placement="top"
                v-if="scope.row.userId !== 1"
              >
                <el-button
                  type="text"
                  icon="Delete"
                  @click="handleDelete(scope.row)"
                  v-hasPermi="['system:user:remove']"
                ></el-button>
              </el-tooltip>
              <el-tooltip
                :content="$t('system.resetPassword')"
                placement="top"
                v-if="scope.row.userId !== 1"
              >
                <el-button
                  type="text"
                  icon="Key"
                  @click="handleResetPwd(scope.row)"
                  v-hasPermi="['system:user:resetPwd']"
                ></el-button>
              </el-tooltip>
              <!-- <el-tooltip
                content="分配角色"
                placement="top"
                v-if="scope.row.userId !== 1"
              >
                <el-button
                  type="text"
                  icon="CircleCheck"
                  @click="handleAuthRole(scope.row)"
                  v-hasPermi="['system:user:edit']"
                ></el-button>
              </el-tooltip> -->
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>

    <!-- 添加或修改用户配置对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form :model="form" :rules="rules" ref="userRef" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item :label="$t('common.userNickname')" prop="nickName">
              <el-input
                v-model="form.nickName"
                :placeholder="$t('common.pleaseEnter')"
                maxlength="30"
              />
            </el-form-item>
          </el-col>

          <!-- <el-col :span="12">
            <el-form-item label="归属部门" prop="deptId">
              <tree-select
                v-model:value="form.deptId"
                :options="deptOptions"
                :placeholder="$t('common.select')"
                :objMap="{ value: 'id', label: 'label', children: 'children' }"
              />
            </el-form-item>
          </el-col> -->
          <el-col :span="12">
            <el-form-item :label="$t('system.role')">
              <el-select v-model="form.roleIds[0]" :placeholder="$t('common.select')">
                <el-option
                  v-for="item in roleOptions"
                  :key="item.roleId"
                  :label="item.roleName"
                  :value="item.roleId"
                  :disabled="item.status == 1"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item
              v-if="form.userId == undefined"
              :label="$t('system.userName')"
              prop="userName"
            >
              <el-input
                v-model="form.userName"
                :placeholder="$t('common.pleaseEnter')"
                maxlength="30"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              v-if="form.userId == undefined"
              :label="$t('userManage.password')"
              prop="password"
            >
              <el-input
                v-model="form.password"
                :placeholder="$t('common.pleaseEnter')"
                type="password"
                maxlength="20"
                show-password
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item :label="$t('common.phoneNumber')" prop="phonenumber">
              <el-input
                v-model="form.phonenumber"
                :placeholder="$t('common.pleaseEnter')"
                maxlength="11"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('homePage.email')" prop="email">
              <el-input
                v-model="form.email"
                :placeholder="$t('common.pleaseEnter')"
                maxlength="50"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item :label="$t('homePage.sex')">
              <el-select v-model="form.sex" :placeholder="$t('common.select')">
                <el-option
                  v-for="dict in sys_user_sex"
                  :key="dict.value"
                  :label="$t(dict.label)"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('common.state')">
              <el-radio-group v-model="form.status">
                <el-radio
                  v-for="dict in sys_normal_disable"
                  :key="dict.value"
                  :label="dict.value"
                  >{{ $t(dict.label) }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <!-- <el-col :span="12">
            <el-form-item label="岗位">
              <el-select v-model="form.postIds" multiple :placeholder="$t('common.select')">
                <el-option
                  v-for="item in postOptions"
                  :key="item.postId"
                  :label="item.postName"
                  :value="item.postId"
                  :disabled="item.status == 1"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col> -->
          <!-- <el-col :span="12">
            <el-form-item label="角色">
              <el-select v-model="form.roleIds" multiple :placeholder="$t('common.select')">
                <el-option
                  v-for="item in roleOptions"
                  :key="item.roleId"
                  :label="item.roleName"
                  :value="item.roleId"
                  :disabled="item.status == 1"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col> -->
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item :label="$t('common.remark')" prop="remark">
              <el-input
                v-model="form.remark"
                type="textarea"
                :placeholder="$t('common.pleaseEnter')"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">{{ $t('common.confirm') }}</el-button>
          <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog
      :title="upload.title"
      v-model="upload.open"
      width="400px"
      append-to-body
    >
      <el-upload
        ref="uploadRef"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip text-center">
            <div class="el-upload__tip">
              <el-checkbox
                v-model="upload.updateSupport"
              />是否更新已经存在的用户数据
            </div>
            <span>仅允许导入xls、xlsx格式文件。</span>
            <el-link
              type="primary"
              :underline="false"
              style="font-size: 12px; vertical-align: baseline"
              @click="importTemplate"
              >下载模板</el-link
            >
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">{{ $t('common.confirm') }}</el-button>
          <el-button @click="upload.open = false">{{ $t('common.cancel') }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="User">
import { getToken } from '@/utils/auth'
import { treeselect } from '@/api/system/dept'
import i18n from "../../../i18n";
import { changeUserStatus, listUser, resetUserPwd, delUser, getUser, updateUser, addUser } from '@/api/system/user'

const router = useRouter()
const { proxy } = getCurrentInstance()
const { sys_normal_disable, sys_user_sex } = proxy.useDict('sys_normal_disable', 'sys_user_sex')

const userList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref('')
const dateRange = ref([])
const deptName = ref('')
const deptOptions = ref(undefined)
const initPassword = ref(undefined)
const postOptions = ref([])
const roleOptions = ref([])
/** * 用户导入参数 */
const upload = reactive({
  // 是否显示弹出层（用户导入）
  open: false,
  // 弹出层标题（用户导入）
  title: '',
  // 是否禁用上传
  isUploading: false,
  // 是否更新已经存在的用户数据
  updateSupport: 0,
  // 设置上传的请求头部
  headers: { Authorization: 'Bearer ' + getToken() },
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + 'system/user/importData'
})
// 列显隐信息
const columns = ref([
  { key: 0, label: '用户编号', visible: true },
  { key: 1, label: '用户名称', visible: true },
  { key: 2, label: '用户昵称', visible: true },
  { key: 3, label: '角色', visible: true },
  { key: 4, label: '手机号码', visible: true },

  { key: 5, label: '创建时间', visible: true },
  { key: 6, label: '最后登录时间', visible: true },
  { key: 7, label: '状态', visible: true }
])

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    userName: undefined,
    phonenumber: undefined,
    status: undefined,
    deptId: undefined
  },
  rules: {
    userName: [{ required: true, message: i18n.global.t('common.cannotBeEmpty'), trigger: 'blur' },
      { min: 2, max: 20, message: i18n.global.t('common.strLengthRange')+'2,20', trigger: 'blur' }],
    nickName: [{ required: true, message: i18n.global.t('common.cannotBeEmpty'), trigger: 'blur' }],
    password: [{ required: true, message: i18n.global.t('common.cannotBeEmpty'), trigger: 'blur' },
      { min: 5, max: 20, message: i18n.global.t('common.strLengthRange')+'5,20', trigger: 'blur' }],
    email: [{ type: 'email', message: i18n.global.t('common.formatError'), trigger: ['blur', 'change'] }],
    phonenumber: [{ pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: i18n.global.t('common.formatError'), trigger: 'blur' }],
    remark: [{ max: 500, message: i18n.global.t('common.charactersExceeds') + '500', trigger: 'blur' }]
  }
})

const { queryParams, form, rules } = toRefs(data)

/** 通过条件过滤节点  */
const filterNode = (value, data) => {
  if (!value) return true
  return data.label.indexOf(value) !== -1
}
/** 根据名称筛选部门树 */
watch(deptName, val => {
  proxy.$refs.deptTreeRef.filter(val)
})
/** 查询部门下拉树结构 */
function getTreeselect () {
  treeselect().then(response => {
    deptOptions.value = response.data
  })
};
/** 查询用户列表 */
function getList () {
  loading.value = true
  listUser(proxy.addDateRange(queryParams.value, dateRange.value)).then(res => {
    loading.value = false
    userList.value = res.rows
    total.value = res.total
  })
};
/** 节点单击事件 */
function handleNodeClick (data) {
  queryParams.value.deptId = data.id
  handleQuery()
};
/** 搜索按钮操作 */
function handleQuery () {
  queryParams.value.pageNum = 1
  getList()
};
/** 重置按钮操作 */
function resetQuery () {
  dateRange.value = []
  proxy.resetForm('queryRef')
  handleQuery()
};
/** 删除按钮操作 */
function handleDelete (row) {
  const userIds = row.userId || ids.value
  proxy.$modal.confirm(i18n.global.t('system.confirmDelete') + userIds ).then(function () {
    return delUser(userIds)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess(i18n.global.t('common.success'))
  }).catch(() => {})
};
/** 导出按钮操作 */
function handleExport () {
  proxy.download('system/user/export', {
    ...queryParams.value
  }, `user_${new Date().getTime()}.xlsx`)
};
/** 用户状态修改  */
function handleStatusChange (row) {
  const text = row.status === '0' ? i18n.global.t('common.enable') : i18n.global.t('common.disable')
  proxy.$modal.confirm(text + ' "' + row.userName + '"?').then(function () {
    return changeUserStatus(row.userId, row.status)
  }).then(() => {
    proxy.$modal.msgSuccess(text + i18n.global.t('common.success'))
  }).catch(function () {
    row.status = row.status === '0' ? '1' : '0'
  })
};
/** 更多操作 */
function handleCommand (command, row) {
  switch (command) {
    case 'handleResetPwd':
      handleResetPwd(row)
      break
    case 'handleAuthRole':
      handleAuthRole(row)
      break
    default:
      break
  }
};
/** 跳转角色分配 */
function handleAuthRole (row) {
  const userId = row.userId
  router.push('/system/user-auth/role/' + userId)
};
/** 重置密码按钮操作 */
function handleResetPwd (row) {
  proxy.$prompt(i18n.global.t('common.pleaseEnter') + ' "' + row.userName + i18n.global.t('system.newPassword'), i18n.global.t('common.tips'), {
    confirmButtonText: i18n.global.t('common.confirm'),
    cancelButtonText: i18n.global.t('common.cancel'),
    closeOnClickModal: false,
    inputPattern: /^.{5,20}$/,
    inputErrorMessage: i18n.global.t('homePage.pwdLengthLimit')
  }).then(({ value }) => {
    resetUserPwd(row.userId, value).then(response => {
      proxy.$modal.msgSuccess(i18n.global.t('common.success'))
    })
  }).catch(() => {})
};
/** 选择条数  */
function handleSelectionChange (selection) {
  ids.value = selection.map(item => item.userId)
  single.value = selection.length != 1
  multiple.value = !selection.length
};
/** 导入按钮操作 */
function handleImport () {
  upload.title = '用户导入'
  upload.open = true
};
/** 下载模板操作 */
function importTemplate () {
  proxy.download('system/user/importTemplate', {
  }, `user_template_${new Date().getTime()}.xlsx`)
};
/** 文件上传中处理 */
const handleFileUploadProgress = (event, file, fileList) => {
  upload.isUploading = true
}
/** 文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {
  upload.open = false
  upload.isUploading = false
  proxy.$refs.uploadRef.clearFiles()
  proxy.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + '</div>', '导入结果', { dangerouslyUseHTMLString: true })
  getList()
}
/** 提交上传文件 */
function submitFileForm () {
  proxy.$refs.uploadRef.submit()
};
/** 初始化部门数据 */
function initTreeData () {
  // 判断部门的数据是否存在，存在不获取，不存在则获取
  if (deptOptions.value === undefined) {
    treeselect().then(response => {
      deptOptions.value = response.data
    })
  }
};
/** 重置操作表单 */
function reset () {
  form.value = {
    userId: undefined,
    deptId: undefined,
    userName: undefined,
    nickName: undefined,
    password: undefined,
    phonenumber: undefined,
    email: undefined,
    sex: undefined,
    status: '0',
    remark: undefined,
    postIds: [],
    roleIds: []
  }
  proxy.resetForm('userRef')
};
/** 取消按钮 */
function cancel () {
  open.value = false
  reset()
};
/** 新增按钮操作 */
function handleAdd () {
  reset()
  initTreeData()
  getUser().then(response => {
    postOptions.value = response.posts
    roleOptions.value = response.roles
    open.value = true
    title.value = i18n.global.t('common.add')
    form.password.value = initPassword.value
  })
};
/** 修改按钮操作 */
function handleUpdate (row) {
  reset()
  initTreeData()
  const userId = row.userId || ids.value
  getUser(userId).then(response => {
    form.value = response.data
    postOptions.value = response.posts
    roleOptions.value = response.roles
    form.value.postIds = response.postIds
    form.value.roleIds = response.roleIds
    open.value = true
    title.value = i18n.global.t('common.edit')
    form.password = ''
  })
};
/** 提交按钮 */
function submitForm () {
  proxy.$refs.userRef.validate(valid => {
    if (valid) {
      if (form.value.userId != undefined) {
        updateUser(form.value).then(response => {
          proxy.$modal.msgSuccess(i18n.global.t('common.success'))
          open.value = false
          getList()
        })
      } else {
        addUser(form.value).then(response => {
          proxy.$modal.msgSuccess(i18n.global.t('common.success'))
          open.value = false
          getList()
        })
      }
    }
  })
};

getTreeselect()
getList()
</script>
