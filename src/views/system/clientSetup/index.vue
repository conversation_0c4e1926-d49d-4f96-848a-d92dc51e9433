<template>
  <div class="app-container">
    <el-form>
      <el-form-item :label="item.buttonName" v-for="item in settings.customerSettingList" :key="item.id">
        <el-radio-group v-model="item.status">
          <el-radio :label="1">开启</el-radio>
          <el-radio :label="0">{{ $t('common.close') }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <div class="footer-options">
        <el-button type="primary" @click="onSubmit">{{ $t('common.cancel') }}</el-button>
        <el-button>{{ $t('common.cancel') }}</el-button>
      </div>
    </el-form>
  </div>
</template>

<script setup name="clientSetup">
import { reactive, ref } from 'vue'
import {
  getCustomerSettingList,
  savaCustomerSettingList
} from '@/api/system/clientSetup'

const setupPatams = ref({
  redBag: false
})

const settings = reactive({
  customerSettingList: []
})

const onSubmit = () => {
  savaCustomerSettingList(settings.customerSettingList)
    .then((response) => {
      alert(response.msg)
    })
    .catch((error) => {
      alert(error.msg)
    })
}

// 获取列表数据
function getList () {
  getCustomerSettingList().then((response) => {
    settings.customerSettingList = response.data
    console.log(settings.customerSettingList)
  })
}

getList()
</script>

<style lang="scss" scoped>
.app-container {
  position: absolute;
  left: 50%;
  top: 30%;
  transform: translate(-50%, -50%);
  margin: auto;
}

.footer-options {
  margin: 30px 0;
}
</style>
