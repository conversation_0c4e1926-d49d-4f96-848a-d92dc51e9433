<template>
  <div class="app-container">
    <div class="module-container">
      <div class="title">{{$t('system.userAgreement')}}</div>
      <div id="agreement-editor" style="width: 100%; height: 300px"></div>
    </div>
    <div class="module-container">
      <div class="title">{{$t('system.privacyPolicy')}}</div>
      <div id="privacy-editor" style="width: 100%; height: 300px"></div>
    </div>

    <div class="footer-options">
      <el-button type="primary" @click="save">{{ $t('common.save') }}</el-button>
      <el-button @click="deleteEditorContent">{{ $t('common.cancel') }}</el-button>
    </div>
  </div>
</template>

<script setup name="PrivacyEdit">
import { reactive, onMounted } from 'vue'
import Quill from 'quill'
import 'quill/dist/quill.snow.css'
import { ElMessage } from 'element-plus'
import { getAgreementPrivacy, saveAgreementPrivacy } from '@/api/system/privacy'
import i18n from '../../../i18n'

let agreementEditor
let privacyEditor

const state = reactive({
  id: '',
  userAgreement: '',
  privacyPolicy: ''
})

onMounted(() => {
  const toolbarOptions = [
    ['bold', 'italic', 'underline', 'strike'], // toggled buttons
    ['blockquote', 'code-block'],
    [{ header: 1 }, { header: 2 }], // custom button values
    [{ list: 'ordered' }, { list: 'bullet' }],
    [{ script: 'sub' }, { script: 'super' }], // superscript/subscript
    [{ indent: '-1' }, { indent: '+1' }], // outdent/indent
    [{ direction: 'rtl' }], // text direction
    [{ size: ['small', false, 'large', 'huge'] }], // custom dropdown
    [{ header: [1, 2, 3, 4, 5, 6, false] }],
    [{ color: [] }, { background: [] }], // dropdown with defaults from theme
    [{ align: [] }],
    ['clean'] // remove formatting button
  ]
  agreementEditor = new Quill('#agreement-editor', {
    theme: 'snow',
    modules: {
      toolbar: toolbarOptions
    },
    placeholder: i18n.global.t('common.pleaseEnter')
  })
  privacyEditor = new Quill('#privacy-editor', {
    theme: 'snow',
    modules: {
      toolbar: toolbarOptions
    },
    placeholder: i18n.global.t('common.pleaseEnter')
  })
  getContent()
})

function getContent () {
  getAgreementPrivacy().then((response) => {
    if (response.data != null) {
      state.userAgreement = response.data.userAgreement
      state.privacyPolicy = response.data.privacyPolicy
      state.id = response.data.id
      agreementEditor.root.innerHTML = state.userAgreement
      privacyEditor.root.innerHTML = state.privacyPolicy
    }
  })
}

const save = () => {
  if (agreementEditor.getLength() > 10000) {
    ElMessage({
      type: 'warning',
      message: '用户协议文字长度超过了限制'
    })
    return
  }
  if (privacyEditor.getLength() > 10000) {
    ElMessage({
      type: 'warning',
      message: '隐私政策文字长度超过了限制'
    })
  }
  state.userAgreement = agreementEditor.root.innerHTML
  state.privacyPolicy = privacyEditor.root.innerHTML
  saveAgreementPrivacy(state)
    .then((response) => {
      console.log(state)
      if (response.code === 200) {
        ElMessage.success(i18n.global.t('common.success'))
      }
    })
    .catch((response) => {
      ElMessage.error(i18n.global.t('common.failed'))
    })
}

const deleteEditorContent = () => {
  agreementEditor.deleteText(0, agreementEditor.getLength())
  privacyEditor.deleteText(0, privacyEditor.getLength())
}
</script>

<style lang="scss" scoped>
.module-container {
  margin-bottom: 30px;
  .title {
    font-size: 18px;
    margin-bottom: 16px;
  }
}
.footer-options {
  text-align: center;
  margin: 30px;
}
</style>
