<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="130px">
         <el-form-item :label="$t('system.dictionaryName')" prop="dictName">
            <el-input
               v-model="queryParams.dictName"
               :placeholder="$t('common.pleaseEnter')"
               clearable
               style="width: 240px"
               @keyup.enter="handleQuery"
            />
         </el-form-item>
         <el-form-item :label="$t('system.dictionaryType')" prop="dictType">
            <el-input
               v-model="queryParams.dictType"
               :placeholder="$t('common.pleaseEnter')"
               clearable
               style="width: 240px"
               @keyup.enter="handleQuery"
            />
         </el-form-item>
         <el-form-item :label="$t('common.state')" prop="status">
            <el-select
               v-model="queryParams.status"
               :placeholder="$t('system.dictionaryStatus')"
               clearable
               style="width: 240px"
            >
               <el-option
                  v-for="dict in sys_normal_disable"
                  :key="dict.value"
                  :label="$t(dict.label)"
                  :value="dict.value"
               />
            </el-select>
         </el-form-item>
         <el-form-item :label="$t('common.createdTime')" style="width: 308px">
            <el-date-picker
               v-model="dateRange"
               value-format="YYYY-MM-DD"
               type="daterange"
               range-separator="-"
               :start-placeholder="$t('common.startDate')"
               :end-placeholder="$t('common.endDate')"
            ></el-date-picker>
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">{{ $t('common.search') }}</el-button>
            <el-button icon="Refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
         </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
         <el-col :span="1.5">
            <el-button
               type="primary"
               plain
               icon="Plus"
               @click="handleAdd"
               v-hasPermi="['system:dict:add']"
            >{{ $t('common.add') }}</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button
               type="success"
               plain
               icon="Edit"
               :disabled="single"
               @click="handleUpdate"
               v-hasPermi="['system:dict:edit']"
            >{{ $t('common.edit') }}</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button
               type="danger"
               plain
               icon="Delete"
               :disabled="multiple"
               @click="handleDelete"
               v-hasPermi="['system:dict:remove']"
            >{{ $t('common.delete') }}</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button
               type="warning"
               plain
               icon="Download"
               @click="handleExport"
               v-hasPermi="['system:dict:export']"
            >{{$t('common.export')}}</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button
               type="danger"
               plain
               icon="Refresh"
               @click="handleRefreshCache"
               v-hasPermi="['system:dict:remove']"
            >{{ $t('system.refreshCache') }}</el-button>
         </el-col>
         <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="typeList" @selection-change="handleSelectionChange">
         <el-table-column type="selection" width="55" align="center" />
         <el-table-column :label="$t('system.dictionaryCode')" align="center" prop="dictId" />
         <el-table-column :label="$t('system.dictionaryName')" align="center" prop="dictName" :show-overflow-tooltip="true"/>
         <el-table-column :label="$t('system.dictionaryType')" align="center" :show-overflow-tooltip="true">
            <template #default="scope">
               <router-link :to="'/system/dict-data/index/' + scope.row.dictId" class="link-type">
                  <span>{{ scope.row.dictType }}</span>
               </router-link>
            </template>
         </el-table-column>
         <el-table-column :label="$t('common.state')" align="center" prop="status">
            <template #default="scope">
               <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
            </template>
         </el-table-column>
         <el-table-column :label="$t('common.remark')" align="center" prop="remark" :show-overflow-tooltip="true" />
         <el-table-column :label="$t('common.createdTime')" align="center" prop="createTime" width="180">
            <template #default="scope">
               <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
         </el-table-column>
         <el-table-column :label="$t('common.operate')" align="center" class-name="small-padding fixed-width">
            <template #default="scope">
               <el-button
                  type="text"
                  icon="Edit"
                  @click="handleUpdate(scope.row)"
                  v-hasPermi="['system:dict:edit']"
               >{{ $t('common.edit') }}</el-button>
               <el-button
                  type="text"
                  icon="Delete"
                  @click="handleDelete(scope.row)"
                  v-hasPermi="['system:dict:remove']"
               >{{ $t('common.delete') }}</el-button>
            </template>
         </el-table-column>
      </el-table>

      <pagination
         v-show="total > 0"
         :total="total"
         v-model:page="queryParams.pageNum"
         v-model:limit="queryParams.pageSize"
         @pagination="getList"
      />

      <!-- 添加或修改参数配置对话框 -->
      <el-dialog :title="title" v-model="open" width="500px" append-to-body>
         <el-form ref="dictRef" :model="form" :rules="rules" label-width="140px">
            <el-form-item :label="$t('system.dictionaryName')" prop="dictName">
               <el-input v-model="form.dictName" :placeholder="$t('common.pleaseEnter')" />
            </el-form-item>
            <el-form-item :label="$t('system.dictionaryType')" prop="dictType">
               <el-input v-model="form.dictType" :placeholder="$t('common.pleaseEnter')" />
            </el-form-item>
            <el-form-item :label="$t('common.state')" prop="status">
               <el-radio-group v-model="form.status">
                  <el-radio
                     v-for="dict in sys_normal_disable"
                     :key="dict.value"
                     :label="dict.value"
                  >{{ $t(dict.label) }}</el-radio>
               </el-radio-group>
            </el-form-item>
            <el-form-item :label="$t('common.remark')" prop="remark">
               <el-input v-model="form.remark" type="textarea" :placeholder="$t('common.pleaseEnter')"></el-input>
            </el-form-item>
         </el-form>
         <template #footer>
            <div class="dialog-footer">
               <el-button type="primary" @click="submitForm">{{ $t('common.confirm') }}</el-button>
               <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
            </div>
         </template>
      </el-dialog>
   </div>
</template>

<script setup name="Dict">
import { listType, getType, delType, addType, updateType, refreshCache } from '@/api/system/dict/type'
import i18n from "../../../i18n";

const { proxy } = getCurrentInstance()
const { sys_normal_disable } = proxy.useDict('sys_normal_disable')

const typeList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref('')
const dateRange = ref([])

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    dictName: undefined,
    dictType: undefined,
    status: undefined
  },
  rules: {
    dictName: [{ required: true, message: i18n.global.t('common.cannotBeEmpty'), trigger: 'blur' },
      { max: 100, message: i18n.global.t('common.charactersExceeds') + '100', trigger: 'blur' }],
    dictType: [{ required: true, message: i18n.global.t('common.cannotBeEmpty'), trigger: 'blur' },
      { max: 100, message: i18n.global.t('common.charactersExceeds') + '100', trigger: 'blur' }],
    remark: [{ max: 500, message: i18n.global.t('common.charactersExceeds') + '500', trigger: 'blur' }]
  }
})

const { queryParams, form, rules } = toRefs(data)

/** 查询字典类型列表 */
function getList () {
  loading.value = true
  listType(proxy.addDateRange(queryParams.value, dateRange.value)).then(response => {
    typeList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}
/** 取消按钮 */
function cancel () {
  open.value = false
  reset()
}
/** 表单重置 */
function reset () {
  form.value = {
    dictId: undefined,
    dictName: undefined,
    dictType: undefined,
    status: '0',
    remark: undefined
  }
  proxy.resetForm('dictRef')
}
/** 搜索按钮操作 */
function handleQuery () {
  queryParams.value.pageNum = 1
  getList()
}
/** 重置按钮操作 */
function resetQuery () {
  dateRange.value = []
  proxy.resetForm('queryRef')
  handleQuery()
}
/** 新增按钮操作 */
function handleAdd () {
  reset()
  open.value = true
  title.value = i18n.global.t('common.add')
}
/** 多选框选中数据 */
function handleSelectionChange (selection) {
  ids.value = selection.map(item => item.dictId)
  single.value = selection.length != 1
  multiple.value = !selection.length
}
/** 修改按钮操作 */
function handleUpdate (row) {
  reset()
  const dictId = row.dictId || ids.value
  getType(dictId).then(response => {
    form.value = response.data
    open.value = true
    title.value = i18n.global.t('common.edit')
  })
}
/** 提交按钮 */
function submitForm () {
  proxy.$refs.dictRef.validate(valid => {
    if (valid) {
      if (form.value.dictId != undefined) {
        updateType(form.value).then(response => {
          proxy.$modal.msgSuccess(i18n.global.t('common.success'))
          open.value = false
          getList()
        })
      } else {
        addType(form.value).then(response => {
          proxy.$modal.msgSuccess(i18n.global.t('common.success'))
          open.value = false
          getList()
        })
      }
    }
  })
}
/** 删除按钮操作 */
function handleDelete (row) {
  const dictIds = row.dictId || ids.value
  proxy.$modal.confirm(i18n.global.t('system.confirmDelete') + dictIds).then(function () {
    return delType(dictIds)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess(i18n.global.t('common.success'))
  }).catch(() => {})
}
/** 导出按钮操作 */
function handleExport () {
  proxy.download('system/dict/type/export', {
    ...queryParams.value
  }, `dict_${new Date().getTime()}.xlsx`)
}
/** 刷新缓存按钮操作 */
function handleRefreshCache () {
  refreshCache().then(() => {
    proxy.$modal.msgSuccess(i18n.global.t('common.success'))
  })
}

getList()
</script>
