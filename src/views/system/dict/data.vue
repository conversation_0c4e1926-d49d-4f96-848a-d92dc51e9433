<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="130px">
         <el-form-item :label="$t('system.dictionaryName')" prop="dictType">
            <el-select v-model="queryParams.dictType">
               <el-option
                  v-for="item in typeOptions"
                  :key="item.dictId"
                  :label="item.dictName"
                  :value="item.dictType"
               />
            </el-select>
         </el-form-item>
         <el-form-item :label="$t('system.dictionaryLabel')" prop="dictLabel">
            <el-input
               v-model="queryParams.dictLabel"
               :placeholder="$t('common.pleaseEnter')"
               clearable
               @keyup.enter="handleQuery"
            />
         </el-form-item>
         <el-form-item :label="$t('common.state')" prop="status">
            <el-select v-model="queryParams.status" :placeholder="$t('common.pleaseEnter')" clearable>
               <el-option
                  v-for="dict in sys_normal_disable"
                  :key="dict.value"
                  :label="$t(dict.label)"
                  :value="dict.value"
               />
            </el-select>
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">{{ $t('common.search') }}</el-button>
            <el-button icon="Refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
         </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
         <el-col :span="1.5">
            <el-button
               type="primary"
               plain
               icon="Plus"
               @click="handleAdd"
               v-hasPermi="['system:dict:add']"
            >{{ $t('common.add') }}</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button
               type="success"
               plain
               icon="Edit"
               :disabled="single"
               @click="handleUpdate"
               v-hasPermi="['system:dict:edit']"
            >{{ $t('common.edit') }}</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button
               type="danger"
               plain
               icon="Delete"
               :disabled="multiple"
               @click="handleDelete"
               v-hasPermi="['system:dict:remove']"
            >{{ $t('common.delete') }}</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button
               type="warning"
               plain
               icon="Download"
               @click="handleExport"
               v-hasPermi="['system:dict:export']"
            >{{$t('common.export')}}</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button
               type="warning"
               plain
               icon="Close"
               @click="handleClose"
            >{{ $t('common.close') }}</el-button>
         </el-col>
         <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange">
         <el-table-column type="selection" width="55" align="center" />
         <el-table-column :label="$t('system.dictionaryNum')" align="center" prop="dictCode" />
         <el-table-column :label="$t('system.dictionaryLabel')" align="center" prop="dictLabel">
            <template #default="scope">
               <span v-if="scope.row.listClass == '' || scope.row.listClass == 'default'">{{ scope.row.dictLabel }}</span>
               <el-tag v-else :type="scope.row.listClass == 'primary' ? '' : scope.row.listClass">{{ scope.row.dictLabel }}</el-tag>
            </template>
         </el-table-column>
         <el-table-column :label="$t('system.dictionaryKey')" align="center" prop="dictValue" />
         <el-table-column :label="$t('system.dictionaryOrder')" align="center" prop="dictSort" />
         <el-table-column :label="$t('common.state')" align="center" prop="status">
            <template #default="scope">
               <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
            </template>
         </el-table-column>
         <el-table-column :label="$t('common.remark')" align="center" prop="remark" :show-overflow-tooltip="true" />
         <el-table-column :label="$t('common.createdTime')" align="center" prop="createTime" width="180">
            <template #default="scope">
               <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
         </el-table-column>
         <el-table-column :label="$t('common.operate')" align="center" width="150" class-name="small-padding fixed-width">
            <template #default="scope">
               <el-button
                  type="text"
                  icon="Edit"
                  @click="handleUpdate(scope.row)"
                  v-hasPermi="['system:dict:edit']"
               >{{ $t('common.edit') }}</el-button>
               <el-button
                  type="text"
                  icon="Delete"
                  @click="handleDelete(scope.row)"
                  v-hasPermi="['system:dict:remove']"
               >{{ $t('common.delete') }}</el-button>
            </template>
         </el-table-column>
      </el-table>

      <pagination
         v-show="total > 0"
         :total="total"
         v-model:page="queryParams.pageNum"
         v-model:limit="queryParams.pageSize"
         @pagination="getList"
      />

      <!-- 添加或修改参数配置对话框 -->
      <el-dialog :title="title" v-model="open" width="500px" append-to-body>
         <el-form ref="dataRef" :model="form" :rules="rules" label-width="130px">
            <el-form-item :label="$t('system.dictionaryType')" prop="dictType">
               <el-input v-model="form.dictType" :disabled="true" />
            </el-form-item>
            <el-form-item :label="$t('system.dataLabel')" prop="dictLabel">
               <el-input v-model="form.dictLabel" :placeholder="$t('common.pleaseEnter')" />
            </el-form-item>
            <el-form-item :label="$t('system.dataKeyValue')" prop="dictValue">
               <el-input v-model="form.dictValue" :placeholder="$t('common.pleaseEnter')" />
            </el-form-item>
            <el-form-item :label="$t('system.styleAttribute')" prop="cssClass">
               <el-input v-model="form.cssClass" :placeholder="$t('common.pleaseEnter')" />
            </el-form-item>
            <el-form-item :label="$t('system.displayOrder')" prop="dictSort">
               <el-input type="number" v-model="form.dictSort" controls-position="right" />
            </el-form-item>
            <el-form-item :label="$t('system.echoStyle')" prop="listClass">
               <el-select v-model="form.listClass">
                  <el-option
                     v-for="item in listClassOptions"
                     :key="item.value"
                     :label="item.label"
                     :value="item.value"
                  ></el-option>
               </el-select>
            </el-form-item>
            <el-form-item :label="$t('common.state')" prop="status">
               <el-radio-group v-model="form.status">
                  <el-radio
                     v-for="dict in sys_normal_disable"
                     :key="dict.value"
                     :label="dict.value"
                  >{{ $t(dict.label) }}</el-radio>
               </el-radio-group>
            </el-form-item>
            <el-form-item :label="$t('common.remark')" prop="remark">
               <el-input v-model="form.remark" type="textarea" :placeholder="$t('common.pleaseEnter')"></el-input>
            </el-form-item>
         </el-form>
         <template #footer>
            <div class="dialog-footer">
               <el-button type="primary" @click="submitForm">{{ $t('common.confirm') }}</el-button>
               <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
            </div>
         </template>
      </el-dialog>
   </div>
</template>

<script setup name="Data">
import { listType, getType } from '@/api/system/dict/type'
import { listData, getData, delData, addData, updateData } from '@/api/system/dict/data'
import i18n from "../../../i18n";

const { proxy } = getCurrentInstance()
const { sys_normal_disable } = proxy.useDict('sys_normal_disable')

const dataList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref('')
const defaultDictType = ref('')
const typeOptions = ref([])
const route = useRoute()
// 数据标签回显样式
const listClassOptions = ref([
  { value: 'default', label: i18n.global.t('system.default') },
  { value: 'primary', label: i18n.global.t('system.primary') },
  { value: 'success', label: i18n.global.t('system.success') },
  { value: 'info', label: i18n.global.t('system.info') },
  { value: 'warning', label: i18n.global.t('system.warning') },
  { value: 'danger', label: i18n.global.t('system.danger') }
])

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    dictName: undefined,
    dictType: undefined,
    status: undefined
  },
  rules: {
    dictSort: [{ required: true, message: i18n.global.t('common.cannotBeEmpty'), trigger: 'blur' },
      { min: 0, max: 3, message: i18n.global.t('common.numberExceeds') + '0,999', trigger: 'blur' }],
    dictType: [{ required: true, message: i18n.global.t('common.cannotBeEmpty'), trigger: 'blur' },
      { max: 100, message: i18n.global.t('common.charactersExceeds') + '100', trigger: 'blur' }],
    dictLabel: [{ required: true, message: i18n.global.t('common.cannotBeEmpty'), trigger: 'blur' },
      { max: 100, message: i18n.global.t('common.charactersExceeds') + '100', trigger: 'blur' }],
    dictValue: [{ required: true, message: i18n.global.t('common.cannotBeEmpty'), trigger: 'blur' },
      { max: 100, message: i18n.global.t('common.charactersExceeds') + '100', trigger: 'blur' }],
    cssClass: [ { max: 100, message: i18n.global.t('common.charactersExceeds') + '100', trigger: 'blur' }],
    remark: [{ max: 500, message: i18n.global.t('common.charactersExceeds') + '500', trigger: 'blur' }]
  }
})

const { queryParams, form, rules } = toRefs(data)

/** 查询字典类型详细 */
function getTypes (dictId) {
  getType(dictId).then(response => {
    queryParams.value.dictType = response.data.dictType
    defaultDictType.value = response.data.dictType
    getList()
  })
}

/** 查询字典类型列表 */
function getTypeList () {
  listType().then(response => {
    typeOptions.value = response.rows
  })
}
/** 查询字典数据列表 */
function getList () {
  loading.value = true
  listData(queryParams.value).then(response => {
    dataList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}
/** 取消按钮 */
function cancel () {
  open.value = false
  reset()
}
/** 表单重置 */
function reset () {
  form.value = {
    dictCode: undefined,
    dictLabel: undefined,
    dictValue: undefined,
    cssClass: undefined,
    listClass: 'default',
    dictSort: 0,
    status: '0',
    remark: undefined
  }
  proxy.resetForm('dataRef')
}
/** 搜索按钮操作 */
function handleQuery () {
  queryParams.value.pageNum = 1
  getList()
}
/** 返回按钮操作 */
function handleClose () {
  const obj = { path: '/system/dict' }
  proxy.$tab.closeOpenPage(obj)
}
/** 重置按钮操作 */
function resetQuery () {
  proxy.resetForm('queryRef')
  queryParams.value.dictType = defaultDictType
  handleQuery()
}
/** 新增按钮操作 */
function handleAdd () {
  reset()
  open.value = true
  title.value = i18n.global.t('common.add')
  form.value.dictType = queryParams.value.dictType
}
/** 多选框选中数据 */
function handleSelectionChange (selection) {
  ids.value = selection.map(item => item.dictCode)
  single.value = selection.length != 1
  multiple.value = !selection.length
}
/** 修改按钮操作 */
function handleUpdate (row) {
  reset()
  const dictCode = row.dictCode || ids.value
  getData(dictCode).then(response => {
    form.value = response.data
    form.value.dictSort = response.data.dictSort + ""
    open.value = true
    title.value = i18n.global.t('common.edit')
  })
}
/** 提交按钮 */
function submitForm () {
  proxy.$refs.dataRef.validate(valid => {
    if (valid) {
      if (form.value.dictCode != undefined) {
        updateData(form.value).then(response => {
          proxy.$modal.msgSuccess(i18n.global.t('common.success'))
          open.value = false
          getList()
        })
      } else {
        addData(form.value).then(response => {
          proxy.$modal.msgSuccess(i18n.global.t('common.success'))
          open.value = false
          getList()
        })
      }
    }
  })
}
/** 删除按钮操作 */
function handleDelete (row) {
  const dictCodes = row.dictCode || ids.value
  proxy.$modal.confirm(i18n.global.t('system.confirmDelete') + dictCodes ).then(function () {
    return delData(dictCodes)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess(i18n.global.t('common.success'))
  }).catch(() => {})
}
/** 导出按钮操作 */
function handleExport () {
  proxy.download('system/dict/data/export', {
    ...queryParams.value
  }, `dict_data_${new Date().getTime()}.xlsx`)
}

getTypes(route.params && route.params.dictId)
getTypeList()
</script>
