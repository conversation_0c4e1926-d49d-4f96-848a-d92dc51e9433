<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
         <el-form-item :label="$t('system.menuName')" prop="menuName">
            <el-input
               v-model="queryParams.menuName"
               :placeholder="$t('common.pleaseEnter')"
               clearable
               @keyup.enter="handleQuery"
            />
         </el-form-item>
         <el-form-item :label="$t('common.state')" prop="status">
            <el-select v-model="queryParams.status" :placeholder="$t('system.menuStatus')" clearable>
               <el-option
                  v-for="dict in sys_normal_disable"
                  :key="dict.value"
                  :label="$t(dict.label)"
                  :value="dict.value"
               />
            </el-select>
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">{{ $t('common.search') }}</el-button>
            <el-button icon="Refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
         </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
         <el-col :span="1.5">
            <el-button
               type="primary"
               plain
               icon="Plus"
               @click="handleAdd"
               v-hasPermi="['system:menu:add']"
            >{{ $t('common.add') }}</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button
               type="info"
               plain
               icon="Sort"
               @click="toggleExpandAll"
            >{{$t('system.expandCollapse')}}</el-button>
         </el-col>
         <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table
         v-if="refreshTable"
         v-loading="loading"
         :data="menuList"
         row-key="menuId"
         :default-expand-all="isExpandAll"
         :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      >
         <el-table-column prop="menuName" :label="$t('system.menuName')" :show-overflow-tooltip="true" width="160"></el-table-column>
         <el-table-column prop="icon" :label="$t('system.icon')" align="center" width="100">
            <template #default="scope">
               <svg-icon :icon-class="scope.row.icon" />
            </template>
         </el-table-column>
         <el-table-column prop="orderNum" :label="$t('system.sorting')" width="60"></el-table-column>
         <el-table-column prop="perms" :label="$t('system.permissionIdentifier')" :show-overflow-tooltip="true"></el-table-column>
         <el-table-column prop="component" :label="$t('system.componentPath')" :show-overflow-tooltip="true"></el-table-column>
         <el-table-column prop="status" :label="$t('common.state')" width="80">
            <template #default="scope">
               <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
            </template>
         </el-table-column>
         <el-table-column :label="$t('common.createdTime')" align="center" prop="createTime">
            <template #default="scope">
               <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
         </el-table-column>
         <el-table-column :label="$t('common.operate')" align="center" width="200" class-name="small-padding fixed-width">
            <template #default="scope">
               <el-button
                  type="text"
                  icon="Edit"
                  @click="handleUpdate(scope.row)"
                  v-hasPermi="['system:menu:edit']"
               >{{ $t('common.edit') }}</el-button>
               <el-button
                  type="text"
                  icon="Plus"
                  @click="handleAdd(scope.row)"
                  v-hasPermi="['system:menu:add']"
               >{{ $t('common.add') }}</el-button>
               <el-button
                  type="text"
                  icon="Delete"
                  @click="handleDelete(scope.row)"
                  v-hasPermi="['system:menu:remove']"
               >{{ $t('common.delete') }}</el-button>
            </template>
         </el-table-column>
      </el-table>

      <!-- 添加或修改菜单对话框 -->
      <el-dialog :title="title" v-model="open" width="900px" :before-close="handleClose" append-to-body>
         <el-form ref="menuRef" :model="form" :rules="rules" label-width="170px">
            <el-row>
               <el-col :span="24">
                  <el-form-item :label="$t('system.parentMenu')">
                     <tree-select
                        v-model:value="form.parentId"
                        :options="menuOptions"
                        :objMap="{ value: 'menuId', label: 'menuName', children: 'children' }"
                        :placeholder="$t('system.selectParentMenu')"
                     />
                  </el-form-item>
               </el-col>
               <el-col :span="24">
                  <el-form-item :label="$t('system.menuType')" prop="menuType">
                     <el-radio-group v-model="form.menuType">
                        <el-radio label="M">{{ $t('system.directory')}}</el-radio>
                        <el-radio label="C">{{ $t('system.menu')}}</el-radio>
                        <el-radio label="F">{{ $t('system.button')}}</el-radio>
                     </el-radio-group>
                  </el-form-item>
               </el-col>
               <el-col :span="24" v-if="form.menuType != 'F'">
                  <el-form-item :label="$t('system.menuIcon')" prop="icon">
                     <el-popover
                        placement="bottom-start"
                        :width="540"
                        v-model:visible="showChooseIcon"
                        trigger="click"
                        @show="showSelectIcon"
                     >
                        <template #reference>
                           <el-input v-model="form.icon" :placeholder="$t('system.clickToSelect')" @click="showSelectIcon" readonly>
                              <template #prefix>
                                 <svg-icon
                                    v-if="form.icon"
                                    :icon-class="form.icon"
                                    class="el-input__icon"
                                    style="height: 32px;width: 16px;"
                                 />
                                 <el-icon v-else style="height: 32px;width: 16px;"><search /></el-icon>
                              </template>
                           </el-input>
                        </template>
                        <icon-select ref="iconSelectRef" @selected="selected" />
                     </el-popover>
                  </el-form-item>
               </el-col>
               <el-col :span="12">
                  <el-form-item :label="$t('system.menuName')" prop="menuName">
                     <el-input v-model="form.menuName" :placeholder="$t('common.pleaseEnter')" />
                  </el-form-item>
               </el-col>
               <el-col :span="12">
                  <el-form-item :label="$t('system.displayOrder')" prop="orderNum">
                     <el-input type="number" v-model="form.orderNum" controls-position="right" />
                  </el-form-item>
               </el-col>
               <el-col :span="12" v-if="form.menuType != 'F'">
                  <el-form-item>
                     <template #label>
                        <span>
                           <el-tooltip content="选择是外链则路由地址需要以`http(s)://`开头" placement="top">
                              <i class="el-icon-question"></i>
                           </el-tooltip>{{ $t('system.isExternalLink')}}
                        </span>
                     </template>
                     <el-radio-group v-model="form.isFrame">
                        <el-radio label="0">{{ $t('common.yes')}}</el-radio>
                        <el-radio label="1">{{ $t('common.no')}}</el-radio>
                     </el-radio-group>
                  </el-form-item>
               </el-col>
               <el-col :span="12" v-if="form.menuType != 'F'">
                  <el-form-item prop="path">
                     <template #label>
                        <span>
                           <el-tooltip content="访问的路由地址，如：`user`，如外网地址需内链访问则以`http(s)://`开头" placement="top">
                              <i class="el-icon-question"></i>
                           </el-tooltip>
                           {{ $t('system.routeAddress')}}
                        </span>
                     </template>
                     <el-input v-model="form.path" :placeholder="$t('common.pleaseEnter')" />
                  </el-form-item>
               </el-col>
               <el-col :span="12" v-if="form.menuType == 'C'">
                  <el-form-item prop="component">
                     <template #label>
                        <span>
                           <el-tooltip content="访问的组件路径，如：`system/user/index`，默认在`views`目录下" placement="top">
                              <i class="el-icon-question"></i>
                           </el-tooltip>
                           {{ $t('system.componentPath')}}
                        </span>
                     </template>
                     <el-input v-model="form.component" :placeholder="$t('common.pleaseEnter')" />
                  </el-form-item>
               </el-col>
               <el-col :span="12" v-if="form.menuType != 'M'">
                  <el-form-item prop="perms">
                     <el-input v-model="form.perms" :placeholder="$t('common.pleaseEnter')" />
                     <template #label>
                        <span>
                           <el-tooltip content="控制器中定义的权限字符，如：@PreAuthorize(`@ss.hasPermi('system:user:list')`)" placement="top">
                              <i class="el-icon-question"></i>
                           </el-tooltip>
                           {{ $t('system.permissionCharacter')}}
                        </span>
                     </template>
                  </el-form-item>
               </el-col>
               <el-col :span="12" v-if="form.menuType == 'C'">
                  <el-form-item prop="query">
                     <el-input v-model="form.query" :placeholder="$t('common.pleaseEnter')" />
                     <template #label>
                        <span>
                           <el-tooltip content='访问路由的默认传递参数，如：`{"id": 1, "name": "ry"}`' placement="top">
                              <i class="el-icon-question"></i>
                           </el-tooltip>
                           {{ $t('system.routeParameters')}}
                        </span>
                     </template>
                  </el-form-item>
               </el-col>
               <el-col :span="12" v-if="form.menuType == 'C'">
                  <el-form-item>
                     <template #label>
                        <span>
                           <el-tooltip content="选择是则会被`keep-alive`缓存，需要匹配组件的`name`和地址保持一致" placement="top">
                              <i class="el-icon-question"></i>
                           </el-tooltip>
                           {{ $t('system.isCachingEnabled')}}
                        </span>
                     </template>
                     <el-radio-group v-model="form.isCache">
                        <el-radio label="0">{{ $t('system.cache') }}</el-radio>
                        <el-radio label="1">{{ $t('system.noCache') }}</el-radio>
                     </el-radio-group>
                  </el-form-item>
               </el-col>
               <el-col :span="12" v-if="form.menuType != 'F'">
                  <el-form-item>
                     <template #label>
                        <span>
                           <el-tooltip content="选择隐藏则路由将不会出现在侧边栏，但仍然可以访问" placement="top">
                              <i class="el-icon-question"></i>
                           </el-tooltip>
                           {{ $t('system.displayStatus')}}
                        </span>
                     </template>
                     <el-radio-group v-model="form.visible">
                        <el-radio
                           v-for="dict in sys_show_hide"
                           :key="dict.value"
                           :label="dict.value"
                        >{{ $t(dict.label) }}</el-radio>
                     </el-radio-group>
                  </el-form-item>
               </el-col>
               <el-col :span="12" v-if="form.menuType != 'F'">
                  <el-form-item>
                     <template #label>
                        <span>
                           <el-tooltip content="选择停用则路由将不会出现在侧边栏，也不能被访问" placement="top">
                              <i class="el-icon-question"></i>
                           </el-tooltip>
                           {{ $t('system.menuStatus')}}
                        </span>
                     </template>
                     <el-radio-group v-model="form.status">
                        <el-radio
                           v-for="dict in sys_normal_disable"
                           :key="dict.value"
                           :label="dict.value"
                        >{{ $t(dict.label) }}</el-radio>
                     </el-radio-group>
                  </el-form-item>
               </el-col>
            </el-row>
         </el-form>
         <template #footer>
            <div class="dialog-footer">
               <el-button type="primary" @click="submitForm">{{ $t('common.confirm') }}</el-button>
               <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
            </div>
         </template>
      </el-dialog>
   </div>
</template>

<script setup name="Menu">
import { addMenu, delMenu, getMenu, listMenu, updateMenu } from '@/api/system/menu'
import SvgIcon from '@/components/SvgIcon'
import IconSelect from '@/components/IconSelect'
import i18n from "../../../i18n";

const { proxy } = getCurrentInstance()
const { sys_show_hide, sys_normal_disable } = proxy.useDict('sys_show_hide', 'sys_normal_disable')

const menuList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const title = ref('')
const menuOptions = ref([])
const isExpandAll = ref(false)
const refreshTable = ref(true)
const showChooseIcon = ref(false)
const iconSelectRef = ref(null)

const data = reactive({
  form: {},
  queryParams: {
    menuName: undefined,
    visible: undefined
  },
  rules: {
    menuName: [{ required: true, message: i18n.global.t('common.cannotBeEmpty'), trigger: 'blur' },
      { max: 50, message: i18n.global.t('common.charactersExceeds') + '50', trigger: 'blur' }],
    orderNum: [{ required: true, message: i18n.global.t('common.cannotBeEmpty'), trigger: 'blur' },
      { min: 0, max: 3, message: i18n.global.t('common.numberExceeds') + '0,999', trigger: 'blur' }],
    path: [{ required: true, message: i18n.global.t('common.cannotBeEmpty'), trigger: 'blur' },
      { max: 200, message: i18n.global.t('common.charactersExceeds') + '200', trigger: 'blur' }],
    component: [{ max: 200, message: i18n.global.t('common.charactersExceeds') + '200', trigger: 'blur' }],
    perms: [{ max: 100, message: i18n.global.t('common.charactersExceeds') + '100', trigger: 'blur' }],
    query: [{ max: 200, message: i18n.global.t('common.charactersExceeds') + '200', trigger: 'blur' }]
  }
})

const { queryParams, form, rules } = toRefs(data)

/** 查询菜单列表 */
function getList () {
  loading.value = true
  listMenu(queryParams.value).then(response => {
    menuList.value = proxy.handleTree(response.data, 'menuId')
    loading.value = false
  })
}
/** 查询菜单下拉树结构 */
async function getTreeselect () {
  menuOptions.value = []
  await listMenu().then(response => {
    const menu = { menuId: 0, menuName: i18n.global.t('system.mainCategory'), children: [] }
    menu.children = proxy.handleTree(response.data, 'menuId')
    menuOptions.value.push(menu)
  })
}
/** 取消按钮 */
function cancel () {
  showChooseIcon.value = false
  open.value = false
  reset()
}
/** 表单重置 */
function reset () {
  form.value = {
    menuId: undefined,
    parentId: 0,
    menuName: undefined,
    icon: undefined,
    menuType: 'M',
    orderNum: undefined,
    isFrame: '1',
    isCache: '0',
    visible: '0',
    status: '0'
  }
  proxy.resetForm('menuRef')
}
/** 展示下拉图标 */
function showSelectIcon () {
  iconSelectRef.value.reset()
  showChooseIcon.value = true
}
/** 选择图标 */
function selected (name) {
  form.value.icon = name
  showChooseIcon.value = false
}
/** 关闭弹窗隐藏图标选择 */
function handleClose () {
  cancel()
  showChooseIcon.value = false
}
/** 搜索按钮操作 */
function handleQuery () {
  getList()
}
/** 重置按钮操作 */
function resetQuery () {
  proxy.resetForm('queryRef')
  handleQuery()
}
/** 新增按钮操作 */
async function handleAdd (row) {
  reset()
  await getTreeselect()
  if (row != null && row.menuId) {
    form.value.parentId = row.menuId
  } else {
    form.value.parentId = 0
  }
  open.value = true
  title.value = i18n.global.t('common.add')
}
/** 展开/折叠操作 */
function toggleExpandAll () {
  refreshTable.value = false
  isExpandAll.value = !isExpandAll.value
  nextTick(() => {
    refreshTable.value = true
  })
}
/** 修改按钮操作 */
async function handleUpdate (row) {
  reset()
  await getTreeselect()
  getMenu(row.menuId).then(response => {
    form.value = response.data
    open.value = true
    title.value = i18n.global.t('common.edit')
  })
}
/** 提交按钮 */
function submitForm () {
  showChooseIcon.value = false
  proxy.$refs.menuRef.validate(valid => {
    if (valid) {
      if (form.value.menuId != undefined) {
        updateMenu(form.value).then(response => {
          proxy.$modal.msgSuccess(i18n.global.t('common.success'))
          open.value = false
          getList()
        })
      } else {
        addMenu(form.value).then(response => {
          proxy.$modal.msgSuccess(i18n.global.t('common.success'))
          open.value = false
          getList()
        })
      }
    }
  })
}
/** 删除按钮操作 */
function handleDelete (row) {
  proxy.$modal.confirm(i18n.global.t('system.confirmDelete') + row.menuName ).then(function () {
    return delMenu(row.menuId)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess(i18n.global.t('common.success'))
  }).catch(() => {})
}

getList()
</script>
