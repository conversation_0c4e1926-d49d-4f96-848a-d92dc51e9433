<template>
  <el-form ref="formRef" :model="formParams" :rules="rules" label-width="100px">
    <el-row v-for="field in fields" :key="field.key" :gutter="20">
      <el-col :span="12">
        <el-form-item label-width="200px" :label="field.label" :prop="field.key">
          <el-input v-if="field.type === 'textarea'" v-model="formParams[field.key]" v-bind="field.props" :type="field.type" />

          <el-select v-if="field.type === 'select'" v-model="formParams[field.key]" v-bind="field.selectProps" >
            <el-option
              v-for="(item, index) in field.options"
              :key="index"
              :label="item.label"
              :value="item"
            />
          </el-select>

          <el-checkbox-group v-if="field.type === 'checkboxes'" v-model="formParams[field.key]">
            <el-checkbox
              v-for="(item, index) in formatObject(formObject.beReportClientIds)"
              style="display: block;"
              :key="index" :label="item"
            />
          </el-checkbox-group>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20" justify="end">
      <el-col class="right" :span="2">
        <slot />
      </el-col>
      <el-col :span="2">
        <el-button type="primary" @click="handleSubmit">{{ $t('common.submit') }}</el-button>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup>
import { computed, watch, ref, getCurrentInstance } from 'vue'
import { fields as formFields, rules, sources, types } from './instances'

const { proxy } = getCurrentInstance()

const props = defineProps({ formObject: Object })
const emit = defineEmits(['submitForm', 'update:formParams'])

const formRef = ref()
const fields = ref(formFields)
const formParams = computed(() => props.formObject)

function formatObject (string = '') {
  return string ? string.split(',') : []
}

function handleSubmit () {
  proxy.$refs.formRef.validate(async valid => {
    if (valid) {
      const { targetId, expirationTime, remark, conversationType, reportType, id } = formParams.value
      emit('submitForm', {
        targetId: targetId.map(id => Number(id?.split('(')[0])),
        expirationTime: expirationTime.value,
        days: expirationTime.code,
        remark,
        reportId: id,
        targetType: sources.find(source => source.value === conversationType)?.code || null,
        freezeReason: types.find(type => type.value === reportType)?.code || null
      })
    }
  })
}

watch(() => props.formObject, () => {
  proxy.$refs.formRef.resetFields()
})

</script>

<style scoped lang="scss">
  .right {
    display: flex;
    justify-content: flex-end;
  }
</style>
