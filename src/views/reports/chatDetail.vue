<template>
  <div v-for="(item, index) in detail.reportDetail" :key="item.msgId" className="item">
    <el-row :gutter="20">
      <el-col :offset="1" :span="1">
        <el-avatar :icon="UserFilled" :src="item.senderHeadPortrait"/>
      </el-col>

      <el-col :span="18">
        <p className="text-grey">{{ item.senderName }} ({{ item.sender }})</p>

        <div v-if="item?.attrs">
          <p v-if="item?.attrs?.fromConvChatType === '7'" className="channel">转发自 [{{
              item?.attrs?.fromConvShowName
            }}频道]</p>

          <span v-if="item?.attrs?.replyMsgId" className="attrs-content">
            {{ item?.attrs?.replyUserName }}
            <br/> <br/>
            {{ item?.attrs?.replyMsgContent }}
          </span>

          <span v-if="item?.attrs?.snapshotUrl" className="attrs-content">
            <p>{{ item?.attrs?.addressDetail }}</p>
            <el-image
              style="width: 250px; height: 200px; padding: 3px;"
              :src="item?.attrs?.snapshotUrl"
            />
          </span>

          <span v-if="item?.attrs?.userId" className="attrs-content">
            <el-row :gutter="10" align="center">
              <el-col :offset="1" :span="6">
                <el-image
                  style="width: 100%; height: 100%; border-radius: 50%; text-align: center;"
                  :src="item?.attrs?.avatar"
                />
              </el-col>
              <el-col :span="12" style="padding-top: 5px;">
                <p>{{ item?.attrs?.userName }}</p>
                <p>{{ item?.attrs?.dailCode }} {{ item?.attrs?.phone }}</p>
              </el-col>
            </el-row>
          </span>
          <!-- attrs content -->
          <span v-if="item?.attrs?.recordContent" className="attrs-content">
            <p>聊天记录 {{ item?.attrs?.recordSourceConvName }}</p>
            <p style="margin-top: 5px;" v-for="(content, index) in JSON.parse(item?.attrs?.recordContent)" :key="index">
              <!-- {{ content?.name }}: {{ content?.content }} -->
              <span>{{ content?.name }}: {{ getMessageContent(content.msgType, content) }}</span>
            </p>
          </span>

          <el-image
            v-if="item?.attrs?.faceMd5"
            :style="{width: `${item?.attrs?.faceWidth}px`, height: `${item?.attrs?.faceHeight}px`, padding: '3px'}"
            :src="md5Url(item.attrs)"
          />
        </div>

        <div v-show="item.urlList" className="block">
          <div v-if="item.type === 'IMAGE'">
            <el-image
              style="width: 150px; height: 100px; padding: 3px;"
              v-for="(image, index) in item.urlList" :key="index"
              :src="image"
              :zoom-rate="1.2"
              :preview-src-list="item.urlList"
              :initial-index="index"
              fit="cover"
            />
          </div>

          <div v-if="item.type === 'VOICE'">
            <audio
              v-for="(audio, index) in item.urlList"
              controls :key="index" :src="audio"
              style="padding-top: 5px; padding-bottom: 5px;"
            />
          </div>

          <div v-if="item.type === 'VIDEO'">
            <video
              v-for="(video, index) in item.urlList"
              :src="video" :key="index"
              style="padding-top: 5px; padding-bottom: 5px;"
              crossorigin="anonymous"
              width="300" height="150" controls
            />
          </div>

          <div v-if="item.type === 'FILE'">
            <el-badge v-for="(file, index) in item.urlList" :key="index" style="padding: 5px;">
              <p @click="downloadFile(file)" class="file-style">{{ getFileName(file) }}</p>
              <!-- <el-button @click="downloadFile(file)">{{ getFileName(file) }}</el-button> -->
            </el-badge>
          </div>
        </div>
        <p className="content">{{ item.content }}</p>
      </el-col>

      <el-col :offset="1" :span="2" style="text-align: end;">
        <p className="date">
          {{ item.createTime.split('T')[0] }}
        </p>
        <p className="text-grey">
          {{ new Date(item.createTime).getHours() }}:{{ new Date(item.createTime).getMinutes() }}
        </p>
      </el-col>
    </el-row>

    <el-row>
      <el-col :offset="2" :span="21">
        <el-divider v-show="index !== (detail.reportDetail.length - 1)"/>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import {UserFilled} from '@element-plus/icons-vue'

const props = defineProps({data: Object})
const emit = defineEmits(['handleClose'])
const detail = computed(() => props.data)

function md5Url(attrs) {
  if (!attrs) return ''
  // const domain = 'https://oss-test.metathought.cc'
  const host = import.meta.env.VITE_APP_MINIO_ENDPOINT
  const bucket = 'mos-phiz'
  const extension = attrs.faceType
  const last6Chars = attrs.faceMd5.slice(-6)
  const code1 = last6Chars.slice(-3)
  const code2 = last6Chars.slice(0, 3)
  return host + '/' + bucket + '/' + code1 + '/' + code2 + '/' + attrs.faceMd5 + '.' + extension
}

function getMessageContent(msgType, content) {
  if (msgType === -1) {
    return content?.content;
  } else if (msgType === -2) {
    return "图片消息";
  } else if (msgType === -3) {
    return "语音消息";
  } else if (msgType === -4) {
    return "视频消息";
  } else if (msgType === -5) {
    return "文件消息";
  } else if (msgType === 11) {
    return "名片";
  } else if (msgType === 12) {
    return "定位";
  } else if (msgType === 13) {
    return "回复";
  } else if (msgType === 14) {
    return "合并转发";
  } else if (msgType === 15) {
    return "自定义表情";
  } else if (msgType === 16) {
    return "频道邀请链接";
  } else if (msgType === 1000) {
    return "音视频通话";
  } else {
    return "";
  }
}

function getFileName(file, withExtension = false) {
  if (!file) return ''
  const filePathes = file.split('/')
  let fileName = filePathes[filePathes.length - 1]
  if (!fileName) return 'empty'
  fileName = withExtension ? fileName : fileName.split('.')
  // fileName = withExtension ? fileName : fileName.split('.')[0].substr(0, 30) + '...'
  return decodeURI(fileName)
}

function downloadFile(url) {
  const x = new window.XMLHttpRequest()
  x.open('GET', url, true)
  x.responseType = 'blob'
  x.onload = () => {
    const content = window.URL.createObjectURL(x.response)
    const a = document.createElement('a')
    a.href = content
    a.download = getFileName(url, true)
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    window.URL.revokeObjectURL(content)
  }
  x.send()
}
</script>

<style scoped lang="scss">
.item {
  padding-bottom: 20px;
}

.text-grey {
  color: grey;
  font-size: small;
  padding-bottom: 6px;
}

.channel {
  color: #409EFF;
  font-size: small;
  padding-bottom: 6px;
}

.content {
  padding-top: 6px;
  color: black;
  font-size: medium;
}

.file {
  width: 150px;
  height: 100px;
  padding-top: 3px;
  padding-bottom: 3px;
}

.date {
  padding-bottom: 5px;
}

.attrs-content {
  padding: 5px;
  margin-top: 5px;
  margin-bottom: 5px;
  border: 1px solid #f4f4f5;
  border-radius: 10px;
  border-radius: 10px;
  background-color: #f4f4f5;
  display: inline-block;
}

.file-style {
  color: #606266;
  border: 1px solid #dcdfe6;
  padding: 8px 15px;
  border-radius: 5px;
  cursor: pointer;
  box-sizing: border-box;
  transition: .1s;

  &:hover {
    color: #409eff;
    background: #ecf5ff;
  }
}
</style>

