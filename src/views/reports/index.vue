<template>
  <div class="app-container">
    <QueryForm :queryParams="queryParams" @getList="getList" />

    <Table :state="state" @handleDialog="handleDialog" @handleNeglect="handleNeglect" />

    <pagination
      v-show="state.total > 0"
      :total="state.total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="({ page, limit, ...rest }) => getList({ ...rest, ...queryParams, pageNum: page, pageSize: limit })"
    />

    <el-dialog :title="state.dialogTitle === '处理' ? i18n.global.t('common.dealWith'):(state.dialogTitle === '举报详情'?i18n.global.t('user.reportDetails'):i18n.global.t('common.details'))"
    v-if="state.dialogVisible"
    v-model="state.dialogVisible" width="80%"
    :before-close="handleClose" append-to-body>
      <Form v-if="state.dialogTitle === '处理'" :formObject="data" @submitForm="submitForm" >
        <el-button @click="handleClose">{{state.dialogTitle === '详情' ? '返 回' : i18n.global.t('common.cancel')}}</el-button>
      </Form>

      <Detail v-if="state.dialogTitle === '详情' " :data="data" @handleClose="handleClose" />

      <!-- <ChatDetail v-if="state.dialogTitle === '举报详情'" :data="data" @handleClose="handleClose" /> -->

      <History :recordList="data" v-if="state.dialogTitle === '举报详情'"/>
    </el-dialog>
  </div>
</template>

<script setup>
import { reactive, ref, getCurrentInstance } from 'vue'
import { listData, createData, getDataDetail, ignoreData, getData } from '@/api/reports'
import QueryForm from './queryForm'
import Table from './table'
import Form from './form'
import Detail from './detail'
import ChatDetail from './chatDetail'
import History from '@/views/assistant/components/History.vue'
import { formatDate } from '@/utils/index'
import i18n from "../../i18n";

const { proxy } = getCurrentInstance()

const state = reactive({
  tableData: [],
  getListLoading: true,
  total: 0,
  dialogVisible: false,
  dialogTitle: ''
})

const data = ref()

const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  fromDate: null,
  toDate: null,
  reportType: null,
  status: null,
  conversationType: null
})

function getList (params) {
  state.getListLoading = true
  listData(params)
    .then((res) => {
      state.tableData = res.data.records
      state.total = res.data.total
    })
    .finally(() => (state.getListLoading = false))
}

async function handleDialog (row, method) {
  if (method === 'process') {
    data.value = row
  } else if (method === 'chatDetail') {
    const res = await getDataDetail(row.id)
    const { reportDetail } = res.data
    // data.value = { ...res.data, reportDetail: res.data.reportDetail.map(detail => !!detail?.attrs ? { ...detail, attrs: JSON.parse(detail.attrs) } : detail) }
    data.value = reportDetail.map(i => {
      return {
        headPortrait: i.senderHeadPortrait,
        fromUserName: i.senderName,
        fromUserId: i.sender,
        createTime: formatDate(i.createTime),
        content: i.content,
        type: i.type,
        contentJson: {
          ...i,
          type: i.type,
          isReport: true,
          attrs: JSON.parse(i.attrs),
          files: i.urlList?.length ? i.urlList.map(i => { return { url: i } }) : [{ url: '' }]
        }
      }
    })
    console.log(data.value)
  } else {
    const res = await getData(row.id)
    res.data.createTime= formatDate(res.data.createTime)
    res.data.handleTime= formatDate(res.data.handleTime)
    data.value = res.data
  }
  state.dialogTitle = method === 'process' ? '处理' : method === 'chatDetail' ? '举报详情' : '详情'
  state.dialogVisible = !state.dialogVisible
}

function handleClose () {
  state.dialogVisible = false
}

function handleNeglect (row) {
  proxy.$modal.confirm(i18n.global.t('user.sureToIgnore'))
    .then(async function () {
      await ignoreData(row)
      getList()
    }).catch(() => {})
}

function submitForm (values) {
  createData(values).then((res) => {
    proxy.$modal.msgSuccess(i18n.global.t('common.success'))
    state.dialogVisible = false
    getList()
  })
}

getList()
</script>

<style scoped lang="scss">
    .title {
      margin-bottom: 20px;
      font-size: 16px;
      font-weight: bold;
    }
</style>
