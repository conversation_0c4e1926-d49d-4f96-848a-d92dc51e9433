<template>
  <el-form :model="formQueryParams" ref="queryFormRef" :inline="true">
    <el-form-item v-for="field in queryFields" :key="field.key" :prop="field.key" :label="field.label">
      <el-select v-model="formQueryParams[field.key]" v-bind="field.selectProps" >
        <el-option
          v-for="(item, index) in field.options"
          :key="index"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>

    <el-form-item>
      <el-button type="primary" icon="Search" @click="handleQuery">{{ $t('common.search') }}</el-button>
      <el-button icon="Refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup>
import { ref } from 'vue'
import { queryFields } from './instances'
const { proxy } = getCurrentInstance()

const queryFormRef = ref()
const formQueryParams = computed({
  get: () => props.queryParams,
  set: val => context.emit('update:queryParams', val)
})

const props = defineProps({ queryParams: Object })
const emit = defineEmits(['getList', 'update:queryParams'])

function handleQuery () {
  formQueryParams.value.pageNum = 1
  formQueryParams.value.fromDate = formQueryParams.value.date ? +new Date(formQueryParams.value.date.split(',')[0]) : null
  formQueryParams.value.toDate = formQueryParams.value.date ? +new Date(formQueryParams.value.date.split(',')[1]) : null
  emit('getList', formQueryParams.value)
}

function resetQuery () {
  formQueryParams.value.fromDate = null
  formQueryParams.value.toDate = null
  formQueryParams.value.date = null
  formQueryParams.value.reportType = null
  formQueryParams.value.status = null
  formQueryParams.value.conversationType = null
  proxy.resetForm('queryFormRef')
  emit('getList')
}
</script>
