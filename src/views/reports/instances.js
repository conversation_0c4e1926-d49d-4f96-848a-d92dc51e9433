// Utils
import i18n from "../../i18n";

const destructDate = date => {
  const newDate = new Date(date)
  const year = newDate.getFullYear()
  const day = newDate.getDate()
  const month = newDate.getMonth() + 1
  const hour = newDate.getHours()
  const min = newDate.getMinutes()
  return { year, day, month, hour, min }
}

const formatDate = (number, type, dateOnly = false) => {
  const currentDate = new Date()
  if (type === 'day') {
    currentDate.setDate(currentDate.getDate() + number)
  } else if (type === 'hour') {
    currentDate.setHours(currentDate.getHours() + number)
  } else {
    currentDate.setFullYear(currentDate.getFullYear() + number)
  }
  const { year, day, month, hour, min } = destructDate(currentDate)
  return dateOnly ? `${year}-${month}-${day}` : `${year}-${month}-${day} ${hour}:${min}:00`
}

const getFirstLastOf = (type) => {
  const currentDate = new Date()
  const fromDate = type === 'week'
    ? new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate() - 7)
    : new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, currentDate.getDate())
  const toDate = new Date()
  const { year, day, month } = destructDate(fromDate)
  const { year: year2, day: day2, month: month2 } = destructDate(toDate)
  return `${year}-${month}-${day} 00:01:00 , ${year2}-${month2}-${day2} 23:59:00`
}

// Form Rules
export const rules = {
  expirationTime: [{ required: true, message: i18n.global.t('common.cannotBeEmpty'), trigger: 'change' }],
  targetId: [{ required: true, message: i18n.global.t('common.cannotBeEmpty'), trigger: 'blur' }]
}

// Options
const dates = [
  { label: i18n.global.t('user.sameDay'), value: `${formatDate(0, 'day', true)} 00:01:00, ${formatDate(0, 'day', true)} 23:59:00` },
  { label: i18n.global.t('user.yesterday'), value: `${formatDate(-1, 'day', true)} 00:01:00, ${formatDate(-1, 'day', true)} 23:59:00` },
  { label: i18n.global.t('user.servenDays'), value: getFirstLastOf('week') },
  { label: i18n.global.t('user.oneMonth'), value: getFirstLastOf('month') }
]

export const sources = [
  { label: i18n.global.t('user.user'), value: 'USER', code: 1 },
  { label: i18n.global.t('user.group'), value: 'GROUP', code: 2 },
  { label: i18n.global.t('user.channel'), value: 'CHANNEL', code: 3 },
  { label: i18n.global.t('user.customer'), value: 'CUSTOMER', code: 4 },
  { label: i18n.global.t('user.moment'), value: 'MOMENT', code: 5}
]

export const types = [
  { label: i18n.global.t('userManage.harassmentAndBullying'), value: 'HARASS', code: 1 },
  { label: i18n.global.t('userManage.inappropriateRemarks'), value: 'INAPPROPRIATE', code: 2 },
  { label: i18n.global.t('userManage.violationOfRegulation'), value: 'BREACH', code: 3 },
  { label: i18n.global.t('userManage.pornographicViolence'), value: 'PORNO_VIOLENCE', code: 4 },
  { label: i18n.global.t('userManage.other'), value: 'OTHER', code: 5 }
]

export const statuses = [
  { label: i18n.global.t('user.processed'), value: 'PROCESSED', code: 1 },
  { label: i18n.global.t('user.ignored'), value: 'IGNORED', code: 2 },
  { label: i18n.global.t('user.pending'), value: 'PENDING', code: 3 }
]

export const freezeTimes = [
  { label: i18n.global.t('userManage.threeHours'), value: formatDate(3, 'hour'), code: 1 },
  { label: i18n.global.t('userManage.twelveHours'), value: formatDate(12, 'hour'), code: 2 },
  { label: i18n.global.t('userManage.oneDay'), value: formatDate(1, 'day'), code: 3 },
  { label: i18n.global.t('userManage.threeDays'), value: formatDate(3, 'day'), code: 4 },
  { label: i18n.global.t('userManage.oneWeek'), value: formatDate(7, 'day'), code: 5 },
  { label: i18n.global.t('userManage.permanent'), value: formatDate(999), code: 6 }
]

// Form Fields
export const fields = [
  {
    label: i18n.global.t('user.reportedTarget'),
    key: 'targetId',
    type: 'checkboxes'
  },
  {
    label: i18n.global.t('user.frozenTime'),
    key: 'expirationTime',
    type: 'select',
    options: freezeTimes,
    selectProps: { style: 'width: 100%', placeholder: i18n.global.t('common.select') }
  },
  {
    label: i18n.global.t('common.remark'),
    key: 'remark',
    type: 'textarea',
    props: { maxlength: '300', placeholder: i18n.global.t('common.pleaseEnter'), row: '3', showWordLimit: true }
  }
]

export const queryFields = [
  {
    label: i18n.global.t('user.date'),
    key: 'date',
    type: 'select',
    options: dates,
    selectProps: { style: 'width: 100%', placeholder: i18n.global.t('common.select') }
  },
  {
    label: i18n.global.t('user.reportFrom'),
    key: 'conversationType',
    type: 'select',
    options: sources,
    selectProps: { style: 'width: 100%', placeholder: i18n.global.t('common.select') }
  },
  {
    label: i18n.global.t('user.reportType'),
    key: 'reportType',
    type: 'select',
    options: types,
    selectProps: { style: 'width: 100%', placeholder: i18n.global.t('common.select') }
  },
  {
    label: i18n.global.t('user.processingState'),
    key: 'status',
    type: 'select',
    options: statuses,
    selectProps: { style: 'width: 100%', placeholder: i18n.global.t('common.select') }
  }
]
