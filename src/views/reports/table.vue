<template>
  <el-table v-loading="state.getListLoading" :data="state.tableData">

    <el-table-column :label="$t('user.reportUser')" prop="reporterClientId" align="center" />
    <el-table-column :label="$t('user.reportFrom')" prop="conversationType" align="center">
      <template #default="scope">
        <p>{{ getLabel(sources, scope.row.conversationType) }}</p>
      </template>
    </el-table-column>

    <el-table-column :label="$t('user.reportedTarget')" prop="beReportClientIds" align="center">
      <template #default="scope">
        {{ limitWordCounts(scope.row.beReportClientIds, 'arrayString', 20) }}
      </template>
    </el-table-column>
    <el-table-column :label="$t('user.reportType')" prop="reportType" align="center">
      <template #default="scope">
        <p>{{ getLabel(types, scope.row.reportType) }}</p>
      </template>
    </el-table-column>

    <el-table-column :label="$t('user.reportDescription')" prop="reportRemark" align="center">
      <template #default="scope">
        {{ limitWordCounts(scope.row.reportRemark, 'string') }}
      </template>
    </el-table-column>
    <el-table-column :label="$t('user.reportDetails')" prop="detail" align="center">
      <template #default="scope">
        <el-button type="text" @click="emit('handleDialog', scope.row, 'chatDetail')">{{ $t('user.reportDetails') }}</el-button>
      </template>
    </el-table-column>

    <el-table-column :label="$t('common.state')" prop="status" align="center">
      <template #default="scope">
        <p :style="{color: statusClasses[scope.row.status]}">{{ getLabel(statuses, scope.row.status) }}</p>
      </template>
    </el-table-column>

    <el-table-column :label="$t('common.createdTime')" prop="createTime" align="center">
        <template #default="scope">
          <span>{{  parseTime(scope.row.createTime)}}</span>
        </template>
    </el-table-column>

    <el-table-column :label="$t('common.operate')" align="center" class-name="small-padding fixed-width">
      <template #default="scope">
        <el-button type="text" @click="emit('handleDialog', scope.row, 'detail')">{{ $t('common.details') }}</el-button>
        <el-button :disabled="isDisable(scope.row)" type="text" @click="emit('handleDialog', scope.row, 'process')">{{ $t('common.dealWith') }}</el-button>
        <el-button :disabled="isDisable(scope.row)" type="text" @click="emit('handleNeglect', scope.row)">{{ $t('user.ignore') }}</el-button>
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup>
import { sources, types, statuses } from './instances'
const props = defineProps({ state: Object })
const emit = defineEmits(['handleDialog', 'handleNeglect'])
const statusClasses = { pending: '#409EFF', solved: '#606266', ignored: '#F56C6C' }
const getLabel = (array, value) => array.find(element => (element.value === value) || (element.code === value))?.label || ''
const isDisable = ({ status }) => status ? status !== 'PENDING' : false

function limitWordCounts (string = '', type, count = 10) {
  if (!string) return ''
  if (type === 'arrayString') {
    const ids = string?.split(',')
    const idsString = ids.slice(0, 1).join(' , ')
    return idsString.slice(0, count) + (idsString?.length > count ? '...' : '')
  } else {
    const zhRegExp = /[\u3040-\u30ff\u3400-\u4dbf\u4e00-\u9fff\uf900-\ufaff\uff66-\uff9f]/g
    if (zhRegExp.test(string)) {
      return string?.slice(0, count) + (string?.length > count ? '...' : '')
    } else {
      const arrayString = string?.split(' ')
      return arrayString?.length > 1
        ? string?.split(' ').slice(0, count).join(' ') + (string?.split(' ')?.length > count ? '...' : '')
        : string?.slice(0, count) + (string?.length > count ? '...' : '')
    }
  }
}

</script>
