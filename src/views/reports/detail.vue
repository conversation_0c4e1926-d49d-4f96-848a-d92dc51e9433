<template>
  <div>
    <el-row class="mh" v-for="dataKey in dataKeys" :key="dataKey" :gutter="20">
      <el-col class="text-center" v-show="translations[dataKey]" :span="4">{{
        translations[dataKey]
      }}</el-col>
      <el-col v-show="translations[dataKey]" :span="20">
        <template v-if="dataKey === 'days'">{{ $t("common.freeze") }}</template>
        {{
          getLabel(
            dataKey === "conversationType"
              ? sources
              : dataKey === "reportType"
              ? types
              : dataKey === "days"
              ? freezeTimes
              : statuses,
            data[dataKey]
          ) || data[dataKey]
        }}
      </el-col>
    </el-row>

    <el-row :gutter="20" justify="end">
      <el-col :span="2">
        <el-button @click="emit('handleClose')">{{
          $t("common.return")
        }}</el-button>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { types, sources, statuses, freezeTimes } from "./instances";
import i18n from "../../i18n";
let translations = {
  reporterClientId: i18n.global.t("user.reportUser"),
  conversationType: i18n.global.t("user.reportFrom"),
  beReportClientIds: i18n.global.t("user.reportedTarget"),
  reportType: i18n.global.t("user.reportType"),
  reportRemark: i18n.global.t("user.reportDescription"),
  status: i18n.global.t("common.state"),
  createTime: i18n.global.t("common.createdTime"),
  handleRemark: i18n.global.t("common.remark")
};
const props = defineProps({ data: Object });
const emit = defineEmits(["handleClose"]);
if (props.data.status === 'PROCESSED') {
  translations = {
    ...translations,
    handleTime: i18n.global.t("common.handleTime"),
    handleClientId: i18n.global.t("common.handleClientId"),
    days: i18n.global.t("common.days"),
  }
}
const dataKeys = Object.keys(props.data).filter((key) => !!translations[key]);
const getLabel = (array, value) => {
  console.log('array', array);
  console.log('value', value);
  return array.find((item) => item.value === value || item.code === value)?.label;
}
</script>

<style lang='scss' scoped>
.mh {
  margin-top: 30px;
  margin-bottom: 30px;
  &:first-child {
    margin-top: 0px;
  }
  &:last-child {
    margin-bottom: 0px;
  }
}

.text-center {
  text-align: center;
}
</style>
