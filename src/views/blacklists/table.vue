<template>
  <el-table v-loading="state.getListLoading" :data="state.tableData" @selection-change="handleSelectionChange">

    <el-table-column type="selection" width="55" />

    <el-table-column :label="$t('safe.restrictedCountry')" prop="countryCode" align="center" >
      <template #default="scope">
        {{ scope.row.dialCode ? countryCodeText(scope.row) : "" }}
      </template>
    </el-table-column>
    <el-table-column :label="$t('safe.countryCode')" prop="dialCode" align="center" />
    <el-table-column :label="$t('safe.restrictedByCountryCode')" prop="isRestrictedByDialCode" align="center">
      <template #default="scope">
        {{ scope.row.isRestrictedByDialCode ? $t('safe.yes') : $t('safe.no') }}
      </template>
    </el-table-column>
    <el-table-column :label="$t('safe.ipRule')" prop="ipRules" align="center" >
      <template #default="scope">
        {{ scope.row.ipRule ? $t('safe.have') : $t('safe.none') }}
      </template>
    </el-table-column>
    <el-table-column :label="$t('safe.restryctedByIP')" prop="isRestrictedByIp" align="center">
      <template #default="scope">
        {{ scope.row.isRestrictedByIp ? $t('safe.yes') : $t('safe.no') }}
      </template>
    </el-table-column>
    <el-table-column :label="$t('common.createdTime')" prop="createTime" align="center" >
      <template #default="scope">
             <span>{{  parseTime(scope.row.createTime)}}</span>
        </template>
    </el-table-column>

    <el-table-column :label="$t('common.remark')" prop="remark" align="center" show-overflow-tooltip>
      <template #default="scope">
        {{ limitWordCounts(scope.row.remark) }}
      </template>
    </el-table-column>

    <el-table-column :label="$t('safe.enabledState')" prop="status" align="center">
      <template #default="scope">
        <el-switch
          v-model="scope.row.status"
          :before-change="() => emit('handleStatusChange', scope.row)"
        />
      </template>
    </el-table-column>

    <el-table-column :label="$t('common.operate')" align="center" class-name="small-padding fixed-width">
      <template #default="scope">
        <el-button type="text" icon="Edit" @click="emit('handleDialog', scope.row, 'edit')">{{ $t('common.edit') }}</el-button>
        <el-button type="text" icon="Delete" @click="emit('handleDelete', scope.row)">{{ $t('common.delete') }}</el-button>
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup>
import { countryCode } from '@/utils/enum.js'
import i18n from "../../i18n";
const props = defineProps({ state: Object })
const emit = defineEmits(['handleDialog', 'handleStatusChange', 'handleDelete'])
const selectedRows = ref([])


function countryCodeText (row) {
  const list = countryCode.filter((i) => i.tel === row.dialCode)
  let txt = ''
  if (list.length) {
    txt=i18n.global.locale === 'en' ? list[0].en : list[0].name
  }
  return txt
}

const handleSelectionChange = (rows) => {
  selectedRows.value = rows
}

function limitWordCounts (string = '') {
  if (!string) return ''
  const zhRegExp = /[\u3040-\u30ff\u3400-\u4dbf\u4e00-\u9fff\uf900-\ufaff\uff66-\uff9f]/g
  if (zhRegExp.test(string)) {
    return string?.slice(0, 11) + (string?.length > 10 ? '...' : '')
  } else {
    const arrayString = string?.split(' ')
    return arrayString?.length > 1
      ? string?.split(' ').slice(0, 11).join(' ') + (string?.split(' ')?.length > 10 ? '...' : '')
      : string?.slice(0, 11) + (string?.length > 10 ? '...' : '')
  }
}

defineExpose({ selectedRows })
</script>
