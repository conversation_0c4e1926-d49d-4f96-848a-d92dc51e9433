import { countryCode } from '@/utils/enum'
import i18n from "../../i18n";

// Form Rules
// const ipRegex = /^((\d{1,3}\.){3}\d{1,3})|(([0-9a-fA-F]{1,4}:){7}([0-9a-fA-F]{1,4}))$/
export const rules = (value) => {
  return {
    name: [{ required: value.isRestrictedByIp, message: i18n.global.t('common.cannotBeEmpty'), trigger: 'change' }],
    isRestrictedByDialCode: [{ required: true, message: i18n.global.t('common.cannotBeEmpty'), trigger: 'change' }],
    isRestrictedByIp: [{ required: true, message: i18n.global.t('common.cannotBeEmpty'), trigger: 'change' }]
    // ipRules: [{ required: value?.isRestrictedByIp, type: 'string', pattern: ipRegex, message: '需要是ipv4或ipv6', trigger: 'change' }]
  }
}

// Form Fields
export const fields = [
  {
    label: i18n.global.t('safe.restrictedCountry'),
    key: 'name',
    type: 'select',
    options: countryCode,
    selectProps: { style: 'width: 100%', placeholder: i18n.global.t('common.select') }
  },
  {
    label: i18n.global.t('safe.countryCode'),
    key: 'dialCode',
    type: 'text',
    span: 10,
    props: { maxlength: '10', placeholder: i18n.global.t('safe.countryCode'), readonly: true }
  },
  {
    label: i18n.global.t('safe.restrictedByCountryCode'),
    key: 'isRestrictedByDialCode',
    type: 'radio',
    span: 10
  },
  {
    label: i18n.global.t('safe.ipRule'),
    key: 'ipRules',
    type: 'text',
    span: 10
  },
  {
    label: i18n.global.t('safe.restryctedByIP'),
    key: 'isRestrictedByIp',
    type: 'radio',
    span: 10
  },
  {
    label: i18n.global.t('common.remark'),
    key: 'remark',
    type: 'textarea',
    props: { maxlength: '16', placeholder: i18n.global.t('common.pleaseEnter'), row: '3', showWordLimit: true }
  }
]
