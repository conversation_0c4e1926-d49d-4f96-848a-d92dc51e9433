<template>
  <div class="app-container">
    <QueryForm ref="queryFormRef" @getList="getList" />

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain @click="handleDialog">{{ $t('common.add') }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button :disabled="!tableRef?.selectedRows?.length" type="danger" plain @click="handleBulkDelete">{{ $t('common.delete') }}</el-button>
      </el-col>
    </el-row>

    <Table ref="tableRef" :state="state" @handleDialog="handleDialog" @handleStatusChange="handleStatusChange" @handleDelete="handleDelete" />

    <pagination
      v-show="state.total > 0"
      :total="state.total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="({ page, limit, ...rest }) => getList({ ...rest, ...queryParams, pageNum: page, pageSize: limit })"
    />

    <el-dialog :title="state.dialogTitle" v-model="state.dialogVisible" width="60%" :before-close="handleClose" append-to-body>
      <Form :formObject="formObject" @submitForm="submitForm" >
        <el-button @click="handleClose">{{state.dialogTitle === '详情' ? i18n.global.t('common.return') : i18n.global.t('common.cancel')}}</el-button>
      </Form>
    </el-dialog>
  </div>
</template>

<script setup>
import { reactive, ref, getCurrentInstance } from 'vue'
import { listData, createData, updateData, updateStatus, deleteData, bulkDelete } from '@/api/blacklist'
import QueryForm from './queryForm'
import Table from './table'
import Form from './form'
import i18n from "../../i18n";

const { proxy } = getCurrentInstance()

const state = reactive({
  tableData: [],
  getListLoading: true,
  total: 0,
  dialogVisible: false,
  dialogTitle: ''
})

const formObject = ref()
const tableRef = ref()
const queryFormRef = ref()
const queryParams = computed(() => queryFormRef.value?.queryParams || { pageNum: 1, pageSize: 10 })

function getList (params) {
  state.getListLoading = true
  listData(params || { pageNum: 1, pageSize: 10 })
    .then((res) => {
      state.tableData = res.data.records
      state.total = res.data.total
    })
    .finally(() => (state.getListLoading = false))
}

function handleDialog (row, method) {
  console.log(row)
  formObject.value = method ? { ...row, dialCode: Number(row.dialCode) } : { isRestrictedByIp: false, isRestrictedByDialCode: false, countryCode: row.countryCode, ipRule: false }
  state.dialogVisible = !state.dialogVisible
  state.dialogTitle = method === 'edit' ? i18n.global.t('common.edit') : i18n.global.t('common.add')
}

function handleClose () {
  state.dialogVisible = false
}

function handleDelete (row) {
  proxy.$modal.confirm(i18n.global.t('common.confirmDelete'))
    .then(async function () {
      await deleteData(row)
      proxy.$modal.msgSuccess(i18n.global.t('common.success'))
      getList()
    }).catch(() => {})
}

function handleBulkDelete () {
  proxy.$modal.confirm(i18n.global.t('common.confirmDelete'))
    .then(async function () {
      await bulkDelete(tableRef.value?.selectedRows)
      proxy.$modal.msgSuccess(i18n.global.t('common.success'))
      getList()
    }).catch(() => {})
}

async function handleStatusChange (row) {
  const text = row.status ? i18n.global.t('common.off') : i18n.global.t('common.on')
  proxy.$modal.confirm(text + '?')
    .then(async function () {
      await updateStatus(row)
      proxy.$modal.msgSuccess(i18n.global.t('common.success'))
      getList()
    }).catch(() => {})
}

function submitForm (values) {
  const onSubmitFuntion = values.id ? updateData : createData
  onSubmitFuntion(values).then((res) => {
    const message = values.id ? i18n.global.t('common.success') : i18n.global.t('common.success')
    proxy.$modal.msgSuccess(message)
    state.dialogVisible = false
    getList()
  })
}

getList()
</script>

<style scoped lang="scss">
    .title {
      margin-bottom: 20px;
      font-size: 16px;
      font-weight: bold;
    }
</style>
