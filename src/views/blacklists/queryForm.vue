<template>
  <el-form :model="queryParams" ref="queryFormRef" :inline="true">
    <el-form-item>
      <el-input
        v-model.number="queryParams.dialCode" type="text" maxlength="10"
        :placeholder="$t('common.pleaseEnter')" style="width: 240px"
      />
    </el-form-item>

    <el-form-item>
      <el-button type="primary" icon="Search" @click="handleQuery">{{ $t('common.search') }}
      </el-button>
      <el-button icon="Refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup>
import { ref } from 'vue'
const { proxy } = getCurrentInstance()

const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  dialCode: null
})

const emit = defineEmits(['getList'])

function handleQuery () {
  queryParams.value.pageNum = 1
  emit('getList', queryParams.value)
}

function resetQuery () {
  queryParams.value.dialCode = null
  proxy.resetForm('queryFormRef')
  emit('getList', queryParams.value)
}

defineExpose({ queryParams })
</script>
