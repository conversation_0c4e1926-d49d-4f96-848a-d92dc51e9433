<template>
  <el-form ref="formRef" :model="formParams" :rules="customRules" label-width="100px">
    <el-row :gutter="20">
      <el-col v-for="field in fields" :key="field.key" :span="field?.span || 20">
        <el-form-item label-width="210px" :label="field.label" :prop="field.key">
          <el-switch v-if="field.type === 'radio'" v-model="formParams[field.key]" @change="changeSwitch(formParams[field.key], field.key)"/>

          <el-select v-else-if="field.type === 'select'" :modelValue="formParams.countryCode" @update:modelValue="value => onChangeCountry(value, field.options)" v-bind="field.selectProps" filterable>
            <el-option
              v-for="(item, index) in field.options"
              :key="index"
              :label="i18n.global.locale === 'en' ? item.en : item.name"
              :value="item.shortName"
            />
          </el-select>

          <el-input v-if="(field.type === 'text' || field.type === 'textarea') && field.key !== 'ipRules'" v-model="formParams[field.key]" v-bind="field.props" :type="field.type" />
          <span v-if="field.key === 'ipRules'">{{ formParams.isRestrictedByIp && countryLimit ? $t('safe.have') : $t('safe.have') }}</span>
        </el-form-item>
      </el-col>
    </el-row>
    <div class="right">
      <slot />
      <el-button type="primary" @click="handleSubmit">{{ $t('common.submit') }}</el-button>
    </div>
  </el-form>
</template>

<script setup>
import { computed, watch, ref, getCurrentInstance } from 'vue'
import { fields as formFields, rules } from './instances'
import { ElMessage } from 'element-plus'
import { countryIp } from '@/api/blacklist'
import i18n from "../../i18n";

const { proxy } = getCurrentInstance()
const ipData = ref()
const props = defineProps({ formObject: Object })
const emit = defineEmits(['submitForm', 'uqpdate:formParams'])

const fields = ref(formFields)
const formRef = ref()
const countryLimit = ref()
const countryCode = ref(props.formObject.countryCode)
const formParams = computed(() => props.formObject)
const customRules = computed(() => rules(formParams.value))
formParams.value.ipRule = false
function onChangeCountry (value, countries) {
  const data = countries.find(country => country.shortName === value)
  formParams.value.name = i18n.global.locale === 'en' ? data?.en : data?.name
  formParams.value.dialCode = data?.tel
  formParams.value.countryCode = value
  countryCode.value = value
}
function handleSubmit () {
  proxy.$refs.formRef.validate(async valid => {
    const { isRestrictedByIp, isRestrictedByDialCode } = formParams.value
    if (valid) {
      if (isRestrictedByIp || isRestrictedByDialCode) {
        emit('submitForm', formParams.value)
      } else {
        ElMessage({
          message: i18n.global.t('safe.bothRestrictionsCanNotBeEmpty'),
          type: 'warning'
        })
      }
    }
  })
}

watch(() => props.formObject, () => {
  proxy.$refs.formRef.resetFields()
  if (formParams.value.isRestrictedByIp) {
    getCountryIp()
  }
})
if (formParams.value.isRestrictedByIp) {
  getCountryIp()
}
// watch(() => formParams.value.isRestrictedByIp, (val) => {
//   console.log(val)
// })
function changeSwitch (val, filed) {
  if (filed === 'isRestrictedByIp') {
    if (val) {
      getCountryIp()
    } else {
      formParams.value.ipRule = false
    }
  }
}
function getCountryIp () {
  if (countryCode.value) {
    const params = {
      countryCode: countryCode.value,
      offset: 0,
      limit: 1
    }
    countryIp(params).then(res => {
      countryLimit.value = res.data
      formParams.value.ipRule = !!res.data
      console.log(!!res.data)
    })
  } else {
    ElMessage.warning(i18n.global.t('safe.selectCountryFirst'))
    formParams.value.isRestrictedByIp = false
  }
}
</script>

<style scoped lang="scss">
  .right {
    display: flex;
    justify-content: flex-end;
  }
</style>
