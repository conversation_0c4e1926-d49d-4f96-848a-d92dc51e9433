<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryFormRef"
      v-show="state.showSearch"
      :inline="true"
    >
      <el-form-item :label="$t('channelManage.channelType')" prop="type">
        <el-select
          style="width: 150px"
          v-model="queryParams.type"
          clearable
          :placeholder="$t('channelManage.pleaseSelect')"
        >
          <el-option :label="$t('channelManage.public')" :value="1" />
          <el-option :label="$t('channelManage.private')" :value="2" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('channelReview.channelId')" prop="channelId">
        <el-input
          v-model.trim="queryParams.channelId"
          :placeholder="$t('common.pleaseEnter')"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('channelManage.channelName')" prop="name">
        <el-input
          v-model.trim="queryParams.name"
          :placeholder="$t('common.pleaseEnter')"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('channelReview.certType')" prop="authType">
        <el-select
          style="width: 150px"
          v-model="queryParams.authType"
          class="m-2"
          clearable
          :placeholder="$t('common.select')"
        >
          <el-option :label="$t('channelReview.personal')" :value="1" />
          <el-option :label="$t('channelReview.enterprise')" :value="2" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('channelReview.status')" prop="authStatus">
        <el-select
          style="width: 150px"
          v-model="queryParams.authStatus"
          class="m-2"
          clearable
          :placeholder="$t('common.select')"
        >
          <el-option :label="$t('channelReview.inReview')" :value="1" />
          <el-option :label="$t('channelReview.notPassed')" :value="2" />
          <el-option :label="$t('channelReview.passed')" :value="3" />
          <el-option :label="$t('channelReview.expired')" :value="4" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">{{
          $t("common.search")
        }}</el-button>
        <el-button icon="Refresh" @click="resetQuery(queryFormRef)">{{
          $t("common.reset")
        }}</el-button>
      </el-form-item>
    </el-form>

    <!-- 表格数据 -->
    <el-table v-loading="state.getListLoading" :data="state.tableData">
      <el-table-column
        :label="$t('channelManage.channelAvatar')"
        prop="headPortrait"
        align="center"
        width="100px"
      >
        <template #default="scope">
          <el-image
            :src="scope.row.headPortrait"
            fit="contain"
            v-if="scope.row.headPortrait"
            class="img-msg"
          />
          <div v-else>{{ $t('channelManage.noAvatar') }}</div>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('channelManage.channelName')"
        align="center"
        prop="channelName"
      />
      <el-table-column
        :label="$t('channelManage.channelType')"
        align="center"
        prop="type"
      >
        <template #default="scope">
          <div>
            {{
              scope.row.type == 1
                ? $t("channelManage.public")
                : $t("channelManage.private")
            }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('channelManage.channelLink')"
        prop="link"
        align="center"
        show-overflow-tooltip
        width="260px"
      >
        <template #default="scope">
          <el-tooltip
            class="item"
            effect="dark"
            :content="$t('channelManage.clickToCopy')"
            placement="bottom"
          >
            <span
              style="cursor: pointer"
              @click="copyContent(scope.row.link)"
              >{{ `${scope.row.link}` }}</span
            >
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('channelReview.channelId')"
        align="center"
        prop="channelId"
      ></el-table-column>
      <el-table-column
        :label="$t('channelManage.ownerID')"
        align="center"
        prop="channelOwnerIdNumber"
      >
      </el-table-column>
      <el-table-column
        :label="$t('channelManage.ownerName')"
        prop="channelOwnerName"
        align="center"
        show-overflow-tooltip
        width="150px"
      />
      <el-table-column
        :label="$t('common.createdTime')"
        prop="createTime"
        align="center"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('channelReview.certType')"
        align="center"
        prop="authType"
      >
        <template #default="scope">
          <div>
            {{
              scope.row.authType == 1
                ? $t("channelReview.personal")
                : $t("channelReview.enterprise")
            }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('channelReview.status')"
        prop="authStatus"
        align="center"
        width="140"
      >
        <template #default="scope">
          <el-tag type="info" v-if="scope.row.authStatus == 1">{{
            $t("channelReview.inReview")
          }}</el-tag>
          <el-tag type="danger" v-if="scope.row.authStatus == 2">{{
            $t("channelReview.notPassed")
          }}</el-tag>
          <el-tag type="success" v-if="scope.row.authStatus == 3">{{
            $t("channelReview.passed")
          }}</el-tag>
          <el-tag type="warning" v-if="scope.row.authStatus == 4">{{
            $t("channelReview.expired")
          }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('common.operate')"
        align="center"
        width="120"
        class-name="small-padding fixed-width"
        fixed="right"
      >
        <template #default="scope">
          <el-button
            type="text"
            v-if="scope.row.authStatus == 1"
            @click="showDialog(scope.row, 'edit')"
          >
            {{ $t("channelReview.auditing") }}
          </el-button>
          <el-button
            type="text"
            v-if="scope.row.authStatus == 2 || scope.row.authStatus == 3"
            @click="showDialog(scope.row, 'view')"
          >
            {{ $t("common.view") }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog v-model="dialogVisible" width="624px" :title="dialogTitle">
      <el-form
        label-position="top"
        ref="formRef"
        :model="authForm"
        label-width="auto"
        class="demo-ruleForm"
      >
        <template v-if="currentRow.authType === 2">
          <h3 class="dialog-form-title">{{ $t('channelReview.enterpriseInfo') }}</h3>
          <el-form-item
            :label="$t('auth.enterpriseRegCountry')"
            prop="enterpriseRegCountry"
          >
            <el-select
              :disabled="true"
              style="width: 100%"
              v-model="authForm.enterpriseRegCountry"
              clearable
              :placeholder="$t('channelManage.pleaseSelect')"
            >
              <el-option
                v-for="item in countryCode"
                :key="item.tel"
                :label="item[langKey]"
                :value="item.shortName"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            :label="$t('auth.enterpriseRegLocation')"
            prop="enterpriseRegLocation"
          >
            <el-input
              :disabled="true"
              v-model="authForm.enterpriseRegLocation"
              type="text"
              autocomplete="off"
            />
          </el-form-item>
          <el-form-item
            :label="$t('auth.enterpriseRegCertPhoto')"
            prop="enterpriseRegCertPhoto"
          >
            <el-image
              style="width: 165px; height: 100px"
              :src="authForm.enterpriseRegCertPhoto"
              fit="fill"
              :preview-src-list="[authForm.enterpriseRegCertPhoto]"
            />
          </el-form-item>
          <el-form-item
            :label="$t('auth.enterpriseRegCode')"
            prop="enterpriseRegCode"
          >
            <el-input
              :disabled="true"
              v-model="authForm.enterpriseRegCode"
              type="text"
              autocomplete="off"
            />
          </el-form-item>
          <h3 class="dialog-form-title mt-30">{{ $t('channelReview.contactInfo') }}</h3>
        </template>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item :label="$t('auth.country')" prop="country">
              <el-select
                :disabled="true"
                style="width: 100%"
                v-model="authForm.country"
                clearable
                :placeholder="$t('channelManage.pleaseSelect')"
              >
                <el-option
                  v-for="item in countryCode"
                  :key="item.tel"
                  :label="item[langKey]"
                  :value="item.shortName"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('auth.idType')" prop="certType">
              <el-select
                :disabled="true"
                style="width: 100%"
                v-model="authForm.certType"
                clearable
                :placeholder="$t('channelManage.pleaseSelect')"
              >
                <el-option :label="$t('auth.IDCard')" :value="1" />
                <el-option :label="$t('auth.passport')" :value="2" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item :label="$t('auth.certNumber')" prop="certNumber">
          <el-input
            :disabled="true"
            v-model="authForm.certNumber"
            type="text"
            autocomplete="off"
          />
        </el-form-item>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item
              :label="$t('auth.certFrontPhoto')"
              prop="certFrontPhoto"
            >
              <el-image
                style="width: 165px; height: 100px"
                :src="authForm.certFrontPhoto"
                fit="fill"
                :preview-src-list="[authForm.certFrontPhoto]"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('auth.certBackPhoto')" prop="certBackPhoto">
              <el-image
                style="width: 165px; height: 100px"
                :src="authForm.certBackPhoto"
                fit="fill"
                :preview-src-list="[authForm.certBackPhoto]"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item
          :label="$t('auth.holdingCertPhoto')"
          prop="holdingCertPhoto"
        >
          <el-image
            style="width: 165px; height: 100px"
            :src="authForm.holdingCertPhoto"
            fit="fill"
            :preview-src-list="[authForm.holdingCertPhoto]"
          />
        </el-form-item>
        <el-form-item :label="$t('auth.fullName')" prop="fullName">
          <el-input
            :disabled="true"
            v-model="authForm.fullName"
            type="text"
            autocomplete="off"
          />
        </el-form-item>
        <el-form-item :label="$t('homePage.mobilePhoneNumber')" prop="phone">
          <el-input
            :disabled="true"
            v-model="authForm.phone"
            type="text"
            autocomplete="off"
          >
            <template #prepend>+{{ authForm.dialCode }}</template>
          </el-input>
        </el-form-item>
        <el-form-item
          :label="$t('auth.reviewResult')"
          prop="result"
          :rules="[{ required: true, message: $t('common.select') }]"
        >
          <el-radio-group
            :disabled="dialogType === 'view'"
            v-model="authForm.result"
          >
            <el-radio :label="true">{{ $t("auth.approved") }}</el-radio>
            <el-radio :label="false">{{ $t("auth.reject") }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          v-if="!authForm.result"
          :label="$t('auth.reasonRejection')"
          prop="rejectReason"
          :rules="[{ required: true, message: $t('common.pleaseEnter') }]"
        >
          <el-input
            :disabled="dialogType === 'view'"
            v-model="authForm.rejectReason"
            type="textarea"
            autocomplete="off"
            maxlength="300"
          />
        </el-form-item>

        <el-form-item>
          <div style="margin-top: 20px">
            <el-button
              :disabled="dialogType === 'view'"
              type="primary"
              @click="submitForm(formRef)"
              >{{ $t("auth.submit") }}</el-button
            >
            <el-button @click="resetForm(formRef)">{{
              $t("common.cancel")
            }}</el-button>
          </div>
        </el-form-item>
      </el-form>
    </el-dialog>

    <pagination
      v-show="state.total > 0"
      :total="state.total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="FundWithdrawalReview">
import { reactive, ref, computed } from 'vue'
import { pageChannelAuthList, channelAuth } from '@/api/channelReview'
import { ElMessage } from 'element-plus'

import { countryCode } from '@/utils/enum.js'
import i18n from '../../i18n'
const myt = i18n.global.t
const queryFormRef = ref()
const formRef = ref(null)
const authForm = ref({})

// 搜索参数
const queryParams = ref({
  type: null,
  channelId: null,
  name: null,
  channelLink: null,
  authType: null,
  authStatus: null,
  pageNum: 1,
  pageSize: 10
})

const state = reactive({
  showSearch: true,
  getListLoading: false,
  tableData: [],
  total: 0,
  selectedIds: [],
  dialogVisible: false,
  dialogTitle: ''
})
function replaceEmptyStringWithNull (obj) {
  Object.entries(obj).forEach(([key, value]) => {
    if (value === '') {
      obj[key] = null
    }
  })
  return obj
}
// 获取列表数据
function getList () {
  state.getListLoading = true
  const query = replaceEmptyStringWithNull(queryParams.value)
  pageChannelAuthList(query)
    .then((response) => {
      state.tableData = response.data.records
      state.total = response.data.total
    })
    .finally(() => {
      state.getListLoading = false
    })
}
// 查询数据
function handleQuery () {
  queryParams.value.pageNum = 1
  getList()
}
// 重置查询
const resetQuery = (formEl) => {
  if (!formEl) return
  console.log(formEl)
  formEl.resetFields()
  getList()
}

const dialogVisible = ref(false)
const currentRow = ref(null)
const dialogTitle = computed(() => {
  if (currentRow.value?.authType === 1) {
    return myt('channelReview.personalApply')
  }
  return myt('channelReview.enterpriseApply')
})

const submitForm = (formEl) => {
  if (!formEl) return
  formEl.validate(async (valid) => {
    if (valid) {
      console.log('submit!')
      const { id, rejectReason, result } = authForm.value
      const ret = await channelAuth({
        id,
        rejectReason: result ? null : rejectReason,
        result
      })
      ElMessage.success(i18n.global.t('channelReview.submitSuccess'))
      resetForm(formRef.value)
      getList()
      console.log(ret)
    } else {
      console.log('error submit!')
      return false
    }
  })
}

const resetForm = (formEl) => {
  if (!formEl) return
  dialogVisible.value = false
  formEl.resetFields()
}

const dialogType = ref('')
const langKey = ref(i18n.global.locale === 'zh' ? 'name' : 'en')
const showDialog = (row, type) => {
  console.log(row, type, i18n.global.locale)
  dialogType.value = type
  currentRow.value = row
  dialogVisible.value = true
  authForm.value = {
    ...currentRow.value,
    rejectReason: currentRow.value.rejectionReason || null,
    result: currentRow.value.authStatus === 3
  }
  setTimeout(() => {
    formRef.value.clearValidate()
  })
}

function copyContent (text) {
  const InputDom = document.createElement('input')
  InputDom.value = text
  document.body.appendChild(InputDom)
  InputDom.select() // 选择对象
  document.execCommand('Copy') // 执行浏览器复制命令
  InputDom.remove()
  ElMessage.success(i18n.global.t('msg.copied'))
}

getList()
</script>
<style scoped>
.img-msg {
  width: 60px;
  height: 60px;
  border-radius: 6px;
  background: #eee;
}
.dialog-form-title {
  color: rgba(0, 0, 0, 0.80);
  padding-left: 8px;
  margin-bottom: 24px;
  position: relative;
  &::before {
    content: '';
    position: absolute;
    left: 0;
    height: 100%;
    background-color: #067EFF;
    width: 4px;
    border-radius: 20px;
  }
}
</style>
