<template>
  <div>
    <h3 class="dialog-title">{{ $t('crawlerManage.addTarget') }}</h3>
    <el-form
      ref="formRef"
      :model="formParams"
      :rules="rules"
      label-width="150px"
    >
      <el-form-item :label="$t('crawlerManage.selectPlatform')" prop="source">
        <el-select
          v-model="formParams.source"
          :placeholder="$t('common.pleaseEnter')"
          style="width: 100%"
        >
          <el-option label="Telegram" value="telegram" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('crawlerManage.channelName')" prop="channelName">
        <el-input
          v-model="formParams.channelName"
          type="text"
          :placeholder="$t('common.pleaseEnter')"
        ></el-input>
      </el-form-item>
      <el-form-item :label="$t('crawlerManage.channelLink')" prop="url">
        <el-input
          v-model="formParams.url"
          type="text"
          :placeholder="$t('common.pleaseEnter')"
        ></el-input>
      </el-form-item>
      <el-form-item :label="$t('crawlerManage.isEnabled')" prop="status">
        <el-switch v-model="formParams.status" :active-value="1" :inactive-value="0" />
      </el-form-item>
    </el-form>
    <div class="footer-options">
      <el-button
        :loading="state.submitLoading"
        type="primary"
        @click="submitForm"
        >{{ $t('common.confirm') }}</el-button
      >
      <el-button @click="emit('close')">{{ $t('common.cancel') }}</el-button>
    </div>
  </div>
</template>

<script setup>
// 修改密码

import { reactive, getCurrentInstance, defineEmits } from 'vue'
import i18n from "../../../i18n";
import {
  addConfig
} from '@/api/tgSpider/config'
import { ElMessage } from 'element-plus'
// import { sendMessage } from '@/api/consumer/imuser'
const { proxy } = getCurrentInstance()
const rules = reactive({
  source: [{ required: true, message: i18n.global.t('crawlerManage.selectPlatform'), trigger: 'change' }],
  channelName: [{ required: true, message: i18n.global.t('crawlerManage.enterChannelName'), trigger: 'change' }],
  url: [{ required: true, message: i18n.global.t('crawlerManage.enterChannelLink'), trigger: 'change' }]
})

const formParams = reactive({
  source: 'telegram',
  channelName: '',
  url: '',
  status: 1
})
const state = reactive({
  submitLoading: false
})

const emit = defineEmits(['close'])

const submitForm = () => {
  proxy.$refs.formRef.validate((valid) => {
    if (valid) {
      state.submitLoading = true
      addConfig({
        ...formParams
      }).then((response) => {
        if (response.data === true) {
          ElMessage.success(i18n.global.t('common.success'))
          emit('close', '添加目标')
        } else {
          ElMessage.error(i18n.global.t('common.failed'))
        }
      }).finally(() => (state.submitLoading = false))
    }
  })
}
</script>

<style lang="scss" scoped>
.footer-options {
  text-align: right;
}
</style>
