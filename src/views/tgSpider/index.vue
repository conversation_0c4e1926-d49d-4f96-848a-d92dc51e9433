<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      v-show="showSearch"
      ref="queryFormRef"
      :inline="true"
      label-width="150px"
    >
      <el-form-item :label="$t('crawlerManage.platform')" prop="source">
        <el-select
          style="width: 150px"
          v-model="queryParams.source"
          clearable
          class="m-2"
          :placeholder="$t('common.select')"
        >
          <el-option :label="$t('common.all')" value="" />
          <el-option label="Telegram" value="telegram" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('crawlerManage.channelName')" prop="channelName">
        <el-input
          v-model="queryParams.channelName"
          :placeholder="$t('common.select')"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('crawlerManage.channelLink')" prop="url">
        <el-input
          v-model="queryParams.url"
          :placeholder="$t('common.pleaseEnter')"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >{{ $t('common.search') }}</el-button
        >
        <el-button icon="Refresh" @click="resetForm(queryFormRef)"
          >{{ $t('common.reset') }}</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="showDialog('添加目标')"
          >{{ $t('common.add') }}
        </el-button>
        <el-button
          type="primary"
          icon="Open"
          plain
          @click="changeSwitchStatus()"
          >{{ $t('crawlerManage.switchState') }}</el-button
        >
        <el-button type="danger" plain icon="Delete" @click="handleDelete(ids)"
          >{{ $t('common.delete') }}</el-button
        >
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="getListLoading"
      :data="tableDataList"
      @selection-change="handleTableSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column :label="$t('crawlerManage.platform')" prop="source" align="center" />
      <el-table-column
        :label="$t('crawlerManage.channelName')"
        align="center"
        prop="channelName"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        :label="$t('crawlerManage.channelLink')"
        align="center"
        prop="url"
        :show-overflow-tooltip="true"
      />
      <el-table-column :label="$t('crawlerManage.addTime')" align="center" prop="time">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('crawlerManage.enabledState')" align="center" prop="state">
        <template #default="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="1"
            :inactive-value="0"
            :before-change="() => changeSwitchStatus(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column :label="$t('crawlerManage.contents')" align="center" prop="count">
        <template #default="scope">
          <el-button
            type="text"
            @click="showDialog('爬取内容详情', scope.row)"
            >{{ scope.row.count }}</el-button
          >
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('common.operate')"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            type="text"
            icon="Edit"
            @click="handleStartPython(scope.row)"
            >{{ $t('crawlerManage.crawlOnce') }}</el-button
          >
          <el-button
            type="text"
            icon="Delete"
            @click="handleDelete([scope.row.id])"
            >{{ $t('common.delete') }}</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog
      v-if="state.dialogVisible"
      v-model="state.dialogVisible"
      :width="state.dialogWidth"
      append-to-body
      custom-class="el-dialog-no-header"
    >
      <Add v-if="state.dialogType === '添加目标'" @close="closeDialog" />
      <List
        :params="seleectedRowData"
        v-if="state.dialogType === '爬取内容详情'"
        @close="closeDialog"
      />
    </el-dialog>

    <!-- 查看或新增banner -->
    <el-dialog
      :title="addBannerDialogTitle === '爬取内容详情'? i18n.global.t('crawlerManage.contentDetails'):addBannerDialogTitle"
      v-model="addBannerDialogVisible"
      width="900px"
      append-to-body
    >
      <el-form
        ref="addBannerFormRef"
        :model="addBannerParams"
        label-width="150px"
        :disabled="addBannerDialogTitle === '详情'"
      >
        <el-row>
          <el-col :span="14">
            <el-form-item label="频道名称" prop="title">
              <el-input
                v-model="addBannerParams.channelName"
                :placeholder="$t('common.pleaseEnter')"
              />
            </el-form-item>

            <el-form-item
              label="点击"
              prop="order"
              v-if="addBannerDialogTitle === '详情'"
            >
              {{ addBannerParams.click }}
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer" v-if="addBannerDialogTitle === '详情'">
          <el-button @click="addBannerDialogVisible = false">{{ $t('common.close') }}</el-button>
        </div>

        <div class="dialog-footer" v-else>
          <el-button
            type="primary"
            @click="addBanner(addBannerParams)"
            :loading="loading"
            >{{ $t('common.confirm') }}</el-button
          >
          <el-button @click="addBannerDialogVisible = false">{{ $t('common.cancel') }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="BannerManage">
import { ref, reactive } from 'vue'
import {
  addConfig,
  getConfigList,
  deleteConfig,
  startPython,
  changeStatus
} from '@/api/tgSpider/config'
import { ElMessage, ElMessageBox } from 'element-plus'
import Add from './components/add.vue'
import List from './components/list.vue'
import i18n from "../../i18n";

const ids = ref([])
const showSearch = ref(true)
const seleectedRowData = ref({})
const queryParams = ref({
  source: '',
  channelName: '',
  url: '',
  pageNum: 1,
  pageSize: 10
})
const state = reactive({
  dialogVisible: false,
  dialogType: '',
  dialogWidth: '500px'
})
const total = ref(0)
const getListLoading = ref(false)
const tableDataList = ref([])

const addBannerDialogVisible = ref(false)
const addBannerDialogTitle = ref('新增')

const addBannerParams = ref({
  channelName: ''
})

const addBannerFormRef = ref()

const showDialog = (dialogType, row) => {
  if (row) seleectedRowData.value = { ...row, dialogType }
  state.dialogType = dialogType
  switch (dialogType) {
    case '添加目标':
      state.dialogWidth = '500px'
      break
    case '爬取内容详情':
      state.dialogWidth = '750px'
      break
    default:
      state.dialogWidth = '500px'
  }

  state.dialogVisible = true
}
const closeDialog = (type) => {
  state.dialogVisible = false
  state.dialogType = null
  if (type === '添加目标') {
    getList()
  }
}

// 查询
const handleQuery = () => {
  getList()
}

const queryFormRef = ref()
// 重置查询
const resetForm = (formEl) => {
  if (!formEl) return
  console.log(formEl)
  formEl.resetFields()
  getList()
}
const rows = ref([])
const handleTableSelectionChange = (e) => {
  ids.value = e.map((item) => item.id)
  rows.value = e
  console.log(ids.value)
}

const loading = ref(false)

// 新增或者编辑banner提交
const addBanner = (data) => {
  if (!addBannerDialogVisible.value || !addBannerFormRef.value) return
  addBannerFormRef.value.validate((valid, fields) => {
    if (valid) {
      loading.value = true
      addConfig({
        channelName: data.channelName,
        row: data.row
      }).then((response) => {
        loading.value = false
        if (response.data === true) {
          addBannerDialogVisible.value = false
          ElMessage.success(i18n.global.t('common.success'))
          getList()
        } else {
          ElMessage.error(i18n.global.t('common.failed'))
        }
      })
    }
  })
}

function getList () {
  getListLoading.value = true
  getConfigList(queryParams.value)
    .then((response) => {
      total.value = response.data.total
      tableDataList.value = response.data.records
    })
    .finally(() => (getListLoading.value = false))
}

function handleDelete (data) {
  if (data.length === 0) {
    ElMessage.warning(i18n.global.t('common.pleaseSelectDeleteItem'))
    return
  }
  ElMessageBox.confirm(i18n.global.t('common.confirmDelete'), {
    confirmButtonText: i18n.global.t('common.confirm'),
    cancelButtonText: i18n.global.t('common.cancel'),
    type: 'warning'
  })
    .then(() => {
      const params = {
        ids: data
      }
      deleteConfig(params).then((item) => {
        ElMessage.success(i18n.global.t('common.success'))
        getList()
      })
    })
    .catch(() => {})
}

function changeSwitchStatus (row) {
  return new Promise((resolve, reject) => {
    const params = {
      ids: [],
      status: 1
    }
    if (row) {
      params.ids = [row.id]
      params.status = Number(!row.status)
    } else {
      if (rows.value.length === 0) {
        ElMessage.warning(i18n.global.t('common.select'))
        return
      }
      params.ids = ids.value
      params.status = Number(rows.value.some((i) => !i.status))
    }
    changeStatus(params)
      .then((response) => {
        if (!row) {
          getList()
        }
        ElMessage.success(i18n.global.t('common.success'))
        resolve(true)
      })
      .catch(() => {
        if (row) row.status = !row.status
        reject(new Error('Error'))
      })
  })
}

function handleStartPython (data) {
  ElMessageBox.confirm(i18n.global.t('crawlerManage.confirmGetChannelData'), {
    confirmButtonText: i18n.global.t('common.confirm'),
    cancelButtonText: i18n.global.t('common.cancel'),
    type: 'warning'
  })
    .then(() => {
      startPython({
        channelUrl: data.url,
        tgMsgId: data.lastTgMsgId
      }).then((item) => {
        ElMessage.success(i18n.global.t('crawlerManage.successAndWait'))
        getList()
      })
    })
    .catch(() => {})
}

getList()
</script>

<style lang="scss" scoped>
.image-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);

  &:hover {
    border-color: var(--el-color-primary);
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100px;
    height: 100px;
    text-align: center;
  }

  .image {
    width: 100px;
    height: 100px;
    display: block;
  }
}
</style>
