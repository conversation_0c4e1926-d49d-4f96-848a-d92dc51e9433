<template>
  <div class="user-selected-wrapper" ref="userRef" v-loading="loading">
    <span v-if="props.action === 'detail'">{{ checkedSummary }}</span>
    <el-input
      v-else
      type="text"
      :placeholder="$t('common.select')"
      v-model="checkedSummary"
      @focus="focus"
    ></el-input>
    <div class="transfer-box" v-if="state.visibleUser">
      <div class="transfer-box-inner">
        <div class="transfer-unit transfer-lf">
          <div class="search">
            <el-input
              v-model="searchText"
              :disabled="checkAll"
              :placeholder="$t('common.search')"
              clearable
              @input="inputChange"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>
          <div class="tool">
            <span class="num">共{{ state.pagination.total }}项</span>
            <!-- <el-button type="text" @click="handleCheckAllChange">全选</el-button> -->
            <el-checkbox
              v-model="checkAll"
              :indeterminate="isIndeterminate"
              @change="handleCheckAllChange"
              >全部用户</el-checkbox
            >
          </div>
          <div class="cont">
            <div class="title">用户列表</div>
            <template v-if="!checkAll">
            <!-- <template> -->
            <div class="data-list" ref="userListRef" id="userListRef">
              <div
                class="item"
                v-for="(item, idx) in state.transferList"
                :key="idx"
              >
                <el-checkbox
                  v-model="item.checked"
                  :disabled="checkAll"
                  @change="checkedItemClick(item)"
                  >{{ item.name }}</el-checkbox
                >
              </div>
            </div>
            <div class="pagination-tool">
              <el-pagination
                small
                layout="prev, pager, next"
                @current-change="currentPageChange"
                @prev-click="prevPageChange"
                @next-click="nextPageChange"
                :total="state.pagination.total"
              />
            </div>
            </template>
            <span class="all-data" v-else>全部用户</span>
          </div>
        </div>
        <div class="transfer-unit transfer-rh">
          <div class="tool">
            <span class="num" v-if="!checkAll"
              >已选{{ checkedList.length }}项</span
            >
            <span class="num" v-if="checkAll"
              >已选{{ state.pagination.total }}项</span
            >
            <el-button type="text" @click="handleClear">清空</el-button>
          </div>
          <div class="cont">
            <div class="data-list" v-if="!checkAll">
              <div class="item" v-for="(item, idx) in checkedList" :key="idx">
                {{ item.name }}
              </div>
            </div>
            <span class="all-data" v-else>全部用户</span>
          </div>
        </div>
      </div>
      <div class="transfer-footer">
        <el-button @click="close">关闭</el-button>
      </div>
    </div>
  </div>
</template>

<script setup name="UserSelected">
import { ref, reactive, defineProps, defineEmits, computed, watch, onBeforeUnmount } from 'vue'
import { receivers } from '@/api/announcement/announcement'
import { debounce } from '@/utils/index.js'
import i18n from "../../../i18n";

const props = defineProps({
  modelValue: [Number, String],
  action: String,
  showNum: {
    type: Number,
    default: 5
  },
  selectedRowData: Object
})
const emit = defineEmits(['change', 'update:modelValue'])
const searchText = ref()
const showNum = ref(5)
const checkAll = ref(false)
const isIndeterminate = ref(false)
const loading = ref(false)
const checkedList = ref([])
const userRef = ref(null)
const state = reactive({
  visibleUser: false,
  transferList: [],
  unitPageChecked: [],
  pagination: {
    total: 0,
    pageNum: 1,
    pageSize: 10
  }
})
const checkedSummary = computed(() => {
  const text = `（ ${i18n.global.t('mosAssist.selected')} ${checkedList.value.length}）`
  const list = checkedList.value.map(i => i.name).slice(0, showNum.value).join(',')
  const character = '...'
  let str = ''
  // console.log(checkedList.value, props.modelValue)
  if (props.modelValue === '-1') {
    return i18n.global.t('mosAssist.all')
  }
  if (props.modelValue === '0') {
    return i18n.global.t('mosAssist.selectAll')
  }
  if (checkedList.value.length) {
    if (checkedList.value.length === state.pagination.total && !searchText.value) {
      str = i18n.global.t('mosAssist.all')
    } else {
      str = text + list
      if (checkedList.value.length > showNum.value) {
        str = text + list + character
      }
    }
  }

  return str
})
watch(() => checkedList.value, (val) => {
  if (val && val.length) {
    if (val.length === state.pagination.total && !searchText.value) {
      checkAll.value = true
      isIndeterminate.value = false
      emit('change', '-1')
      emit('update:modelValue', '-1')
    } else {
      checkAll.value = false
      isIndeterminate.value = true
      emit('change', val)
      emit('update:modelValue', val.map(i => i.id).join(','))
    }
  } else {
    checkAll.value = false
    isIndeterminate.value = false
    emit('change', [])
    emit('update:modelValue', '')
  }
}, {
  deep: true
})
watch(() => checkAll.value, (val) => {
  state.transferList = state.transferList.map(i => {
    i.checked = val
    return i
  })
  searchText.value = ''
  if (val) {
    filterList()
    emit('change', '-1')
    emit('update:modelValue', '-1')
  } else {
    checkedList.value = []
    emit('change', [])
    emit('update:modelValue', '')
  }
})
watch(() => props.modelValue, (val) => {
  if (val === '-1') {
    checkAll.value = true
  }
}, {
  immediate: true
})
function filterList () {
  state.pagination.pageNum = 1
  getReceiversList()
}
function currentPageChange (val) {
  state.pagination.pageNum = val
  getReceiversList()
}
function prevPageChange () {
  if (loading.value) {
    return
  }
  state.pagination.pageNum--
  getReceiversList()
}
function nextPageChange () {
  if (loading.value) {
    return
  }
  state.pagination.pageNum++
  getReceiversList()
}

function initCheckedHanlder () {
  if (props.action === 'add') {
    checkedList.value = []
    return
  }
  const receiversInfo = props.selectedRowData?.receiversInfo
  if (receiversInfo && receiversInfo.length) {
    checkedList.value = props.selectedRowData.receiversInfo
  } else {
    // console.log(props.modelValue)
    if (!props.modelValue) {
      checkedList.value = []
    }
  }
}

const inputChange = debounce(filterList, 1000, false)
// const prevPageChangeHandler = debounce(prevPageChange, 1000, true)
// const nextPageChangeHandler = debounce(nextPageChange, 1000, true)
// const currentPageChangeHandler = debounce(currentPageChange, 1000, false)

initCheckedHanlder()
getReceiversList()
function getReceiversList () {
  if (loading.value) {
    return
  }
  if (checkAll.value) {
    emit('change', '-1')
    emit('update:modelValue', '-1')
  }

  const params = {
    nickname: searchText.value,
    pageNum: state.pagination.pageNum,
    pageSize: 10
  }
  loading.value = true
  receivers(params).then((res) => {
    const { total, records } = res.data
    // console.log(props.selectedRowData.receiversInfo)
    // if (state.pagination.pageNum === 1) {
    //   if (props.selectedRowData.receiversInfo) {
    //     checkedList.value = props.selectedRowData.receiversInfo
    //   } else {
    //     checkedList.value = []
    //   }
    // }
    const list = checkedList.value.map(i => i.id)
    // console.log(list, props.modelValue)
    // if (props.modelValue === '-1') {
    //   checkAll.value = true
    // }
    if (list.length) {
      state.transferList = records.map(i => {
        i.checked = list.indexOf(i.id) > -1
        return i
      })
    } else {
      // console.log(checkAll.value)
      state.transferList = records.map(i => {
        i.checked = checkAll.value
        return i
      })
    }
    // console.log(state.transferList)
    state.pagination.total = total
    loading.value = false
  }).catch(err => {
    console.log(err)
    loading.value = false
  })
}
const focus = () => {
  state.visibleUser = true
}
const close = () => {
  state.visibleUser = false
}
// 全选
const handleCheckAllChange = (val) => {
  // console.log(val)
  isIndeterminate.value = false
}
// 清空
const handleClear = () => {
  checkAll.value = false
  checkedList.value = []
  state.transferList = state.transferList.map(i => {
    i.checked = false
    return i
  })
  // const list = checkedList.value.map(i => i.id)
  // if (list.length) {
  //   state.transferList = state.transferList.map(i => {
  //     i.checked = list.indexOf(i.id) > -1
  //     return i
  //   })
  // } else {
  //   state.transferList = state.transferList.map(i => {
  //     i.checked = false
  //     return i
  //   })
  // }
}
function checkedItemClick (item) {
  // console.log(item.checked)
  if (item.checked) {
    const list = checkedList.value.map(i => i.id)
    const idx = list.indexOf(item.id)
    if (idx < 0) {
      checkedList.value.push(item)
    }
  } else {
    const list = checkedList.value.map(i => i.id)
    const idx = list.indexOf(item.id)
    checkedList.value.splice(idx, 1)
  }
}
document.addEventListener('click', documentClickHanlder, true)
// 点击外部收起下拉选框
function documentClickHanlder (e) {
  // user-btn 与 iconfont icon-renwu  这两个点击使组件弹出的按钮元素
  if (e.target.className === 'user-selected-wrapper') {
    return
  }
  try {
    if (userRef.value && !userRef.value.contains(e.target) && state.visibleUser) {
      state.visibleUser = false
    }
  } catch (err) {}
}
window.addEventListener('scroll', handleScroll, true)
function handleScroll (e) {
  if (e.target.scrollTop + e.target.clientHeight >= e.target.scrollHeight) {
    // 在此处放入你的加载更多方法
    // console.log('在此处放入你的加载更多方法', state.pagination.pageNum++)
    // getReceiversList()
  }
}

onBeforeUnmount(() => {
  handleClear()
  emit('change', [])
  emit('update:modelValue', '')
})

</script>

<style lang="scss" scoped>
.user-selected-wrapper {
  position: relative;
  width: 100%;
}
.transfer-box {
  width: 100%;
  border: 1px solid #ddd;
  background: #fff;
  border-radius: 4px;
  position: absolute;
  left: 0;
  top: 40px;
  z-index: 99;
  box-shadow: 0px 2px 2px 2px rgba(108, 107, 107, 0.1);
  .transfer-box-inner {
    display: flex;
    // height: 400px;
  }
  .transfer-footer {
    border-top: 1px solid #ddd;
    padding: 5px;
    text-align: right;
  }
  .transfer-unit {
    flex: 1;
    height: 100%;
    padding: 10px 0;
    &:first-child {
      border-right: 1px solid #ddd;
    }
    :deep(.el-input__prefix) {
      display: flex;
      align-items: center;
    }
    .data-list {
      height: 300px;
      overflow-y: auto;
    }
    .tool {
      margin: 5px 0;
      font-size: 12px;
      .num {
        margin-right: 10px;
        color: #666;
      }
    }
    .search,
    .tool,
    .title,
    .data-list,
    .all-data {
      padding: 0 10px;
    }
    .cont {
      .title {
        color: #999;
        margin: 10px 0 5px;
        font-size: 12px;
      }
    }
    .pagination-tool {
      border-top: 1px solid #ddd;
      display: flex;
      align-items: center;
      justify-content: center;
      padding-top: 5px;
      margin-top: 10px;
    }
    &.transfer-rh {
      .tool {
        .num {
          color: #999;
        }
      }
      .data-list {
        height: 360px;
        margin-top: 10px;
        .item {
          height: 30px;
          padding-bottom: 5px;
        }
      }
    }
  }
}
</style>
