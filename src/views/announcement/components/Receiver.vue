<template>
  <div class="user-selected-wrapper">
    <el-dialog
    :title="$t('common.view')"
    v-model="state.visible"
    :before-close="close"
    width="60%">

    <div class="user-list">
      <div v-if="props.selectedRowData.receivers == '0'">
        <span class="bold1" >{{ $t('mosAssist.toReceiverTip') }} </span><br><br>
                <span class="bold" >{{ $t('mosAssist.device') }}:</span><span class="spacer">{{ (importCondition.deviceType === undefined ||importCondition.deviceType === null || importCondition.deviceType ==='')?$t('mosAssist.all'):importCondition.deviceType}}</span><br><br>
                <span class="bold" >{{ $t('mosAssist.version') }}:</span><span class="spacer">{{ (importCondition.version === undefined ||importCondition.version === null || importCondition.version ==='')?$t('mosAssist.all'):importCondition.version}}</span><br><br>
                <span class="bold" >{{ $t('mosAssist.language') }}:</span><span class="spacer">{{ (importCondition.useLanguage === undefined ||importCondition.useLanguage === null || importCondition.useLanguage ==='')?$t('mosAssist.all'):importCondition.useLanguage === 'zh'?$t('mosAssist.chinese'):$t('mosAssist.english')}}</span><br><br>
                <span class="bold" >{{ $t('userManage.dialCode') }}:</span><span class="spacer">{{ (importCondition.dialCode === undefined ||importCondition.dialCode === null || importCondition.dialCode ==='')?$t('mosAssist.all'):importCondition.dialCode}}</span><br><br>
                <span class="bold" >{{ $t('mosAssist.registerTimeRange') }}:</span><span class="spacer">{{ (importCondition.registraStartTime === null || importCondition.registraStartTime ==='')?'': importCondition.registraStartTime }}
          {{ (importCondition.registraEndTime === null || importCondition.registraEndTime ==='')?'':' - '+importCondition.registraEndTime}}</span><br><br>
                <span class="bold" >{{ $t('mosAssist.lastLoginTimeRange') }}:</span><span class="spacer">{{ (importCondition.onlineStartTime === null || importCondition.onlineStartTime ==='')?'':importCondition.onlineStartTime}}
         {{ (importCondition.onlineEndTime === null || importCondition.onlineEndTime ==='')?'':' - ' +importCondition.onlineEndTime}}</span><br><br>
      </div>
      <div v-else class="item" v-for="(item, i) in state.transferList" :key="i">
        <img :src="item.headPortrait" alt="">
        <div class="info">
          <div class="name"><strong>{{ item.nickname }}</strong>
            <p>ID：{{ item.id }}</p></div>
        </div>
      </div>
    </div>
    <template #footer>
    <div class="dialog-footer">
    <el-button type="primary" @click="close">{{ $t('common.confirm') }}</el-button>
    </div>
  </template>
</el-dialog>
  </div>
</template>

<script setup name="Receiver">
import {defineEmits, defineProps, reactive, watch} from 'vue'
import {receiversInfo} from '@/api/announcement/announcement'

const props = defineProps({
  modelValue: Boolean,
  selectedRowData: Object
})
console.log(props, 123)
const state = reactive({
  transferList: [],
  visible: props.modelValue
})
var importCondition = null;
if(props.selectedRowData.importCondition != null) {
  importCondition = JSON.parse(props.selectedRowData.importCondition);
  if (importCondition.deviceType !== undefined && importCondition.deviceType != null) {
    if (importCondition.deviceType === 1) {
      importCondition.deviceType = 'IOS'
    }
    if (importCondition.deviceType === 2) {
      importCondition.deviceType = 'Android'
    }
    if (importCondition.deviceType === 3) {
      importCondition.deviceType = 'WEB'
    }
    if (importCondition.deviceType === 4) {
      importCondition.deviceType = 'Windows'
    }
    if (importCondition.deviceType === 5) {
      importCondition.deviceType = 'Mac'
    }
  } else {
    importCondition.deviceType = '';
  }
  if (importCondition.useLanguage !== undefined && importCondition.useLanguage != null) {
    if (importCondition.useLanguage === 'zh') {
      // importCondition.useLanguage = '中文'
    }
    if (importCondition.useLanguage === 'en') {
      // importCondition.useLanguage = '英文'
    }
  } else {
    importCondition.useLanguage = ''
  }
  console.log(importCondition, 111111)
}


watch(() => props.modelValue, (val) => {
  if (val) {
    getReceiversInfo()
  } else {
    console.log(val)
    emit('update:modelValue', false)
  }
}, {
  immediate: true
})
const emit = defineEmits(['update:modelValue'])
function getReceiversInfo () {
  const params = {
    noticeId: props.selectedRowData.id
  }
  receiversInfo(params).then((res) => {
    state.transferList = res.data
  })
}
function close () {
  state.visible = false
  emit('update:modelValue', false)
}

</script>

<style lang="scss" scoped>
.user-selected-wrapper{
  .user-list{
    display: flex;
    flex-wrap: wrap;
    .item{
      width: 30%;
      display: flex;
      align-items: center;
      margin: 15px 15px 0 0;
      .name{
        color: #999;
        strong{
          color: #333;
          font-weight: 600;
        }
        p{
          margin-top: 8px;
        }
      }
      img{
        width: 30px;
        height: 30px;
        margin-right: 10px;
        border-radius: 50%;
        border:1px solid #ddd
      }
    }
  }
}

 .bold {
   font-weight: bold;
 }
.spacer {
  margin-left: 10px;
}
.bold1 {
  font-weight: bold;
}
</style>
