<template>
  <div class="user-selected-wrapper">
    <el-dialog
      :title="props.selectedRowData.listStatus ? $t('system.success') : $t('mosAssist.exception')"
      v-model="state.visible"
      :before-close="close"
      width="1000px"
    >
      <div  v-loading="loading">
        <div class="user-list">
          <div class="item" v-for="(item, i) in state.list" :key="i">
            <img :src="item.headPortrait" alt="" :key="item.id" />
            <div class="info">
              <div class="name">
                <strong>{{ item.nickname }}</strong>
                <p>ID：{{ item.id }}</p>
              </div>
            </div>
          </div>
        </div>
        <div class="page-center">
          <pagination
            v-show="state.total > 0"
            :total="state.total"
            v-model:page="state.pageNum"
            v-model:limit="state.pageSize"
            @pagination="getList"
            :page-sizes="[12, 33, 42, 63]"
          />
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="close">{{ $t('common.confirm') }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Receiver">
import { reactive, defineProps, defineEmits, watch, ref } from 'vue'
import { sendList } from '@/api/announcement/announcement'

const props = defineProps({
  modelValue: Boolean,
  selectedRowData: Object
})
const state = reactive({
  list: [],
  visible: props.modelValue,
  total: 0,
  pageNum: 1,
  pageSize: 12
})
const loading = ref(false)
watch(
  () => props.modelValue,
  (val) => {
    if (val) {
      state.list = []
      state.pageNum = 1
      getList()
    } else {
      emit('update:modelValue', false)
    }
  },
  {
    immediate: true
  }
)
const emit = defineEmits(['update:modelValue'])
function getList () {
  loading.value = true
  const params = {
    status: props.selectedRowData.listStatus,
    noticeId: props.selectedRowData.id,
    pageNum: state.pageNum,
    pageSize: state.pageSize
  }
  sendList(params).then((res) => {
    state.list = res.data.records
    state.total = res.data.total
    loading.value = false
  }).catch(() => { loading.value = false })
}
function close () {
  state.visible = false
  emit('update:modelValue', false)
}
</script>

<style lang="scss" scoped>
.page-center {
  display: flex;
  justify-content: center;
  :deep(.pagination-container .el-pagination) {
    position: static;
  }
  :deep(.pagination-container) {
    height: auto;
  }
}
.user-selected-wrapper {
  .user-list {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    .item {
      width: 33.3%;
      display: flex;
      align-items: center;
      margin-bottom: 15px;
      padding: 10px 30px;
      .name {
        color: #999;
        strong {
          color: #333;
          font-weight: 600;
        }
        p {
          margin-top: 8px;
        }
      }
      img {
        width: 45px;
        height: 45px;
        margin-right: 10px;
        border-radius: 50%;
        border: 1px solid #ddd;
        object-fit: cover
        ;
      }
    }
  }
}
</style>
