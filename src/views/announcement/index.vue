<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      v-show="showSearch"
      ref="queryFormRef"
      :inline="true"
      label-width="130px"
    >
      <el-form-item :label="$t('common.caption')" prop="title">
        <el-input
          v-model="queryParams.title"
          :placeholder="$t('common.pleaseEnter')"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item :label="$t('mosAssist.notificationType')" prop="noticeType">
        <el-select
          style="width: 130px"
          v-model="queryParams.noticeType"
          clearable
          class="m-2"
          :placeholder="$t('common.select')"
        >
          <el-option :label="$t('common.text')" :value="1" />
          <el-option :label="$t('common.image')" :value="2" />
        </el-select>
      </el-form-item>

      <el-form-item :label="$t('mosAssist.createdTimeRange')" prop="times">
        <el-date-picker
          v-model="queryParams.times"
          type="daterange"
          range-separator="-"
          :start-placeholder="$t('common.startTime')"
          :end-placeholder="$t('common.endTime')"
          value-format="YYYY-MM-DD"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >{{ $t('common.search') }}</el-button
        >
        <el-button icon="Refresh" @click="resetForm(queryFormRef)"
          >{{ $t('common.reset') }}</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="showAddBannerForm"
          v-hasPermi="['system:notice:add']"
          >{{ $t('common.add') }}</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          @click="handleDelete(ids)"
          v-hasPermi="['system:notice:remove']"
          >{{ $t('common.delete') }}</el-button
        >
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="getListLoading"
      :data="tableDataList"
      @selection-change="handleTableSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column
        label="ID"
        align="center"
        prop="id"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        :label="$t('mosAssist.notificationCaption')"
        align="center"
        prop="title"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        :label="$t('mosAssist.language')"
        align="center"
        prop="languageType"
        show-overflow-tooltip
      >
        <template #default="scope">
          <span v-if="scope.row.languageType === 0">{{$t('mosAssist.all')}}</span>
          <span v-if="scope.row.languageType === 1">{{$t('mosAssist.chinese')}}</span>
          <span v-if="scope.row.languageType === 2">{{$t('mosAssist.english')}}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('mosAssist.notificationType')"
        align="center"
        prop="noticeType"
        show-overflow-tooltip
      >
        <template #default="scope">
          <span v-if="scope.row.noticeType === 1">{{$t('common.text')}}</span>
          <span v-if="scope.row.noticeType === 2">{{$t('common.image')}}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('mosAssist.receiver')"
        align="center"
        prop="receivers"
      >
        <template #default="scope">
          <span v-if="scope.row.receivers === '-1'">{{$t('common.all')}}</span>
          <el-button type="text" @click="handleViewUser(scope.row)"
            v-if="scope.row.receivers && scope.row.receivers !== '-1'">{{$t('mosAssist.viewDetails')}}</el-button>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('mosAssist.likes')"
        align="center"
        prop="likeNums"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        :label="$t('mosAssist.views')"
        align="center"
        prop="numberOfReads"
        :show-overflow-tooltip="true"
      />
      <el-table-column :label="$t('mosAssist.publishedState')" align="center" prop="state" width="100">
        <template #default="scope">
          <span>{{ scope.row.state === 0 ? $t('mosAssist.unpublished') : $t('mosAssist.published') }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('mosAssist.progress')" align="center" prop="state" width="140">
        <template #default="scope">
          <div v-if="scope.row.state === 0">0%</div>
          <span v-else-if="scope.row.sendSuccessNum === scope.row.sendNum">
            <div style="color: #409eff">{{scope.row.sendSuccessNum}}{{$t('mosAssist.persons')}}</div>
            <div style="color: #67c23a; font-weight: 600">{{ $t('mosAssist.done') }}</div>
          </span>
          <div v-else>
            <div style="color: #409eff">
              <span style="cursor: pointer" @click="handleViewProgress(scope.row, 1)">{{scope.row.sendSuccessNum}}</span>
              / {{scope.row.sendNum}}{{$t('mosAssist.persons')}}
            </div>
            <div v-if="scope.row.state === 2">{{(scope.row.sendSuccessNum / scope.row.sendNum * 100).toFixed(1) }}%</div>
            <div v-else>
              <span style="color:#f56c6c ;margin-right: 5px; font-weight: 600;cursor: pointer" @click="handleViewProgress(scope.row, 0)">{{$t('mosAssist.exception')}}</span>
              <span style="color: #409eff; font-weight: 600;cursor: pointer" @click="resendNotice(scope.row.id)">{{$t('mosAssist.continue')}}</span>
            </div>
          </div>
        </template>
      </el-table-column>

      <!-- <el-table-column label="发布时间" align="center" prop="releaseTime" width="100">
        <template #default="scope">
          <span>{{ parseTime(scope.row.releaseTime, "{y}-{m}-{d} {h}:{i}:{s}") }}</span>
        </template>
      </el-table-column> -->
      <el-table-column
        :label="$t('mosAssist.author')"
        align="center"
        prop="author"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        :label="$t('common.createdTime')"
        align="center"
        prop="createTime"
        width="100"
      >
        <template #default="scope">
          <span>{{
            parseTime(scope.row.createTime, "{y}-{m}-{d} {h}:{i}:{s}")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('common.operate')"
        align="right"
        fixed="right"
        width="200"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button type="text" icon="Edit" @click="handleView(scope.row)"
            >{{ $t('common.details') }}</el-button
          >
          <el-button
            type="text"
            icon="Edit"
            v-if="scope.row.state === 0"
            @click="handleEdit(scope.row)"
            >{{ $t('common.edit') }}</el-button
          >
          <el-button
            type="text"
            icon="Edit"
            v-if="scope.row.state === 0"
            @click="releaseNotice(scope.row.id)"
            >{{ $t('common.post') }}</el-button
          >
          <el-button
            type="text"
            icon="Delete"
            @click="handleDelete([scope.row.id])"
            >{{ $t('common.delete') }}</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
    <el-dialog
        v-if="memberDialogVisivle"
        v-model="memberDialogVisivle"
               :width="state.moreDialogWidth"
               append-to-body
               custom-class="el-dialog-no-header"
    >
      <UserList v-if="memberDialogVisivle"
                @close="closeMoreDialog"
                @submitForm="submitForm"
                @selectAll="selectAll"
      />

    </el-dialog>
    <!-- 新增/编辑公告 -->
    <el-dialog
      :title="addBannerDialogTitle === '详情'?$t('common.details'):(addBannerDialogTitle === '编辑'?$t('common.edit'):$t('common.add'))"
      v-model="addBannerDialogVisible"
      width="1000px"
      append-to-body
    >
      <el-form
        ref="addBannerFormRef"
        :model="addBannerParams"
        :rules="addBannerFormRules"
        label-width="110px"
      >
        <el-row>
          <el-col :span="23">
            <el-form-item :label="$t('mosAssist.notificationCaption')" prop="title">
              <el-input
                :maxlength="50"
                v-model="addBannerParams.title"
                :placeholder="$t('common.pleaseEnter')"
              />
            </el-form-item>
            <el-form-item :label="$t('mosAssist.notificationType')" prop="noticeType">
              <el-select
                style="width: 130px"
                v-model="addBannerParams.noticeType"
                clearable
                class="m-2"
                :placeholder="$t('common.select')"
              >
                <el-option :label="$t('common.text')" :value="1" />
                <el-option :label="$t('common.image')" :value="2" />
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('mosAssist.author')" prop="author">
              <el-input
                v-model="addBannerParams.author"
                :maxlength="8"
                :placeholder="$t('common.pleaseEnter')"
              />
            </el-form-item>
            <el-form-item :label="$t('mosAssist.receiver')" prop="receivers">
              <el-radio v-model="addBannerParams.radio" label="-1">{{$t('mosAssist.allUsers')}}</el-radio>
              <el-radio v-model="addBannerParams.radio" label="1">{{$t('mosAssist.partOfUsers')}}</el-radio>
              <el-button v-if="addBannerParams.radio == '1'"
                type="danger"
                plain
                icon="Delete"
                @click="selectUser"
              >{{$t('mosAssist.selectUsers')}}</el-button>
              <span v-if="addBannerParams.radio == '1'">{{$t('mosAssist.selected')}}{{ state.userCount}}{{$t('mosAssist.users')}}</span>
<!--              <UserSelected v-model="addBannerParams.receivers"-->
<!--              v-if="addBannerDialogVisible"-->
<!--              :action="addBannerDialogTitle === '新增'? 'add': 'edit'"-->
<!--                :selectedRowData="addBannerParams"-->
<!--                @change="changeUserSelect"/>-->
            </el-form-item>
            <el-form-item :label="$t('mosAssist.coverPhoto')" prop="images" v-if="addBannerParams.noticeType === 2">
                <image-upload v-model="addBannerParams.images"
                  :limit="3"></image-upload>
            </el-form-item>
            <el-form-item :label="$t('mosAssist.abstract')" prop="summary">
              <el-input
                v-model="addBannerParams.summary"
                :placeholder="$t('common.pleaseEnter')"
                type="textarea"
              />
            </el-form-item>
            <el-form-item :label="$t('mosAssist.content')" prop="content">
              <Editor v-model="addBannerParams.content"/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="add" :loading="loading"
            >{{ $t('common.confirm') }}</el-button
          >
          <el-button @click="addBannerDialogVisible = false">{{ $t('common.cancel') }}</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 详情 -->
    <el-dialog
      :title="addBannerDialogTitle === '详情'?$t('common.details'):(addBannerDialogTitle === '编辑'?$t('common.edit'):$t('common.add'))"
      v-model="detailBannerDialogVisible"
      width="800px"
      append-to-body
    >
      <el-form
        ref="addBannerFormRef"
        :model="addBannerParams"
        :rules="addBannerFormRules"
        label-width="100px"
        :disabled="addBannerDialogTitle === '详情'"
      >
        <el-row>
          <el-col :span="23">
            <el-form-item :label="$t('mosAssist.notificationCaption')" prop="title">
              {{addBannerParams.title}}
            </el-form-item>
            <el-form-item :label="$t('mosAssist.language')" prop="languageType">
              <span v-if="addBannerParams.languageType === 0">{{$t('mosAssist.all')}}</span>
              <span v-if="addBannerParams.languageType === 1">{{$t('mosAssist.chinese')}}</span>
              <span v-if="addBannerParams.languageType === 2">{{$t('mosAssist.english')}}</span>
            </el-form-item>
            <el-form-item :label="$t('mosAssist.notificationType')" prop="noticeType">
              <span v-if="addBannerParams.noticeType === 1">{{$t('common.text')}}</span>
              <span v-if="addBannerParams.noticeType === 2">{{$t('common.image')}}</span>
            </el-form-item>
            <el-form-item :label="$t('mosAssist.author')" prop="author">
              {{addBannerParams.author}}
            </el-form-item>
            <el-form-item :label="$t('mosAssist.receiver')" prop="receivers">
              <UserSelected v-model="addBannerParams.receivers"
              v-if="detailBannerDialogVisible"
              :selectedRowData="addBannerParams"
               action="detail" :showNum="-1"/>
            </el-form-item>
            <el-form-item :label="$t('mosAssist.coverPhoto')" prop="images" v-if="addBannerParams.noticeType === 2">
                <image-upload v-model="addBannerParams.images"
                  :disabled="addBannerDialogTitle === '详情'"
                  :isShowTip="addBannerDialogTitle !== '详情'"
                  :limit="3"></image-upload>
            </el-form-item>
            <el-form-item :label="$t('mosAssist.abstract')" prop="summary">
              <div v-html="addBannerParams.summary.replace(/\n/g, '<br>')"></div>
            </el-form-item>
            <el-form-item :label="$t('mosAssist.content')" prop="content" v-show="addBannerDialogTitle === '详情'">
              <div v-html="addBannerParams.content"></div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailBannerDialogVisible = false">{{ $t('common.close') }}</el-button>
        </div>
      </template>
    </el-dialog>

    <Receiver :selectedRowData="selectedRowData" v-model="visibleUser" v-if="visibleUser"/>
    <ProgressList :selectedRowData="selectedRowData" v-model="visibleProgress" v-if="visibleProgress"/>
  </div>
</template>

<script setup name="announcement">
import { reactive, ref, nextTick, watch } from 'vue'
import UserList from './userList.vue'
import Editor from '@/components/Editor'
import UserSelected from './components/UserSelected'
import Receiver from './components/Receiver'
import ProgressList from './components/ProgressList'
import {
  getNoticeList,
  release,
  deleteNotice,
  addNotice,
  editNotice,
  receiversInfo,
  resend
} from '@/api/announcement/announcement'
import { ElMessage, ElMessageBox } from 'element-plus'
import { formatDate } from '@/utils/index'
import i18n from "../../i18n";

const state = reactive({
  info: null,
  filesList: [],
  userCount : 0
})
const showSearch = ref(true)
const queryParams = ref({
  title: '',
  times: [],
  noticeType: '',
  pageNum: 1,
  pageSize: 10
})
const total = ref(999)
const getListLoading = ref(false)
const tableDataList = ref([
  {
    id: '',
    title: '',
    content: '',
    type: '',
    state: '',
    releaseTime: '',
    createTime: ''
  }
])

const addBannerDialogVisible = ref(false)
const detailBannerDialogVisible = ref(false)
const addBannerDialogTitle = ref('新增')
const addBannerParams = ref({
  title: '',
  noticeType: '',
  author: '',
  images: [],
  summary: '',
  receivers: '',
  receiversInfo: [],
  content: ''
})
const addBannerFormRef = ref()
const visibleUser = ref(false)
const selectedRowData = ref()
const visibleProgress = ref(false)

// 查询数据
function handleQuery () {
  queryParams.value.pageNum = 1;
  getList()
}

const queryFormRef = ref()
// 重置查询
const resetForm = (formEl) => {
  if (!formEl) return
  formEl.resetFields()
    queryParams.value.pageNum = 1;
  getList()
}

const loading = ref(false)
const cleartimer = ref()
// 监视属性
watch(addBannerDialogVisible, (newValue, oldValue) => {
  // 回调函数形式
  if (!newValue) {
    state.filesList = []
    addBannerParams.value.id = ''
    getList()
    if (cleartimer.value) {
      clearInterval(cleartimer.value)
    }
  } else {
    if(addBannerParams.value.radio == '1' && state.userCount == 0 ){
      return;
    }
    // cleartimer.value = setInterval(() => {
    //   add('auto')
    // }, 20 * 1000)
  }
})

// 新增/编辑公告
const add = (action) => {
  if (!addBannerDialogVisible.value || !addBannerFormRef.value) return
  if(addBannerParams.value.radio == '1' && state.userCount == 0 ){
    ElMessage( i18n.global.t('common.userCanNotEmpty'))
    return;
  }
  addBannerFormRef.value.validate((valid, fields) => {
    if (valid) {
      if (action !== 'auto') {
        loading.value = true
      }
      loading.value = true
      const params = addBannerParams.value
      if(params.radio == "-1"){
        params.receivers = "-1"
      }
      params.images = addBannerParams.value.images && addBannerParams.value.images.length ? addBannerParams.value.images : null
      let requestMethod = null
      if (params.id) {
        requestMethod = editNotice
      } else {
        requestMethod = addNotice
      }
      requestMethod(params).then((res) => {
        if (action !== 'auto') {
          loading.value = false
          addBannerDialogVisible.value = false
          state.filesList = []
          state.userCount = 0
          ElMessage.success(i18n.global.t('common.success'))
          getList()
        } else {
          addBannerParams.value.id = res.data
          const time = formatDate(new Date())
          ElMessage(time + ' ' + i18n.global.t('common.success'))
        }
      }).finally(res => {
        loading.value = false
      })
    }
  })
}
// 数据列表
function getList () {
  if (getListLoading.value) {
    return
  }
  getListLoading.value = true
  if(queryParams.value.times.length==2){
    queryParams.value.times = [+new Date(queryParams.value.times[0]+' 00:00:00'), +new Date(queryParams.value.times[1]+' 23:59:59')]
  }
  getNoticeList(queryParams.value)
    .then((response) => {
      total.value = response.data.total
      tableDataList.value = response.data.records
    })
    .finally(() => (getListLoading.value = false))
}
const ids = ref([])
const handleTableSelectionChange = (e) => {
  ids.value = e.map((item) => item.id)
  // console.log(ids.value)
}

const showAddBannerForm = () => {
  addBannerDialogTitle.value = '新增'
  addBannerDialogVisible.value = true
  // addBannerFormRef.value.resetFields()
  nextTick(() => {
    addBannerParams.value = {
      id: '',
      title: '',
      type: '',
      author: '',
      languageType: 0,
      receivers: '',
      receiversInfo: [],
      images: [],
      summary: '',
      content: '',
      radio :'-1'
    }
    state.userCount = 0
    addBannerParams.value.receivers = ''
    addBannerParams.value.receiversInfo = []
    // addBannerFormRef.value.resetFields()
    addBannerFormRef.value.resetFields()
    console.log(addBannerParams.value)
  })
}

const handleView = async (row) => {
  addBannerDialogTitle.value = '详情'
  addBannerParams.value = JSON.parse(JSON.stringify(row))
  addBannerParams.value.receiversInfo = await getReceiversInfo(row)
  detailBannerDialogVisible.value = true
}

//  function handleEdit (row) {
const handleEdit = async (row) => {
  addBannerDialogTitle.value = '编辑'
  addBannerParams.value = JSON.parse(JSON.stringify(row))
  if(addBannerParams.value.receivers == '0'){
    addBannerParams.value.radio = '1';
    state.userCount = JSON.parse(addBannerParams.value.importCondition).total
  }else if (addBannerParams.value.receivers == '-1'){
    addBannerParams.value.radio = '-1';
  }else {
    const arr = addBannerParams.value.receivers.split(",")
    addBannerParams.value.radio = '1';
    state.userCount = arr.length;
  }
  addBannerParams.value.receiversInfo = await getReceiversInfo(row)
  addBannerDialogVisible.value = true
}

function changeUserSelect (val) {
  if (val instanceof Array) {
    addBannerParams.value.receivers = val.map(i => i.id).join(',')
  }
  // else {
  //   console.log(2, val)
  //   addBannerParams.value.receivers = val
  // }
}
function getReceiversInfo (row) {
  return new Promise((resolve, reject) => {
    const params = {
      noticeId: row.id
    }
    receiversInfo(params).then((res) => {
      // state.transferList = res.data
      let list = []
      if (res.data?.length) {
        list = res.data.map(i => {
          return {
            ...i,
            id: i.id + '',
            name: `${i.firstName}_${i.lastName}`,
            checked: true
          }
        })
      } else {
        list = []
      }
      console.log(list)
      resolve(list)
    })
  })
}
function handleViewUser (row) {
  visibleUser.value = true
  selectedRowData.value = row
}
function handleViewProgress (row, status) {
  if (status && row.sendSuccessNum === 0) return
  visibleProgress.value = true
  selectedRowData.value = { ...row, listStatus: status }
}
function resendNotice (id) {
  ElMessageBox.confirm(i18n.global.t('common.confirmPost'), {
    confirmButtonText: i18n.global.t('common.confirm'),
    cancelButtonText: i18n.global.t('common.cancel'),
    type: 'warning'
  })
    .then(() => {
      resend(id).then((item) => {
        if (item.data === false) {
          ElMessage.error(i18n.global.t('common.doNotRepost'))
          getList()
        } else {
          ElMessage.success(i18n.global.t('common.success'))
          getList()
        }
      })
    })
    .catch(() => {})
}
// 发布公告
function releaseNotice (data) {
  ElMessageBox.confirm(i18n.global.t('mosAssist.sureToPublishThisAnnouncement'), {
    confirmButtonText: i18n.global.t('common.confirm'),
    cancelButtonText: i18n.global.t('common.cancel'),
    type: 'warning'
  })
    .then(() => {
      release(data).then((item) => {
        if (item.data === false) {
          ElMessage.error(i18n.global.t('common.doNotRepost'))
          getList()
        } else {
          ElMessage.success(i18n.global.t('common.success'))
          getList()
        }
      })
    })
    .catch(() => {})
}
const memberDialogVisivle = ref(false)
// 选择好友
function selectUser () {
  state.moreDialogWidth = '1200px'
  memberDialogVisivle.value = true
}
function closeMoreDialog () {
  memberDialogVisivle.value = false
}
function submitForm (data) {
  addBannerParams.value.receivers = data.value.join(',')
  state.userCount = data.value.length
  addBannerParams.value.importCondition = null
}
function selectAll (data) {
  addBannerParams.value.importCondition = data.paramJson
  state.userCount = data.total
  addBannerParams.value.receivers = '0'
}
// 删除公告
function handleDelete (data) {
  if (Object.keys(data).length === 0) {
    ElMessage.error(i18n.global.t('common.pleaseSelectDeleteItem'))
    return
  }

  ElMessageBox.confirm(i18n.global.t('common.confirmDelete'), {
    confirmButtonText: i18n.global.t('common.confirm'),
    cancelButtonText: i18n.global.t('common.cancel'),
    type: 'warning'
  })
    .then(() => {
      deleteNotice(data).then((item) => {
        ElMessage.success(i18n.global.t('common.success'))
        getList()
      })
    })
    .catch(() => {})
}

const addBannerFormRules = reactive({
  title: [
    { required: true, message: i18n.global.t('common.cannotBeEmpty'), trigger: 'blur' },
    { max: 50, message: i18n.global.t('common.charactersExceeds') + '50', trigger: 'blur' }
  ],
  noticeType: [{ required: true, message: i18n.global.t('common.cannotBeEmpty'), trigger: 'change' }],
  author: [
    { required: true, message: i18n.global.t('common.cannotBeEmpty'), trigger: 'blur' },
    { max: 8, message: i18n.global.t('common.charactersExceeds') + '8', trigger: 'blur' }
  ],
  images: [{ required: true, message: i18n.global.t('common.cannotBeEmpty'), trigger: ['change', 'blur'] }]
})

getList()
</script>

<style lang="scss" scoped>
.image-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);

  &:hover {
    border-color: var(--el-color-primary);
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100px;
    height: 100px;
    text-align: center;
  }

  .image {
    width: 100px;
    height: 100px;
    display: block;
  }
}
.el-image{
  margin: 0 5px 5px 0;
}

.el-form{
  max-height: 800px;
  overflow-y: auto;
}
.limit-num{
  width: 100%;
  color: #999;
  text-align: right;
}
</style>

<style scoped lang="scss">
// .el-upload--picture-card 控制加号部分
:deep(.hide .el-upload--picture-card) {
  display: none;
}
// 去掉动画效果
:deep(.el-list-enter-active),
:deep(.el-list-leave-active) {
  transition: all 0s;
}
:deep(.el-list-enter, .el-list-leave-active) {
  opacity: 0;
  transform: translateY(0);
}
:deep(.el-form-item__content){
  img{
    max-width: 100%;
  }
}
:deep(.el-textarea__inner){
  height:120px;
}
:deep(.is-disabled){
  cursor: not-allowed;
}

</style>
