<template >
  <div class="login-record">
    <h3 class="dialog-title">{{ $t("system.selectUser") }}</h3>
    <el-form :model="queryParams" ref="queryFormRef" :inline="true">
      <el-form-item :label="$t('mosAssist.device')">
        <el-select
          style="width: 150px"
          v-model="queryParams.deviceType"
          clearable
          class="m-2"
          :placeholder="$t('common.select')"
        >
          <el-option label="IOS" :value="1" />
          <el-option label="Android" :value="2" />
          <el-option label="WEB" :value="3" />
          <el-option label="Windows" :value="4" />
          <el-option label="Mac" :value="5" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('mosAssist.mosappVersion')" prop="title">
        <el-input
          v-model="queryParams.version"
          :placeholder="$t('common.pleaseEnter')"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item :label="$t('mosAssist.useLanguage')">
        <el-select
          style="width: 150px"
          v-model="queryParams.useLanguage"
          clearable
          class="m-2"
          :placeholder="$t('common.select')"
        >
          <el-option label="中文" :value="'zh-CN'" />
          <el-option label="English" :value="'en-US'" />
          <el-option label="繁體中文(香港)" :value="'zh-HK'" />
          <el-option label="繁體中文(臺灣)" :value="'zh-TW'" />
          <el-option label="ភាសាខ្មែរ" :value="'km-KH'" />
          <el-option label="日本語" :value="'ja-JP'" />
          <el-option label="Tiếng Việt" :value="'vi-VN'" />
          <el-option label="ภาษาไทย" :value="'th-TH'" />
          <el-option label="Bahasa Melayu" :value="'ms-MY'" />
          <el-option label="한국어" :value="'ko-KR'" />
          <el-option label="Bahasa Indonesia" :value="'id-ID'" />
          <el-option label="ລາວ" :value="'lo-LA'" />
          <el-option label="हिन्दी" :value="'hi-IN'" />
        </el-select>
      </el-form-item>

      <el-form-item :label="$t('mosAssist.countryCode')" prop="title">
        <el-input
          v-model="queryParams.dialCode"
          :placeholder="$t('common.pleaseEnter')"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item
        :label="$t('mosAssist.registeredTime')"
        style="width: 308px"
      >
        <el-date-picker
          v-model="queryParams.timeRanges"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          :start-placeholder="$t('common.startDate')"
          :end-placeholder="$t('common.endDate')"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item
        :label="$t('mosAssist.lastOnlineTime')"
        style="width: 308px"
      >
        <el-date-picker
          v-model="queryParams.logOutTimeRanges"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          :start-placeholder="$t('common.startDate')"
          :end-placeholder="$t('common.endDate')"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >{{ $t("common.search") }}
        </el-button>
        <el-button icon="Refresh" @click="resetQuery">{{
          $t("common.reset")
        }}</el-button>
        <el-button @click="selectAll">{{
          $t("mosAssist.selectAll")
        }}</el-button>
      </el-form-item>
    </el-form>

    <el-table
      :data="state.tableData"
      v-loading="state.getListLoading"
      style="width: 100%"
      @selection-change="handleTableSelectionChange"
      :row-key="getRowKey"
    >
      <el-table-column
        type="selection"
        width="55"
        align="center"
        :reserve-selection="true"
      />
      <el-table-column
        :label="$t('mosAssist.nickname')"
        prop="nickname"
        align="center"
      />
      <el-table-column
        :label="$t('common.phoneNumber')"
        prop="phone"
        :show-overflow-tooltip="true"
      >
        <template #default="scope">
          <template v-if="scope.row.dialCode && scope.row.phone">
            <span v-if="scope.row.phone.includes('_')">{{
              `+${scope.row.dialCode} ${scope.row.phone.split("_")[1]}`
            }}</span>
            <span v-else>{{
              `+${scope.row.dialCode} ${scope.row.phone}`
            }}</span>
          </template>
        </template>
      </el-table-column>
      <!-- <el-table-column :label="$t('mosAssist.country')" prop="dialCode">
        <template #default="scope">
          <span>{{
            scope.row.dialCode ? countryCodeText(scope.row) : ""
          }}</span>
        </template>
      </el-table-column> -->
      <el-table-column
        :label="$t('mosAssist.registeredTime')"
        align="center"
        prop="createTime"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('userManage.lastOnlineTime')"
        prop="lastOnTime"
        align="center"
      />
      <el-table-column
        :label="$t('mosAssist.useLanguage')"
        prop="useLanguage"
        align="center"
      >
        <template #default="scope">
          <span v-if="scope.row.useLanguage === 'zh-CN'">中文</span>
          <span v-if="scope.row.useLanguage === 'en-US'">English</span>
          <span v-if="scope.row.useLanguage === 'zh-HK'">繁體中文(香港)</span>
          <span v-if="scope.row.useLanguage === 'zh-TW'">繁體中文(臺灣)</span>
          <span v-if="scope.row.useLanguage === 'km-KH'">ភាសាខ្មែរ</span>
          <span v-if="scope.row.useLanguage === 'ja-JP'">日本語</span>
          <span v-if="scope.row.useLanguage === 'vi-VN'">Tiếng Việt</span>
          <span v-if="scope.row.useLanguage === 'th-TH'">ภาษาไทย</span>
          <span v-if="scope.row.useLanguage === 'ms-MY'">Bahasa Melayu</span>
          <span v-if="scope.row.useLanguage === 'ko-KR'">한국어</span>
          <span v-if="scope.row.useLanguage === 'id-ID'">Bahasa Indonesia</span>
          <span v-if="scope.row.useLanguage === 'lo-LA'">ລາວ</span>
          <span v-if="scope.row.useLanguage === 'hi-IN'">हिन्दी</span>
        </template>
      </el-table-column>

      <el-table-column
        :label="$t('mosAssist.mosappVersion')"
        prop="version"
        align="center"
        width="100"
      />
    </el-table>

    <div>
      <pagination
        v-show="state.total > 0"
        :total="state.total"
        v-model:page="state.pageNum"
        v-model:limit="state.pageSize"
        @pagination="getList"
      />
    </div>
    <div class="dialog-footer">
      <el-button @click="emit('close')">{{ $t("common.cancel") }}</el-button>
      <el-button type="primary" @click="submitForm">{{
        $t("common.confirm")
      }}</el-button>
    </div>
  </div>
</template>

<script setup name="loginRecord">
import { defineEmits, reactive, ref } from "vue";
import { getNoticeUserFilter } from "@/api/announcement/announcement";
import { countryCode } from "@/utils/enum.js";
import { ElMessage } from "element-plus";
import i18n from "../../i18n";

const emit = defineEmits(["close", "selectAll"]);

const ids = ref([]);
const handleTableSelectionChange = (e) => {
  ids.value = e.map((item) => item.id);
};
function getRowKey(row) {
  // console.log(row)
  return row.id;
}
// 提交
function submitForm() {
  if (ids.value.length == 0) {
    ElMessage.error(i18n.global.t("common.userCanNotEmpty"));
    return;
  }
  //关闭页面 把选择的ids传入添加的用户
  emit("submitForm", ids);
  emit("close");
}
// 全选
function selectAll() {
  var data = ref({});
  data.paramJson = JSON.stringify(selectUserParam.value);
  data.total = state.total;
  //关闭弹窗 传所有入参的json到添加公告的importCondition入参
  emit("selectAll", data);
  emit("close");
}
const queryParams = ref({
  registraStartTime: null,
  registraEndTime: null,
  loginStartTime: null,
  loginEndTime: null,
  timeRanges: [],
  logOutTimeRanges: [],
});

// 好友列表
const state = reactive({
  tableData: [],
  // getListLoading: false,
  // total: 100,
  pageNum: 1,
  pageSize: 10,
});
const selectUserParam = reactive({});
// 查询数据
function handleQuery() {
  getList();
}

const queryFormRef = ref();

// 重置查询
function resetQuery() {
  if (queryFormRef.value) {
    queryFormRef.value.resetFields();
  }
  queryParams.value.timeRanges = [];
  queryParams.value.logOutTimeRanges = [];
  queryParams.value.deviceType = null;
  queryParams.value.useLanguage = null;
  queryParams.value.dialCode = null;
  queryParams.value.version = null;
  handleQuery();
}

function countryCodeText(row) {
  const list = countryCode.filter((i) => i.shortName === row);
  let txt = "";
  if (list.length) {
    txt = i18n.global.locale === "en" ? list[0].en : list[0].name;
  }
  return txt;
}

function getList() {
  // state.getListLoading = true
  //
  //
  const [registraStartTime, registraEndTime] =
    queryParams.value.timeRanges || [];
  queryParams.value.registraStartTime =
    registraStartTime === undefined ? null : registraStartTime + " 00:00:00";
  queryParams.value.registraEndTime =
    registraEndTime === undefined ? null : registraEndTime + " 23:59:59";

  const [onlineStartTime, onlineEndTime] =
    queryParams.value.logOutTimeRanges || [];
  queryParams.value.onlineStartTime =
    onlineStartTime === undefined ? null : onlineStartTime + " 00:00:00";
  queryParams.value.onlineEndTime =
    onlineEndTime === undefined ? null : onlineEndTime + " 23:59:59";

  const params = {
    ...state,
    ...queryParams.value,
  };
  selectUserParam.value = params;
  if (params.tableData) delete params.tableData;
  getNoticeUserFilter(params).then((response) => {
    state.tableData = response.data.records;
    state.total = response.data.total;
    selectUserParam.value.total = response.data.total;
  });
  // state.getListLoading = false
}

getList();
</script>

<style lang="scss" scoped>
.login-record {
  position: relative;
  min-height: 500px;

  .footer-options {
    text-align: right;
  }
}
</style>
