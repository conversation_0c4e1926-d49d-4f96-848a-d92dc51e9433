<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryFormRef" v-show="state.showSearch" :inline="true">
      <el-form-item :label="$t('customer.sname')" prop="name">
        <el-input v-model.trim="queryParams.customerName" :placeholder="$t('common.pleaseEnter')" clearable
          style="width: 240px" @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('customer.slink')" prop="link">
        <el-input v-model.trim="queryParams.customerUrl" :placeholder="$t('common.pleaseEnter')" clearable
          style="width: 240px" @keyup.enter="handleQuery" />
      </el-form-item>
      <!-- <el-form-item :label="$t('channelManage.channelState')" prop="status">
        <el-select style="width: 150px" v-model="queryParams.status" class="m-2" clearable
          :placeholder="$t('common.pleaseSelectState')">
          <el-option :label="$t('channelManage.normal')" :value="0" />
          <el-option :label="$t('common.freeze')" :value="1" />
          <el-option :label="$t('groupManage.deleted')" :value="2" />
        </el-select>
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">{{
          $t("common.search")
          }}</el-button>
        <el-button icon="Refresh" @click="resetQuery(queryFormRef)">{{
          $t("common.reset")
          }}</el-button>
      </el-form-item>
    </el-form>

    <!-- 表格数据 -->
    <el-table v-loading="state.getListLoading" :data="state.tableData">
      <el-table-column :label="$t('customer.avatar')" prop="avatar" align="center" width="100px">
        <template #default="scope">
          <el-image :src="scope.row.avatar" fit="contain" v-if="scope.row.avatar" class="img-msg" />
          <div v-else>{{ $t('channelManage.noAvatar') }}</div>
        </template>
      </el-table-column>
      <el-table-column :label="$t('customer.name')" align="center" prop="name" />
      <el-table-column :label="$t('customer.customerNum')" align="center" prop="conversationCount">
        <template #default="scope">
          <el-button @click="showDialog(scope.row.id, 1)" type="text">{{
            scope.row.conversationCount
            }}</el-button>
        </template>
      </el-table-column>
      <el-table-column :label="$t('customer.conversation')" align="center" prop="customerConversationCount">
        <template #default="scope">
          <el-button @click="showDialog(scope.row.id, 3)" type="text">{{
            scope.row.customerConversationCount
            }}</el-button>
        </template>
      </el-table-column>
      <el-table-column :label="$t('customer.adminNum')" align="center" prop="adminCount">
        <template #default="scope">
          <el-button @click="showDialog(scope.row.id, 2)" type="text">{{
            scope.row.adminCount
            }}</el-button>
        </template>
      </el-table-column>
      <el-table-column :label="$t('customer.link')" prop="link" align="center" show-overflow-tooltip width="260px">
        <template #default="scope">
          <el-tooltip class="item" effect="dark" :content="$t('channelManage.clickToCopy')" placement="bottom">
            <span style="cursor: pointer" @click="copyContent(scope.row.link)">{{ `${scope.row.link}`
              }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column :label="$t('customer.ownerId')" align="center" prop="userId">
      </el-table-column>
      <el-table-column :label="$t('customer.ownerName')" prop="ownerName" align="center" show-overflow-tooltip
        width="150px" />
      <el-table-column :label="$t('customer.createTime')" prop="createTime" align="center">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.state')" prop="status" align="center" width="100">
        <template #default="scope">
          <el-tag type="danger" v-if="scope.row.status == 1">{{
            $t("common.delete")
            }}</el-tag>
          <el-tag type="danger" v-if="scope.row.status == 2">{{
            $t("common.freeze")
            }}</el-tag>
          <el-tag type="success" v-if="scope.row.status == 0">{{
            $t("channelManage.normal")
            }}</el-tag>
        </template>
      </el-table-column>

      <!-- <el-table-column prop="freezeTime" align="center" width="100" :label="$t('userManage.FrozenUntil')">
        <template v-slot="scope">
          <span v-if="scope.row.freezeTime > 3000000000000">{{
            $t("userManage.Forever")
            }}</span>
          <span v-else="scope.row.freezeTime">{{
            parseTime(scope.row.freezeTime)
            }}</span>
        </template>
      </el-table-column> -->
      <!-- <el-table-column :label="$t('common.operate')" align="center" width="180" class-name="small-padding fixed-width"
        fixed="right">
        <template #default="scope">
          <el-button v-if="scope.row.authStatus === 3" type="text" @click="cancelVerify(scope.row.id)">{{
            $t("channelReview.cancelVerify") }}</el-button>
          <el-button type="text" v-if="scope.row.status == 1" @click="showUnFreeze(scope.row)">{{ $t("common.unfreeze")
            }}</el-button>
          <el-button v-if="scope.row.status == 0 && scope.row.isOfficial == 0" type="text"
            @click="showFreeze(scope.row.id)">{{ $t("common.freeze") }}</el-button>
        </template>
      </el-table-column> -->
    </el-table>

    <el-dialog v-model="memberDialogVisivle" width="800px" :title="$t('customer.customer')">
      <MemberView v-if="memberDialogVisivle" :groupId="selectedId" />
    </el-dialog>
    <el-dialog v-model="postDialogVisivle" width="800px" :title="$t('customer.admin')">
      <AdminView v-if="postDialogVisivle" :groupId="selectedId" />
    </el-dialog>
    <el-dialog v-model="groupDialogVisivle" width="1500px" :title="$t('customer.conversation')">
      <GroupView v-if="groupDialogVisivle" :groupId="selectedId" />
    </el-dialog>
    <el-dialog v-if="state.moreDialogVisible" v-model="state.moreDialogVisible" width="400px" append-to-body
      custom-class="el-dialog-no-header">
      <Freeze :params="seleectedRowData" @close="closeMoreDialog" from="group"></Freeze>
    </el-dialog>

    <pagination v-show="state.total > 0" :total="state.total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />
  </div>
</template>

<script setup name="FundWithdrawalReview">
import { reactive, ref } from 'vue'
import { pageList } from '@/api/folder/folder'
import { ElMessage, ElMessageBox } from 'element-plus'
import Freeze from '@/views/consumer/components/freeze.vue'

import MemberView from '../customer/member.vue'
import AdminView from '../customer/admin.vue'
import GroupView from '../customer/group.vue'
import { groupFreeze } from '@/api/consumer/imuser'
import i18n from '../../../i18n'

// const host = ref(import.meta.env.VITE_APP_POST_HOST)

const queryFormRef = ref()

// 搜索参数
const queryParams = ref({
  customerName: null,
  customerUrl: null,
  pageNum: 1,
  pageSize: 10
})

const state = reactive({
  showSearch: true,
  getListLoading: false,
  tableData: [],
  total: 0,
  selectedIds: [],
  dialogVisible: false,
  dialogTitle: '',
  moreDialogVisible: false
})

// 获取列表数据
function getList() {
  state.getListLoading = true
  pageList(queryParams.value)
    .then((response) => {
      state.tableData = response.data.records
      state.total = response.data.total
    })
    .finally(() => {
      state.getListLoading = false
    })
}
// 查询数据
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}
// 重置查询
const resetQuery = (formEl) => {
  queryParams.value.pageNum = 1
  queryParams.value.customerName = null
  queryParams.value.customerUrl = null
  getList()
}

const memberDialogVisivle = ref(false)
const postDialogVisivle = ref(false)
const groupDialogVisivle = ref(false)
const selectedId = ref(null)
const showDialog = (id, type) => {
  if (type === 1) {
    memberDialogVisivle.value = true
  } else if (type === 2) {
    postDialogVisivle.value = true
  } else if (type === 3) {
    groupDialogVisivle.value = true
  }
  selectedId.value = id
}

// 操作弹窗
const seleectedRowData = ref({
  userId: null
})

function copyContent(text) {
  const InputDom = document.createElement('input')
  InputDom.value = text
  document.body.appendChild(InputDom)
  InputDom.select() // 选择对象
  document.execCommand('Copy') // 执行浏览器复制命令
  InputDom.remove()
  ElMessage.success(i18n.global.t('msg.copied'))
}

function closeMoreDialog(type) {
  state.moreDialogVisible = false
  if (type === 'init') {
    // state.tableData.map((i) => {
    //   if (i.id === seleectedRowData.value.userId) {
    //     i.status = 1;
    //   }
    //   return i;
    // });
    getList()
  }
}

function showFreeze(userId) {
  seleectedRowData.value.userId = userId
  state.moreDialogVisible = true
}

function showUnFreeze(data) {
  ElMessageBox.confirm(
    i18n.global.t('common.confirmUnfrozen'),
    i18n.global.t('common.tips'),
    {
      confirmButtonText: i18n.global.t('common.confirm'),
      cancelButtonText: i18n.global.t('common.cancel'),
      type: 'warning'
    }
  )
    .then(() => {
      const params = {
        freezeReason: 0,
        isFreeze: false,
        targetId: data.id,
        time: 0,
        unit: ''
      }
      groupFreeze(params).then((response) => {
        ElMessage({
          type: 'success',
          message: i18n.global.t('common.unfreezeMsg')
        })
        getList()
      })
    })
    .catch(() => {
      // ElMessage.error({
      //   message: '操作失败！'
      // })
    })
}

function cancelVerify(id) {
  ElMessageBox.confirm(
    i18n.global.t('common.confirmCancelChannelVerify'),
    i18n.global.t('common.tips'),
    {
      confirmButtonText: i18n.global.t('common.confirm'),
      cancelButtonText: i18n.global.t('common.cancel'),
      type: 'warning'
    }
  )
    .then(() => {
      const params = {
        channelId: id
      }
      cancelAuth(params).then(() => {
        ElMessage({
          type: 'success',
          message: i18n.global.t('common.cancelVerifyMsg')
        })
        getList()
      })
    })
    .catch(() => {
      // ElMessage.error({
      //   message: '操作失败！'
      // })
    })
}


getList()
</script>
<style scoped>
.img-msg {
  width: 60px;
  height: 60px;
  border-radius: 6px;
  background: #eee;
}
</style>
