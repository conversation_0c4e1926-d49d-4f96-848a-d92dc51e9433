<template>
  <div>
    <el-form :model="queryParams" ref="queryFormRef" :inline="true">
      <el-form-item :label="$t('common.userId')" prop="idNumber">
        <el-input v-model.trim="queryParams.idNumber" :placeholder="$t('common.pleaseEnter')" clearable
          style="width: 240px" @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('common.userNickname')" prop="name">
        <el-input v-model.trim="queryParams.name" :placeholder="$t('common.pleaseEnter')" clearable style="width: 240px"
          @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('common.phoneNumber')" prop="phone">
        <el-input v-model.trim="queryParams.phone" :placeholder="$t('common.pleaseEnter')" clearable
          style="width: 240px" @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">{{ $t('common.search') }}</el-button>
        <el-button icon="Refresh" @click="resetQuery(queryFormRef)">{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <!-- 表格数据 -->
    <el-table v-loading="state.getListLoading" :data="state.tableData">
      <el-table-column :label="$t('common.userId')" align="center" prop="idNumber" />
      <el-table-column :label="$t('common.userNickname')" align="center" prop="nickName" />
      <el-table-column :label="$t('common.phoneNumber')" align="center" prop="phone" />
      <el-table-column :label="$t('channelManage.avatar')" prop="headPortrait" align="center" width="100px">
        <template #default="scope">
          <el-image :src="scope.row.avatar" fit="contain" v-if="scope.row.avatar" class="img-msg" />
          <div v-else>{{$t('channelManage.noAvatar')}}</div>
        </template>
      </el-table-column>
      <!-- <el-table-column :label="$t('wallet.country')" align="center" prop="dialCode">
        <template #default="scope">
          <span>{{
            scope.row.dialCode ? countryCodeText(scope.row) : ""
            }}</span>
        </template>
      </el-table-column> -->
      <el-table-column :label="$t('customer.type')" prop="status" align="center" width="150">
        <template #default="scope">
          <el-tag type="plain" v-if="scope.row.type == 'NORMAL'">{{
            $t("customer.normal")
            }}</el-tag>
          <el-tag type="plain" v-if="scope.row.type == 'GUEST'">{{
            $t("customer.guest")
            }}</el-tag>
          <el-tag type="plain" v-if="scope.row.type == 'ROBOT'">{{
            $t("customer.robot")
            }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column :label="$t('customer.createTime')" align="center" prop="createTime">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="state.total > 0" :total="state.total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />
  </div>
</template>

<script setup name="FundWithdrawalReview">
import { reactive, ref, defineProps } from 'vue'
import { pageCustomer } from '@/api/folder/folder'
import { countryCode } from '@/utils/enum.js'
import i18n from "../../../i18n";

const props = defineProps(['groupId'])

const queryFormRef = ref()

// 搜索参数
const queryParams = ref({
  idNumber: null,
  name: null,
  phone: null,
  pageNum: 1,
  pageSize: 10
})

const state = reactive({
  getListLoading: true,
  tableData: [],
  total: 0
})

// 获取列表数据
function getList() {
  state.getListLoading = true
  const params = queryParams.value
  for (const key in params) {
    if (!params[key]) params[key] = null
  }
  pageCustomer({ ...params, folderId: props.groupId })
    .then((response) => {
      state.tableData = response.data.records
      state.total = response.data.total
    })
    .finally(() => {
      state.getListLoading = false
    })
}
// 查询数据
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}
// 重置查询
function resetQuery(formEl) {
  queryParams.value.pageNum = 1
  queryParams.value.idNumber = null
  queryParams.value.phone = null
  queryParams.value.name = null
  getList()
}

function countryCodeText(row) {
  const list = countryCode.filter((i) => i.tel === row.dialCode)
  let txt = ''
  if (list.length) {
    txt = i18n.global.locale === 'en' ? list[0].en : list[0].name
  }
  return txt
}

getList()
</script>
<style scoped>
.img-msg {
  width: 60px;
  height: 60px;
  border-radius: 6px;
  background: #eee;
}

:deep(.pagination-container) {
  display: flex;
  justify-content: center;
}

:deep(.pagination-container .el-pagination) {
  position: static;
}
</style>
