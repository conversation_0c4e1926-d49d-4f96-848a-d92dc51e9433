<template>
  <div class="app-container">
    <!-- form search -->
    <el-form :model="queryParams" :inline="true" ref="queryFormRef">
      <el-form-item :label="$t('crawlerManage.platform')" prop="platform">
        <el-select
          style="width: 150px"
          v-model="queryParams.platform"
          clearable
          class="m-2"
          :placeholder="$t('common.select')"
        >
          <el-option :label="$t('common.all')" value="" />
          <el-option label="telegram" value="telegram" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('crawlerManage.belongChannel')" prop="channelName">
        <el-input v-model="queryParams.channelName" :placeholder="$t('common.pleaseEnter')" />
      </el-form-item>
      <el-form-item :label="$t('crawlerManage.postTime')" prop="releaseTime">
        <el-date-picker
          v-model="queryParams.releaseTime"
          type="datetimerange"
          :range-separator="$t('common.to')"
          :start-placeholder="$t('common.startTime')"
          :end-placeholder="$t('common.endTime')"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>
      <el-form-item :label="$t('crawlerManage.crawlTime')" prop="spiderTime">
        <el-date-picker
          v-model="queryParams.spiderTime"
          type="datetimerange"
          :range-separator="$t('common.to')"
          :start-placeholder="$t('common.startTime')"
          :end-placeholder="$t('common.endTime')"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="onSearch"
          >{{ $t('common.search') }}</el-button
        >
        <el-button icon="Refresh" @click="resetForm(queryFormRef)">{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>
    <!-- form -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          :disabled="multiple"
          @click="pushMessage()"
          >{{ $t('common.post') }}</el-button
        >
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete(ids)"
          >{{ $t('common.delete') }}</el-button
        >
      </el-col>
    </el-row>
    <el-table
      v-loading="isLoading"
      :data="dataTable"
      style="width: 100%"
      @selection-change="handleTableSelectionChange"
    >
      <el-table-column type="selection" />
      <el-table-column
        :label="$t('crawlerManage.platform')"
        prop="platform"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        :label="$t('crawlerManage.belongChannel')"
        prop="channelName"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column :label="$t('crawlerManage.crawlContent')" align="center" width="174">
        <template #default="scope">
          <el-image
            v-if="scope.row.fileUrl && getImgUrl(scope.row.fileUrl)"
            class="img-msg"
            :src="getImgUrl(scope.row.fileUrl)"
            :preview-src-list="scope.row.fileUrl.split(',')"
            fit="contain"
          />
          <div v-else-if="scope.row.fileUrl">
            {{ getFileType(scope.row.fileUrl) }}
          </div>
          <div class="text-msg" v-else>{{ scope.row.message }}</div>
          <el-button type="text" @click="showDialog('详情', scope.row)"
            >{{ $t('crawlerManage.viewDetails') }}</el-button
          >
        </template>
      </el-table-column>
      <el-table-column :label="$t('crawlerManage.postTime')" align="center">
        <template #default="scope">{{ parseTime(scope.row.releaseTime) }}</template>
      </el-table-column>
      <el-table-column :label="$t('crawlerManage.postTimeOriginalPlatform')" align="center">
        <template #default="scope">{{ parseTime(scope.row.originalTime) }}</template>
      </el-table-column>
      <el-table-column :label="$t('crawlerManage.crawlTime')" align="center">
        <template #default="scope">{{ parseTime(scope.row.createTime) }}</template>
      </el-table-column>
      <el-table-column :label="$t('crawlerManage.postedTimes')" align="center" prop="releaseCount">
        <template #default="scope">
          <el-button type="text" @click="showDialog('发布记录', scope.row)">{{
            scope.row.releaseCount
          }}</el-button>
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.operate')" align="center">
        <template #default="scope">
          <el-button
            type="text"
            icon="Edit"
            @click="showDialog('编辑', scope.row)"
            >{{ $t('common.edit') }}</el-button
          >
          <el-button
            type="text"
            icon="Delete"
            @click="handleDelete([scope.row.id])"
            >{{ $t('common.delete') }}</el-button
          >
          <el-button
            type="text"
            icon="Plus"
            @click="showDialog('发布', [scope.row.id])"
            >{{ $t('common.post') }}</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- Modal -->
    <el-dialog
      v-if="state.dialogVisible"
      v-model="state.dialogVisible"
      :width="state.dialogWidth"
      append-to-body
      custom-class="el-dialog-no-header"
    >
      <ChannelAdd
        v-if="state.dialogType === '详情' || state.dialogType === '编辑'"
        :params="selectedRowData"
        @close="closeDialog"
      />
      <ChannelRecords
        v-if="state.dialogType === '发布记录'"
        :params="selectedRowData"
        @close="closeDialog"
      />
      <ChannelList
        v-if="state.dialogType === '发布'"
        :params="selectedRowData"
        @close="closeDialog"
      />
    </el-dialog>
  </div>
</template>

<script setup name="autoReplySetting">
import {
  deleteMessage,
  getLists
} from '@/api/channelOperations/channelOperations'
import { reactive, ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import ChannelList from './components/channelList.vue'
import ChannelAdd from './components/channelAdd.vue'
import ChannelRecords from './components/channelRecords.vue'
import i18n from "../../i18n";
const queryParams = ref({
  channelName: '',
  platform: '',
  releaseTime: [],
  spiderTime: [],
  pageNum: 1,
  pageSize: 10
})
const state = reactive({
  dialogVisible: false,
  dialogType: '',
  dialogWidth: '500px'
})
const selectedRowData = ref({})
const isLoading = ref(false)
const total = ref(0)
const dataTable = ref([])
const closeDialog = (type) => {
  state.dialogVisible = false
  state.dialogType = null
  if (type === '编辑' || type === '发布') {
    getList()
  }
}
// handle message
const handleMessageError = () => {
  ElMessage.error(i18n.global.t('common.pleaseSelectDeleteItem'))
}

// search
const onSearch = () => {
  getList()
}
// 重置查询
const queryFormRef = ref()
const resetForm = (formEl) => {
  if (!formEl) return
  console.log(formEl)
  formEl.resetFields()
  getList()
}
const ids = ref([])
const handleTableSelectionChange = (e) => {
  ids.value = e.map((item) => item.id)
}

function getFileType (fileUrls) {
  const urls = fileUrls.split(',')
  const fileType = urls[0].split('.').pop().toLowerCase()
  let messageType = ''
  if (fileType === 'pdf') {
    messageType = 'PDF ' + i18n.global.t('common.file')
  } else if (fileType === 'jpg' || fileType === 'jpeg' || fileType === 'png') {
    messageType = i18n.global.t('common.image')
  } else if (fileType === 'mp4') {
    messageType = i18n.global.t('common.video')
  } else if (fileType === 'doc' || fileType === 'docx') {
    messageType = 'Word '+ i18n.global.t('common.file')
  } else if (fileType === 'xls' || fileType === 'xlsx') {
    messageType = 'Excel ' + i18n.global.t('common.file')
  } else if (fileType === 'ppt' || fileType === 'pptx') {
    messageType = 'PowerPoint ' + i18n.global.t('common.file')
  } else {
    messageType = i18n.global.t('common.other')
  }
  return `【${messageType}】` // 移除最后一个逗号和空格
}
function getImgUrl (fileUrls) {
  const urls = fileUrls.split(',')
  const fileType = urls[0].split('.').pop().toLowerCase()
  if (fileType === 'jpg' || fileType === 'jpeg' || fileType === 'png') {
    return urls[0]
  } else {
    return false
  }
}
function handleDelete (data) {
  if (Object.keys(data).length === 0) {
    ElMessage.warning(i18n.global.t('common.pleaseSelectDeleteItem'))
    return
  }
  ElMessageBox.confirm(i18n.global.t('common.confirmDelete'), i18n.global.t('system.warning'), {
    confirmButtonText: i18n.global.t('common.confirm'),
    cancelButtonText: i18n.global.t('common.cancel'),
    type: 'warning'
  })
    .then(() => {
      const params = {
        ids: data
      }
      deleteMessage(params)
        .then(() => {
          ElMessage.success(i18n.global.t('common.success'))
          getList()
        })
        .catch(() => {
          handleMessageError()
        })
    })
    .catch(() => {
      // Do nothing if cancelled
    })
}

function pushMessage (data) {
  if (Object.keys(ids.value).length === 0) {
    ElMessage.warning(i18n.global.t('common.select'))
    return
  }
  console.log(ids.value)
  showDialog('发布', ids.value)
}

const showDialog = (dialogType, row) => {
  if (row) selectedRowData.value = { row: row, dialogType }
  state.dialogType = dialogType
  if (dialogType === '发布') state.dialogWidth = '1200px'
  else state.dialogWidth = '840px'
  state.dialogVisible = true
}

const getList = () => {
  isLoading.value = true
  const params = {
    ...queryParams.value
  }
  if (params.platform === '') delete params.platform
  if (params.releaseTime && params.releaseTime.length !== 0) {
    params.releaseStartTime = +new Date(params.releaseTime[0])
    params.releaseEndTime = +new Date(params.releaseTime[1])
  }
  if (params.spiderTime && params.spiderTime.length !== 0) {
    params.spiderStartTime = +new Date(params.spiderTime[0])
    params.spiderEndTime = +new Date(params.spiderTime[1])
  }
  getLists(params)
    .then((response) => {
      total.value = response.data.total
      dataTable.value = response.data.records
    })
    .finally(() => (isLoading.value = false))
}
getList()
</script>
<style>
.el-table__cell {
  position: static !important;
}
.img-msg {
  width: 150px;
  height: 100px;
  border-radius: 6px;
  background: #eee;
}
.text-msg {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.wrapper-form-dielog {
  display: flex;
  gap: 20px;
}

.wrapper-form-dielog .el-form-item__label {
  width: 70px;
}
</style>
