<template >
  <div class="login-record">
    <h3 class="dialog-title">选择频道运营人员</h3>
    <el-form :model="queryParams" ref="queryFormRef" :inline="true">


      <el-form-item label="名字" prop="title">
        <el-input
          v-model="queryParams.input"
          placeholder="名字"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
        >{{ $t('common.search') }}
        </el-button>
        <el-button icon="Refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <el-table
      :data="state.tableData"
      v-loading="state.getListLoading"
      style="width: 100%"
      @selection-change="handleTableSelectionChange"
      :row-key="getRowKey"
    >
      <el-table-column type="selection" width="55" align="center" :reserve-selection="true"/>
      <el-table-column
        label="名字"
        prop="name"
        align="center"

      />
      <el-table-column :label="$t('common.createdTime')" align="center" prop="createTime" >
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>

    </el-table>

    <div>
      <pagination
        v-show="state.total > 0"
        :total="state.total"
        v-model:page="state.pageNum"
        v-model:limit="state.pageSize"
        @pagination="getList"
      />
    </div>
    <div class="dialog-footer">
      <el-button @click="emit('close')">{{ $t('common.cancel') }}</el-button>
      <el-button type="primary" @click="submitForm">{{ $t('common.post') }}</el-button>
    </div>
  </div>

</template>

<script setup name="loginRecord">
import {defineEmits, defineProps, reactive, ref} from 'vue'
import {pageChannelMember} from '@/api/channelOperations/channelOperations'
import {countryCode} from '@/utils/enum.js'
import {ElMessage} from "element-plus";

const emit = defineEmits(['close','selectAll'])

const ids = ref([])
const handleTableSelectionChange = (e) => {
  ids.value = e.map((item) => item.id)
}
function getRowKey(row) {
  // console.log(row)
  return row.id
}

// 提交
function submitForm () {
  //关闭页面 把选择的ids传入添加的用户
  emit('submitForm', ids)
  emit('close')
}
// 全选
function selectAll() {
  var data = ref({});
  data.paramJson = JSON.stringify(selectUserParam.value)
  data.total = state.total
  //关闭弹窗 传所有入参的json到添加公告的importCondition入参
  emit('selectAll', data)
  emit('close')
}
const queryParams = ref({
  registraStartTime: null,
  registraEndTime: null,
  loginStartTime: null,
  loginEndTime: null,
  timeRanges: [],
  logOutTimeRanges: []
})

// 好友列表
const state = reactive({
  tableData: [],
  // getListLoading: false,
  // total: 100,
  pageNum: 1,
  pageSize: 10,
})
const selectUserParam = reactive({})
// 查询数据
function handleQuery() {
  getList()
}

const queryFormRef = ref()

// 重置查询
function resetQuery() {
  if (queryFormRef.value) {
    queryFormRef.value.resetFields()
  }
  queryParams.value.timeRanges = []
  queryParams.value.logOutTimeRanges = []
  queryParams.value.deviceType = null
  queryParams.value.useLanguage = null
  queryParams.value.dialCode = null
  queryParams.value.version = null
  handleQuery()
}

function countryCodeText(row) {
  const list = countryCode.filter((i) => i.shortName === row)
  let txt = ''
  if (list.length) {
    txt=i18n.global.locale === 'en' ? list[0].en : list[0].name
  }
  return txt
}
const props = defineProps({
  modelValue: String
})
function getList() {
  const params = {
    ...state,
    ...queryParams.value
  }
  selectUserParam.value = params
  selectUserParam.value.groupId = props.modelValue[0]
  console.log(selectUserParam.value,123)
  if (params.tableData) delete params.tableData
  pageChannelMember(params).then((response) => {
    state.tableData = response.data.records
    state.total = response.data.total
    selectUserParam.value.total =  response.data.total;
  })
}



getList()
</script>

<style lang="scss" scoped>
.login-record {
  position: relative;
  min-height: 500px;

  .footer-options {
    text-align: right;
  }
}
</style>
