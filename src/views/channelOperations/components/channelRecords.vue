<template>
  <div>
    <h3 class="dialog-title">{{ $t('crawlerManage.postRecord') }}</h3>
    <el-table
      v-loading="isLoading"
      :data="dataTable"
      style="width: 100%"
      @selection-change="handleTableSelectionChange"
    >
      <el-table-column :label="$t('crawlerManage.postTime')" align="center">
        <template #default="scope">{{ parseTime(scope.row.createTime) }}</template>
      </el-table-column>
      <el-table-column
        :label="$t('crawlerManage.channelName')"
        prop="channelName"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        :label="$t('crawlerManage.channelLink')"
        prop="url"
        align="center"
        width="250px"
        show-overflow-tooltip
      >
        <template #default="scope">{{ `${host}/${scope.row.groupNumber}` }}</template>
      </el-table-column>
      <el-table-column
        :label="$t('crawlerManage.subscribersCount')"
        prop="memberCount"
        align="center"
        show-overflow-tooltip
      >
      </el-table-column>
    </el-table>
    <pagination
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
      :pagerCount="4"
    />
  </div>
</template>

<script setup>
// 修改密码

import { defineProps, ref } from 'vue'
import { getSendRecords } from '@/api/channelOperations/channelOperations'
const host = ref(import.meta.env.VITE_APP_POST_HOST)
const props = defineProps({
  params: Object
})
const isLoading = ref(false)
const total = ref(0)
const dataTable = ref([])
const queryParams = ref({
  pageNum: 1,
  pageSize: 10
})

const getList = () => {
  console.log(host)
  isLoading.value = true
  getSendRecords({
    ...queryParams.value,
    tgDataId: props.params.row.id
  })
    .then((response) => {
      total.value = response.data.total
      dataTable.value = response.data.records
    })
    .finally(() => (isLoading.value = false))
}
getList()
</script>

<style lang="scss" scoped>
:deep(.pagination-container) {
  display: flex;
  justify-content: center;
}
:deep(.pagination-container .el-pagination) {
  position: static;
}
.el-table__cell {
  position: static !important;
}
</style>
