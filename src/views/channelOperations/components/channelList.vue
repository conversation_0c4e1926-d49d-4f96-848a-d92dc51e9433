<template>
  <div class="login-record">
    <h3 class="dialog-title">{{ $t('crawlerManage.selectChannel') }}</h3>
    <el-form :model="queryParams" ref="queryFormRef" :inline="true">
      <el-form-item :label="$t('crawlerManage.channelName')" prop="input">
        <el-input
          v-model="queryParams.input"
          :placeholder="$t('crawlerManage.enterChannelName')"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >{{ $t('common.search') }}
        </el-button>
        <el-button icon="Refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <el-table
      :data="state.tableData"
      v-loading="state.getListLoading"
      style="width: 100%"
      @selection-change="handleTableSelectionChange"
      :row-key="getRowKey"
    >
      <el-table-column
        type="selection"
        width="55"
        align="center"
        :reserve-selection="true"
      />
      <el-table-column :label="$t('crawlerManage.channelName')" prop="name" align="center" />
      <el-table-column :label="$t('crawlerManage.subscribers')" prop="memberCount" align="center" />
      <el-table-column :label="$t('common.createdTime')" align="center" prop="createTime">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
    </el-table>

    <div>
      <pagination
        v-show="state.total > 0"
        :total="state.total"
        v-model:page="state.pageNum"
        v-model:limit="state.pageSize"
        @pagination="getList"
      />
    </div>
    <div class="footer-options">
      <el-button @click="emit('close')">{{ $t('common.cancel') }}</el-button>
      <el-button type="primary" @click="submitForm">{{ $t('common.confirm') }}</el-button>
    </div>
  </div>
</template>

<script setup name="loginRecord">
import { reactive, ref, defineProps, defineEmits } from 'vue'
import {
  release,
  pageChannelList
} from '@/api/channelOperations/channelOperations'
import { ElMessage, ElMessageBox } from 'element-plus'
import i18n from "../../../i18n";

const props = defineProps({
  params: Object
})
const emit = defineEmits(['close'])

const ids = ref([])
const handleTableSelectionChange = (e) => {
  ids.value = e.map((item) => item.id)
  console.log(ids.value)
}
function getRowKey (row) {
  return row.id
}
// 提交
function submitForm () {
  if (ids.value.length === 0) {
    ElMessage.error(i18n.global.t('common.pleaseSelectChannel'))
    return
  }
  const params = {
    tgDataIds: props.params.row,
    conversationIds: ids.value.join(',')
  }
  // 调用发布接口
  ElMessageBox.confirm(i18n.global.t('common.confirmPost'), i18n.global.t('system.warning'), {
    confirmButtonText: i18n.global.t('common.confirm'),
    cancelButtonText: i18n.global.t('common.cancel'),
    type: 'warning'
  })
    .then(() => {
      release(params)
        .then((res) => {
          console.log(res)
          if (res.data) {
            ElMessage.success(i18n.global.t('common.success'))
            emit('close', '发布')
          } else {
            ElMessage.error(i18n.global.t('common.failed'))
          }
        })
        .catch(() => {})
    })
    .catch(() => {
      // Do nothing if cancelled
    })
}
const queryParams = ref({
  input: ''
})

// 列表
const state = reactive({
  getListLoading: false,
  tableData: [],
  pageNum: 1,
  pageSize: 10
})
// 查询数据
function handleQuery () {
  getList()
}

const queryFormRef = ref()

// 重置查询
function resetQuery () {
  if (queryFormRef.value) {
    queryFormRef.value.resetFields()
  }
  handleQuery()
}

function getList () {
  const params = {
    ...state,
    ...queryParams.value
  }
  if (params.tableData) delete params.tableData
  state.getListLoading = true
  pageChannelList(params)
    .then((response) => {
      state.tableData = response.data.records
      state.total = response.data.total
    })
    .finally(() => (state.getListLoading = false))
}

getList()
</script>

<style lang="scss" scoped>
:deep(.pagination-container) {
  display: flex;
  justify-content: center;
}
:deep(.pagination-container .el-pagination) {
  position: static;
}
.footer-options {
  text-align: right;
}
</style>
