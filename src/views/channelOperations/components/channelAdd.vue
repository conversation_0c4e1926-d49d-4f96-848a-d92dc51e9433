<template>
  <div>
    <h3 class="dialog-title"> {{ props.params.dialogType==='详情'?$t('common.details'):$t('common.edit')}}</h3>
    <el-form
      ref="formRef"
      :model="formParams"
      :rules="rules"
      label-width="140px"
      :disabled="props.params.dialogType === '详情'"
    >
      <el-form-item :label="$t('crawlerManage.channelName')" prop="channelName">
        <el-input v-model="formParams.channelName" />
      </el-form-item>
      <el-form-item :label="$t('crawlerManage.messageContent')" prop="message">
        <el-input
          v-model="formParams.message"
          type="textarea"
          :autosize="{ minRows: 4, maxRows: 8 }"
          maxlength="1000"
          show-word-limit
        />
      </el-form-item>

      <el-form-item
        :label="$t('crawlerManage.file')"
        prop="fileUrl"
        v-if="formParams.fileUrl && formParams.fileUrl !== null"
      >
        <template v-if="formParams.fileUrl">
          <div style="width: 100%">
            <template
              v-for="(url, index) in formParams.fileUrl.split(',')"
              :key="index"
            >
              <video
                v-if="getFileExtension(url) === 'mp4'"
                controls
                class="img-video"
              >
                <source :src="url" type="video/mp4" />
              </video>
              <!-- 在这里显示PDF文件下载链接 -->
              <a
                v-else-if="getFileExtension(url) === 'pdf'"
                :href="url"
                download
                >下载PDF文件</a
              >
              <!-- 否则，显示图片组件 -->
              <el-image
                class="img-msg"
                v-else-if="
                  ['jpg', 'jpeg', 'png', 'gif'].includes(getFileExtension(url))
                "
                :src="url"
                fit="contain"
                :initial-index="
                  formParams.fileUrl
                    .split(',')
                    .filter((i) =>
                      ['jpg', 'jpeg', 'png', 'gif'].includes(
                        getFileExtension(i)
                      )
                    )
                    .findIndex((j) => j === url)
                "
                :preview-src-list="
                  formParams.fileUrl
                    .split(',')
                    .filter((i) =>
                      ['jpg', 'jpeg', 'png', 'gif'].includes(
                        getFileExtension(i)
                      )
                    )
                "
              />
              <div v-else>{{ url }}</div>
            </template>
          </div>
        </template>
      </el-form-item>
    </el-form>
    <div class="footer-options">
      <el-button
        type="primary"
        @click="HandleAdd"
        :loading="state.submitLoading"
        v-if="props.params.dialogType === '编辑'"
        >{{ $t('common.confirm') }}</el-button
      >
      <el-button @click="emit('close')">{{ $t('common.cancel') }}</el-button>
    </div>
  </div>
</template>

<script setup>
import { AddData } from '@/api/channelOperations/channelOperations'
import {
  reactive,
  getCurrentInstance,
  defineEmits,
  defineProps,
  ref
} from 'vue'
import { ElMessage } from 'element-plus'
import i18n from "../../../i18n";
// import { sendMessage } from '@/api/consumer/imuser'
const { proxy } = getCurrentInstance()
const rules = reactive({
  type: [{ required: true, message: i18n.global.t('common.cannotBeEmpty'), trigger: 'change' }],
  name: [{ required: true, message: i18n.global.t('common.cannotBeEmpty'), trigger: 'change' }],
  link: [{ required: true, message: i18n.global.t('common.cannotBeEmpty'), trigger: 'change' }]
})
const props = defineProps({
  params: Object
})

const formParams = ref({
  ...props.params.row
})
const state = reactive({
  submitLoading: false
})

const emit = defineEmits(['close'])

// 获取文件后缀名
function getFileExtension (filename) {
  return filename.split('.').pop().toLowerCase()
}

const HandleAdd = () => {
  proxy.$refs.formRef.validate((valid) => {
    if (valid) {
      state.submitLoading = true
      const params = formParams.value
      AddData(params)
        .then((res) => {
          if (res.data) {
            ElMessage.success(i18n.global.t('common.success'))
            emit('close', '编辑')
          } else {
            ElMessage.error(i18n.global.t('common.failed'))
          }
        })
        .finally(() => {
          state.submitLoading = false
        })
    }
  })
}
</script>

<style lang="scss" scoped>
.img-msg {
  width: 235px;
  height: 180px;
  margin-right: 5px;
  background: #eee;
  border-radius: 5px;
}
.img-video {
  width: 235px;
  height: 180px;
  margin-right: 5px;
  border-radius: 5px;
}
.footer-options {
  text-align: right;
}
</style>
