
<template>
  <div class="msg-type-view cont" v-loading="loading">
    <p v-if="props.selectedRowData.content" style="margin-bottom: 10px">
      {{ props.selectedRowData.content }}
    </p>
    <div
      class="pic-list"
      v-if="[-2, 23, -8, 29].includes(contentJson.type)"
    >
      <el-image
        v-for="(file, idx) in contentJson.files"
        :key="idx"
        :src="file.url"
        :zoom-rate="1.2"
        :preview-src-list="getSrcList(contentJson.files, idx)"
        fit="cover"
      />
    </div>
        <div
      class="pic-list"
      v-if="[-6, 27].includes(contentJson.type) && contentJson.isReport"
    >
      <el-image
        v-for="(file, idx) in contentJson.files"
        :key="idx"
        :src="file.url"
        :zoom-rate="1.2"
        :preview-src-list="getSrcList(contentJson.files, idx)"
        fit="cover"
      />
    </div>
    <el-image
      :src="faceMadeUrl(contentJson)"
      style="width: 100px"
      :preview-src-list="[faceMadeUrl(contentJson)]"
      v-if="contentJson.type === 15"
    ></el-image>
    <el-image 
      :src="JSON.parse(contentJson.attrs.giphyFace).url"
      style="width: 100px"
      :preview-src-list="[JSON.parse(contentJson.attrs.giphyFace).url]"
      v-if="contentJson.type === 155"
    ></el-image>
    
    <!-- 添加 AI 问候语问题列表展示 -->
    <div class="qa-list" v-if="contentJson.type === 6026">
      <div v-for="(item, index) in JSON.parse(contentJson.attrs.qaList)" 
           :key="index" 
           class="qa-item"
           style="margin: 8px 0">
        {{ index + 1 }}. {{ item.name }}
      </div>
    </div>

    <!-- 添加富文本链接详情展示 -->
    <div v-if="[-8, 29].includes(contentJson.type)">
      <a
          :href="contentJson.link"
          :style="{ color: 'blue', textDecoration: 'underline' }"
          target="_blank"
          >{{ contentJson.link }}</a
        >
        <a
          :href="props.selectedRowData.link"
          :style="{ color: 'blue', textDecoration: 'underline' }"
          target="_blank"
          >{{ props.selectedRowData.link }}</a
        >
    </div>

    <div
      class="download-file"
      v-if="contentJson.type === -5 || contentJson.type === 25"
    >
      <div
        class="file-item"
        v-for="(file, idx) in contentJson.files"
        :key="idx"
      >
        <a class="filename" :href="file.url" target="_blank" >{{ file.url }}</a>
        <a href="javascript:;" class="down-btn" @click="downloadFile(file)">{{
          $t("user.download")
        }}</a>
      </div>
    </div>
    <div
      class="download-file"
      v-if="
        [-6, -7, 27, 28].includes(contentJson.type) && !contentJson.isReport
      "
    >
      <div
        class="file-item"
        v-for="(url, idx) in contentJson.sourceUrlList"
        :key="idx"
      >
        <a
          :href="url"
          :style="{ color: 'blue', textDecoration: 'underline' }"
          target="_blank"
          >{{ url }}</a
        >
      </div>
    </div>
    <div class="person-card" v-if="contentJson.type === 11">
      <img :src="contentJson.attrs.avatar" alt="" class="avatar" />
      <div class="card-cont">
        <div class="name" v-if="contentJson.attrs.userName">
          {{ contentJson.attrs.userName }}
        </div>
        <div class="tel" v-if="contentJson.attrs.phone">
          +{{ contentJson.attrs.dialCode }} {{ contentJson.attrs.phone }}
        </div>
      </div>
    </div>
    <div class="address-info" v-if="contentJson.type === 12">
      <p class="info">{{ contentJson.attrs.addressDetail }}</p>
      <p class="lat">
        （lat: {{ contentJson.attrs.latitude }}, lng:
        {{ contentJson.attrs.longitude }}）
      </p>
    </div>
    <div
      class="record-info"
      v-if="contentJson.type === 14"
      @click="recordListHander(contentJson.attrs)"
    >
      <div class="title">{{ recrodTitle(contentJson.attrs) }}</div>
      <div
        class="info"
        v-for="(record, idx) in JSON.parse(contentJson.attrs.recordContent)"
        :key="idx"
      >
        <p v-if="idx < 4">{{ record.name }}: {{ recordContentText(record) }}</p>
      </div>
    </div>
    <div
      class="video-list"
      v-if="contentJson.files && contentJson.files[0]?.url"
    >
      <video
        width="320"
        height="240"
        controls
        v-if="
          [-4, 24].includes(contentJson.type)
        "
      >
        <source :src="contentJson.files[0].url" type="video/mp4" />
        Your browser does not support the video tag.
      </video>

      <video
        width="320"
        height="240"
        controls
        v-if="
          [-7, 28].includes(contentJson.type) && contentJson.isReport
        "
      >
        <source :src="contentJson.files[0].url" type="video/mp4" />
        Your browser does not support the video tag.
      </video>
      <audio controls v-if="contentJson.type === -3 || contentJson.type === 22">
        <source :src="contentJson.files[0].url" type="audio/mpeg" />
        Your browser does not support the audio tag.
      </audio>
      <!-- <div class="name">{{ contentJson.files[0].name }}</div> -->
    </div>

    <div
      class="address-info"
      v-if="contentJson.type === 1000 || contentJson.type === 1001"
    >
      <p class="info">{{ $t("msg.audioVideo") }}</p>
    </div>

    <el-dialog
      v-model="visible"
      width="80%"
      :title="$t('msg.chatHistory')"
      append-to-body
      :before-close="beforeClose"
      custom-class="el-dialog-no-header"
    >
      <History
        :selectedRowData="selectedRowData"
        :recordList="recordList"
        v-loading="loading"
      />
    </el-dialog>
  </div>
</template>

<script setup name="MsgTypeView">
import { ref, defineProps, defineEmits, watch } from "vue";
import { randomNum, formatDate } from "@/utils/index";
import { handleUrl } from "@/utils/encrypt";
import { getListByIds } from "@/api/consumer/imuser.js";
import History from "./History.vue";
import axios from "axios";
import i18n from "../../../i18n";

const props = defineProps({
  selectedRowData: Object,
});
console.log(props.selectedRowData);

const emit = defineEmits(["close"]);
const contentJson = ref();
const loading = ref(true);
const visible = ref(false);
const recordList = ref();
const msgLoading = ref(false);
// const state = reactive({
//   contentJson: JSON.parse(props.selectedRowData.contentJson)
// })
watch(
  () => props.selectedRowData,
  (val) => {
    if (typeof val.contentJson === "string") {
      contentJson.value = JSON.parse(val.contentJson);
    } else {
      contentJson.value = { ...val.contentJson };
    }
    console.log(contentJson.value);
    if ([-6, -7, 27, 28].includes(contentJson.value.type)) {
      const sourceUrlList = [];
      contentJson.value.files.forEach((element) => {
        const url = element.sourceUrl ? element.sourceUrl : element.url;
        if (url && !sourceUrlList.includes(url)) {
          sourceUrlList.push(url);
        }
      });
      contentJson.value.sourceUrlList = sourceUrlList;
    }
    loading.value = false;
  },
  {
    deep: true,
    immediate: true,
  }
);

function faceMadeUrl(item) {
  console.log(item);
  let url = "";
  if (item.attrs) {
    // 地址生成规则：存储域名/桶名 / md5的末尾三位 / md5的第二个末尾三位 / jpeg_md5（png_md5、gif_md5）具体值
    // 如：https://test-file.wecloud.cn/uim-phiz/a49/53a/fec34cb3fbb54952fee445753953aa49.jpg
    const { faceMd5, faceType, faceWidth, faceHeight } = item.attrs;
    const host = import.meta.env.VITE_APP_MINIO_ENDPOINT;
    const addr1 = "mos-phiz";
    const addr2 = faceMd5.substr(-3);
    const addr3 = faceMd5.substr(-6, 3);
    url = `${host}/${addr1}/${addr2}/${addr3}/${faceMd5}.${faceType}`;
  } else {
    url = item.url;
  }
  // console.log(url)
  return url;
}

// const url = 'https://oss-test.metathought.cc/mos/357/796/427694378796357/a0e70467-9d57-40e5-a6bf-c2d69c071d9a.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=aF9w1MN81j8MMNxE%2F20230613%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20230613T111725Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=211fe3e9f4cc8643cf89c56f0180caac8ea719426f27e7064e475a35f498ffd1'
function downloadFile(file) {
  const url = file.url;
  window.location.assign(url);
  // const x = new window.XMLHttpRequest()
  // x.open('GET', url, true)
  // x.responseType = 'blob'
  // x.onload = () => {
  //   // const begin = url.lastIndexOf('.')
  //   // const str = url.slice(begin)
  //   // const end = str.indexOf('?')
  //   // let extensionName = ''
  //   // if (end > -1) {
  //   //   extensionName = str.slice(1, end)
  //   // }
  //   const content = window.URL.createObjectURL(x.response)
  //   const a = document.createElement('a')
  //   a.href = content
  //   a.download = file.name ? `${file.name}` : randomNum(30)
  //   document.body.appendChild(a) // 兼容火狐
  //   a.click()
  //   document.body.removeChild(a) // 移除a标签
  //   window.URL.revokeObjectURL(content) // 释放content对象
  // }
  // x.send()
}

function beforeClose() {
  visible.value = false;
}

function getSrcList(files, index) {
  const urls = files.map((i) => i.url);
  return urls.slice(index).concat(urls.slice(0, index));
}

function recrodTitle(attrs) {
  let txt = i18n.global.t("msg.chatHistory");
  if (
    attrs.recordSourceConvType === "1" ||
    attrs.recordSourceConvType === "6"
  ) {
    txt = i18n.global.t("user.user") + ` ${attrs.recordSourceConvName} ${txt}`;
  }
  if (attrs.recordSourceConvType === "2") {
    txt = i18n.global.t("user.group") + ` ${txt}`;
  }
  if (attrs.recordSourceConvType === "7") {
    txt = i18n.global.t("user.channel") + ` ${txt}`;
  }
  return txt;
}

function getUserInfo(id) {
  if (msgLoading.value) {
    return;
  }
  return new Promise((resolve, reject) => {
    msgLoading.value = true;
    const params = {
      userIds: [id],
    };
    getListByIds(params)
      .then((res) => {
        resolve(res.data);
      })
      .finally(() => {
        msgLoading.value = false;
      });
  });
}

function recordListHander(attrs) {
  const list = JSON.parse(attrs.recordContent);
  loading.value = true;
  axios
    .get(attrs.recordFile)
    .then(async (res) => {
      const recordFile = res.data;
      for (const i in recordFile) {
        const item = recordFile[i];
        if (item.files?.length) {
          item.files = await handleUrl(item.files);
        }
      }
      recordList.value = res.data.map((i, idx) => {
        // const info = await getUserInfo(i.sender)
        // console.log(info)
        console.log(i, props.selectedRowData);
        return {
          // headPortrait: props.selectedRowData.headPortrait,
          type: i.type,
          contentJson: i,
          content: i.text,
          fromUserName: list[idx].name,
          fromUserId: i.sender,
          createTime: formatDate(new Date(i.createTime)),
          files: i.files,
        };
      });
      loading.value = false;
      visible.value = true;
    })
    .finally(() => {
      loading.value = false;
    });
}

function recordContentText(record) {
  let txt = record.content;
  if (record.msgType === 15) {
    txt = "[自定义表情]";
  }
  return txt;
}
</script>

<style lang="scss" scoped>
.msg-type-view {
  .pic-list {
    display: flex;
    flex-wrap: wrap;
    width: 390px;
    .el-image {
      width: 120px;
      height: 120px;
      // margin-bottom: 10px;
      margin: 0 10px 10px 0;
    }
  }
  .video-list {
    .name {
      font-size: 12px;
      margin-top: 20px;
      text-align: center;
    }
  }
  .person-card {
    display: flex;
    width: 200px;
    // border: 1px solid #ddd;
    padding: 10px;
    border-radius: 8px;
    background: #f5f5f5;
    box-shadow: 0 0 2px rgba(0, 0, 0, 0.1);
    .avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      border: 2px solid #fff;
    }
    .card-cont {
      margin-left: 5px;
      .name {
        color: #333;
        font-weight: 600;
        font-size: 14px;
        margin-top: 5px;
      }
      .tel {
        font-size: 12px;
        color: #999;
        margin-top: 5px;
      }
    }
  }
  .address-info {
    .info {
      font-size: 14px;
      font-weight: 600;
      color: #666;
    }
    .lat {
      color: #999;
      margin-top: 8px;
    }
  }
  .record-info {
    width: 200px;
    padding: 10px;
    border-radius: 8px;
    background: #f5f5f5;
    box-shadow: 0 0 2px rgba(0, 0, 0, 0.1);
    .title {
      color: #333;
      font-size: 14px;
      margin-bottom: 10px;
    }
    .info {
      color: #666;
      font-size: 12px;
      p {
        margin-top: 5px;
      }
    }
  }
}
.download-file {
  .file-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    .filename {
      width: 80%;
      height: 30px;
      display: inline-block;
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
      word-spacing: normal;
    }
  }
  .down-btn {
    color: #3291f8;
    margin-left: 20px;
    text-decoration: underline;
    cursor: pointer;
  }
}
</style>

