<template>
  <div v-if="state.detailInfo">
    <el-form
      ref="addBannerFormRef"
      :model="state.detailInfo"
      :rules="addBannerFormRules"
      label-width="150px"
    >
      <el-row>
        <el-col :span="14">
          <el-form-item :label="$t('mosAssist.nickname')" prop="nickname">
            {{ state.detailInfo.nickname }}
          </el-form-item>
          <el-form-item :label="$t('common.phoneNumber')" prop="phone">
            <span v-if="state.detailInfo.phone.includes('_')"
            >{{ `+${state.detailInfo.dialCode} ${state.detailInfo.phone.split('_')[1]}` }}</span
          >
          <span v-else>{{ `+${state.detailInfo.dialCode} ${state.detailInfo.phone}` }}</span>
          </el-form-item>
          <el-form-item :label="$t('common.userId')" prop="id">
            {{ state.detailInfo.idNumber }}
          </el-form-item>
          <el-form-item :label="$t('mosAssist.feedbackMessage')" prop="createTime">
            <el-button type="link" size="small" @click="histroyRecordHanlder(state.detailInfo)">{{$t('mosAssist.viewMore')}}</el-button>
          </el-form-item>
          <el-form-item :label="$t('common.state')" prop="helperStatus">
            <span v-if="state.detailInfo.helperStatus === 0" style="color: #3291f8;">{{$t('mosAssist.pending')}}</span>
            <span v-if="state.detailInfo.helperStatus === 1">{{$t('mosAssist.processed')}}</span>
          </el-form-item>
          <el-form-item :label="$t('mosAssist.addTime')" prop="createTime">
          {{  parseTime(state.detailInfo.createTime)}}
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="footer-options">
      <el-button type="primary" @click="emit('close')">{{ $t('common.confirm') }}</el-button>
    </div>

  </div>
</template>

<script setup name="assistantInfo">
import { reactive, defineProps, defineEmits } from 'vue'

const props = defineProps({
  selectedRowData: Object
})

const emit = defineEmits(['close', 'review'])
const state = reactive({
  detailInfo: props.selectedRowData
})

function histroyRecordHanlder (row) {
  emit('review', row)
}
</script>

<style lang="scss" scoped>
.footer-options {
  text-align: right;
}
</style>
