<template>
  <div v-loading="state.loading">
    <!-- <h3 class="dialog-title">反馈信息</h3> -->
    <div class="record-list" v-if="state.dataList && state.dataList.length"
      style="max-height: 600px; overflow-y: auto;">
      <div class="record-unit" v-for="(item, i) in state.dataList" :key="i">
        <div class="record-lf" v-if="item.headPortrait">
          <div class="avatar">
            <img :src="item.headPortrait" alt="" srcset="" />
          </div>
        </div>
        <div class="record-mid">
          <div class="name">{{item.fromUserName}}
            <!-- <span>({{item.fromUserId}}）</span> -->
            </div>
          <!-- <div class="from" v-if="item.contentJson.attrs && item.contentJson.attrs.fromConvChatType === '7'">
            转发自[{{item.contentJson.attrs && item.contentJson.attrs.fromConvShowName}}频道]
          </div> -->
          <div class="cont">
            <div v-if="item.deleted===1">{{$t('mosAssist.messageDeleted')}}</div>
            <MsgTypeView v-else :selectedRowData="item"></MsgTypeView>
          </div>
        </div>
        <div class="record-rh">
          <div class="time">{{parseTime(item.createTime)}}</div>
          <!-- <div class="btns">
            <el-button type="primary" size="small">查看频道</el-button>
          </div> -->
        </div>
      </div>
      <div class="more-btn" @click="changePageNum" v-if="state.visibleMore">{{ $t('common.viewMore') }}</div>
    </div>
    <div v-else>
      <el-empty :description="$t('system.noDataAvailable')" />
    </div>
  </div>
</template>

<script setup name="assistantHistory">
import { reactive, defineProps, defineEmits } from 'vue'
import MsgTypeView from '@/views/assistant/components/MsgTypeView.vue'

import {
  historyHelperMessage
} from '@/api/message/message'
import i18n from '@/i18n'
import mosAssistant from '@/assets/images/mos-assistant.png'
const mosAssistantImg = reactive(mosAssistant)
const props = defineProps({
  selectedRowData: Object,
  recordList: Array
})
console.log(props.selectedRowData)

const emit = defineEmits(['close'])
const state = reactive({
  detailInfo: props.selectedRowData,
  dataList: props.recordList || [],
  pageNum: 1,
  pageSize: 5,
  pageTotal: 0,
  visibleMore: true,
  loading: false
})

function getHistoryHelperMessage () {
  state.loading = true
  const params = {
    fromUserId: state.detailInfo.id,
    pageNum: state.pageNum,
    pageSize: state.pageSize
  }
  historyHelperMessage(params).then((res) => {
    if (res.code === 200) {
      const { records, total } = res.data
      const list = records.map(i => {
        if (i.fromUserId === '888888') {
          i.headPortrait = mosAssistantImg
          i.fromUserName = i18n.global.t('mosAssist.mosAssistant')
        } else {
          i.headPortrait = state.detailInfo.headPortrait
        }
        i.contentJsonbak = i.contentJson
        i.contentJson = JSON.parse(i.contentJson)
        if (i.msgType === -2) {
          // i.files = JSON.parse(i.contentJson).files
        }
        return i
      })
      state.dataList = [...state.dataList, ...list]
      state.pageTotal = total
      if (state.dataList.length === total) {
        state.visibleMore = false
      }
    }
    state.loading = false
  }).catch(err => {
    console.log(err)
    state.loading = false
  })
}

function changePageNum () {
  if (state.pageTotal > state.pageNum * state.pageSize) {
    state.pageNum++
    getHistoryHelperMessage()
  } else {
    state.visibleMore = false
  }
}

console.log(props.recordList)
if (!props.recordList) {
  getHistoryHelperMessage()
} else {
  // 聊天记录不需要展示更多
  state.visibleMore = false
  console.log(props.recordList)
}
</script>

<style lang="scss" scoped>
.footer-options {
  text-align: right;
}
.record-list{
  padding-top: 20px;
}
.record-unit{
  display: flex;
  padding: 10px 0;
  border-bottom: 1px solid #f3f3f3;
  // justify-content: space-between;
  .record-lf{
    .avatar img{
      width: 50px;
      height: 50px;
      border-radius: 50%;
    }
  }
  .record-mid{
    margin: 0 20px 0 10px;
    flex: 1;
    .name{
      margin-bottom: 10px;
      color: #555;
      font-weight: 600;
      font-size: 16px;
      span{
        color: #999;
        font-weight: 400;
      }
    }
    .from{
      color: #3291f8;
    }
    .cont{
      margin-top: 5px;
      font-family: Helvetica Neue, sans-serif;
      white-space: pre-wrap;
      p{
        color: #101010;
      }
      .pic{
        margin-top: 5px;
        width: 310px;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
      }
      .el-image{
        margin-bottom: 5px;
      }
    }
  }
  .record-rh{
    text-align: right;
    .time{
      color: #9a9a9a;
      margin-bottom: 10px;
    }
  }
}
.more-btn{
  text-align: center;
  margin-top: 30px;
  cursor: pointer;
  color: #3291f8;
}
</style>
