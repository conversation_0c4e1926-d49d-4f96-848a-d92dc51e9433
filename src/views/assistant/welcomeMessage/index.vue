<template>
    <div class="app-container">
        <el-form :model="queryParams" v-show="showSearch" ref="queryFormRef" :inline="true" >
            <el-form-item :label="$t('common.caption')" prop="title">
                <el-input v-model="queryParams.title" :placeholder="$t('common.pleaseEnter')" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item :label="$t('mosAssist.welcomeType')" prop="greetingType">
                <el-select style="width: 130px" v-model="queryParams.greetingType" clearable class="m-2" :placeholder="$t('common.select')">
                    <el-option :label="$t('common.text')" :value="1" />
                    <el-option :label="$t('common.image')" :value="2" />
                </el-select>
            </el-form-item>
            <el-form-item :label="$t('mosAssist.createdTimeRange')" prop="times">
                <el-date-picker v-model="queryParams.times" type="daterange" :range-separator="$t('common.to')" :start-placeholder="$t('common.startTime')"
                    :end-placeholder="$t('common.endTime')" value-format="YYYY-MM-DD" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">{{ $t('common.search') }}</el-button>
                <el-button icon="Refresh" @click="resetForm(queryFormRef)">{{ $t('common.reset') }}</el-button>
            </el-form-item>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="Plus" @click="handleAdd(addBannerFormRef)">{{ $t('common.add') }}</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete(ids)"
                    v-hasPermi="['system:notice:remove']">{{ $t('common.delete') }}</el-button>
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <el-table v-loading="getListLoading" :data="tableData" @selection-change="handleTableSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="ID" align="center" prop="id" width="150"/>
            <el-table-column :label="$t('common.caption')" prop="title" align="center">
                <template #default="scope">
                    {{ limitWordCounts(scope.row.title, 'arrayString', 20) }}
                </template>
            </el-table-column>
            <el-table-column :label="$t('mosAssist.language')" align="center" prop="lang" :show-overflow-tooltip="true" width="100">
                <template #default="scope">
                    <span v-if="scope.row.lang === 0">{{ $t('mosAssist.chinese')}}</span>
                    <span v-if="scope.row.lang === 1">{{ $t('mosAssist.english')}}</span>
                    <span v-if="scope.row.lang === 2">{{ $t('mosAssist.all')}}</span>
                </template>
            </el-table-column>
            <el-table-column :label="$t('mosAssist.welcomeType')" align="center" prop="greetingType" :show-overflow-tooltip="true" width="100">
                <template #default="scope">
                    <span v-if="scope.row.greetingType === 1">{{ $t('common.text') }}</span>
                    <span v-if="scope.row.greetingType === 2">{{ $t('common.image') }}</span>
                </template>
            </el-table-column>
            <el-table-column :label="$t('mosAssist.likes')" align="center" prop="likeNums" :show-overflow-tooltip="true" width="100" />
            <el-table-column :label="$t('mosAssist.views')" align="center" prop="state" width="100">
                <template #default="scope">
                    <span>{{ scope.row.readNums }}</span>
                </template>
            </el-table-column>
            <el-table-column :label="$t('mosAssist.publishedState')" align="center" prop="state" width="100">
                <template #default="scope">
                    <span v-if="scope.row.state === 0">{{$t('mosAssist.unpublished')}}</span>
                    <span v-if="scope.row.state === 1">{{$t('mosAssist.published')}}</span>
                </template>
            </el-table-column>
            <el-table-column :label="$t('mosAssist.creator')" prop="author" align="center" width="200">
                <template #default="scope">
                    {{ limitWordCounts(scope.row.author, 'arrayString', 20) }}
                </template>
            </el-table-column>
            <el-table-column :label="$t('common.createdTime')" align="center" prop="createTime" width="200">
                <template #default="scope">
                    <span>{{
                        parseTime(scope.row.createTime, "{y}-{m}-{d} {h}:{i}:{s}")
                    }}</span>
                </template>
            </el-table-column>
            <el-table-column :label="$t('common.operate')" align="center" class-name="small-padding fixed-width">
                <template #default="scope">
                    <el-button type="text" icon="Edit" @click="releaseNotice(scope.row.id)">{{ $t('common.post') }}</el-button>
                    <el-button type="text" icon="FullScreen" @click="handleView(scope.row, addBannerFormRef)">{{ $t('common.details') }}</el-button>
                    <el-button v-if="scope.row.state === 0" type="text" icon="Edit"
                        @click="handleEdit(scope.row, addBannerFormRef)">{{ $t('common.edit') }}</el-button>
                    <el-button type="text" icon="Delete" @click="handleDelete([scope.row.id])">{{ $t('common.delete') }}</el-button>
                </template>
            </el-table-column>
        </el-table>
        <pagination :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
            @pagination="getList" />
        <!-- Edit and add -->
        <el-dialog :title="addBannerDialogTitle === '详情'?$t('common.details'):(addBannerDialogTitle === '编辑'?$t('common.edit'):$t('common.add'))" v-model="addBannerDialogVisible" width="900px"
            append-to-body>
            <el-form ref="addBannerFormRef" :model="addBannerParams" :rules="addBannerFormRules" label-width="100px"
                :disabled="addBannerDialogTitle === '详情'">
                <el-row>
                    <el-col :span="23">
                        <el-form-item :label="$t('common.caption')" prop="title">
                            <el-input :maxlength="50" v-model="addBannerParams.title" :placeholder="$t('common.pleaseEnter')" />
                        </el-form-item>
                        <el-form-item :label="$t('mosAssist.welcomeType')" prop="greetingType">
                            <el-select style="width: 130px" v-model="addBannerParams.greetingType" clearable class="m-2"
                                :placeholder="$t('common.select')">
                                <el-option :label="$t('mosAssist.text')" :value="1" />
                                <el-option :label="$t('mosAssist.image')" :value="2" />
                            </el-select>
                        </el-form-item>
                        <el-form-item :label="$t('mosAssist.language')" prop="lang">
                            <el-select style="width: 130px" v-model="addBannerParams.lang" clearable class="m-2"
                                       :placeholder="$t('common.select')">
                                <el-option :label="$t('mosAssist.chinese')" :value="0" />
                                <el-option :label="$t('mosAssist.english')" :value="1" />
                                <el-option :label="$t('mosAssist.all')" :value="2" />
                            </el-select>
                        </el-form-item>
                        <el-form-item :label="$t('mosAssist.author')" prop="author">
                            <el-input v-model="addBannerParams.author" :maxlength="8" :placeholder="$t('common.pleaseEnter')" />
                        </el-form-item>
                        <el-form-item :label="$t('mosAssist.coverPhoto')" prop="images" v-if="addBannerParams.greetingType === 2">
                            <!-- Upload image -->
                            <image-upload v-model="addBannerParams.images" :disabled="addBannerDialogTitle === '详情'"
                                :isShowTip="addBannerDialogTitle !== '详情'" :limit="3"></image-upload>
                            <!-- end upload -->
                        </el-form-item>
                        <el-form-item :label="$t('mosAssist.abstract')" prop="summary">
                          <el-input autosize v-model="addBannerParams.summary" type="textarea" />
                        </el-form-item>
                        <el-form-item :label="$t('mosAssist.content')" prop="content">
                          <Editor v-if="addBannerDialogTitle !== '详情'" v-model="addBannerParams.content"/>
                          <div v-else class="detail-content" v-html="addBannerParams.content" ></div>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <template #footer>
                <div class="dialog-footer" v-if="addBannerDialogTitle === '详情'">
                    <el-button @click="addBannerDialogVisible = false">{{ $t('common.close') }}</el-button>
                </div>
                <div class="dialog-footer" v-else>
                    <el-button type="primary" @click="add" :loading="loading">{{ $t('common.confirm') }}</el-button>
                    <el-button @click="handleCloseBanner(addBannerFormRef)">{{ $t('common.cancel') }}</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>
<script setup name="welcomeMessage">
import { reactive, ref, watch } from 'vue'
import { getGreenting, releaseGreenting, deleteGreenting, addGreeting } from '@/api/assistant/welcomeMessage'
import { ElMessage, ElMessageBox } from 'element-plus'
import { limitWordCounts } from '../../../utils/limitWordCounts'
import 'quill/dist/quill.snow.css'
import Editor from '@/components/Editor'
import i18n from "../../../i18n";

const state = reactive({
  info: null,
  filesList: []
})
const showSearch = ref(true)
const queryParams = ref({
  title: '',
  times: [],
  greetingType: '',
  pageNum: 1,
  pageSize: 10
})
const total = ref(999)
const getListLoading = ref(false)
const tableData = ref([
  {
    id: '',
    title: '',
    content: '',
    lang: '',
    greetingType: '',
    likeNums: '',
    readNums: '',
    state: '',
    author: '',
    createTime: ''
  }
])
const addBannerDialogVisible = ref(false)
const addBannerDialogTitle = ref('新增')
const method = ref('')
const editRef = ref(false)
const addBannerParams = ref({
  title: '',
  greetingType: '',
  lang: '',
  author: '',
  images: '',
  summary: '',
  content: ''
})

const addBannerFormRef = ref()
// Search data
function handleQuery () {
  getList()
}
const queryFormRef = ref()
// Reset data button
const resetForm = (formEl) => {
  if (!formEl) return
  formEl.resetFields()
  getList()
}
const loading = ref(false)
watch(addBannerDialogVisible, (newValue) => {
  if (!newValue) {
    state.filesList = []
  }
})
const handleCloseBanner = (formEl) => {
  if (!formEl) return
  formEl.resetFields()
  addBannerDialogVisible.value = false
}
const ids = ref([])
const handleTableSelectionChange = (e) => {
  ids.value = e.map((item) => item.id)
}
const add = (data) => {
  if (!addBannerDialogVisible.value || !addBannerFormRef.value) return
  addBannerFormRef.value.validate((valid, fields) => {
    if (valid && method.value) {
      loading.value = true
      const params = addBannerParams.value
      params.images = addBannerParams.value.images
      addGreeting(params, method.value).then((res) => {
        loading.value = false
        addBannerDialogVisible.value = false
        state.filesList = []
        const message = method.value === 'POST' ? i18n.global.t('common.success') : i18n.global.t('common.success')
        ElMessage.success(message)
        getList()
      }).catch(() => {
        loading.value = false
      })
    }
  })
}
// Add Handle
const handleAdd = (formEl) => {
  addBannerDialogTitle.value = '新增'
  addBannerDialogVisible.value = true
  method.value = 'POST'
  addBannerParams.value = {}
  if (formEl) {
    formEl.resetFields()
  }
}
// Edit handle
const handleEdit = (row, formEl) => {
  addBannerDialogTitle.value = '编辑'
  addBannerDialogVisible.value = true
  editRef.value = true
  addBannerParams.value = { ...row }
  method.value = 'PUT'
  if (formEl) {
    formEl.resetFields()
  }
}
const handleView = (row, formEl) => {
  addBannerDialogTitle.value = '详情'
  addBannerDialogVisible.value = true
  addBannerParams.value = { ...row }
  if (formEl) {
    formEl.resetFields()
  }
}
// Get list data table
function getList () {
  getListLoading.value = true
  if(queryParams.value.times.length==2){
    queryParams.value.times=[+new Date(queryParams.value.times[0]+' 00:00:00'),+new Date(queryParams.value.times[1]+' 23:59:59')]
  }
  getGreenting(queryParams.value)
    .then((response) => {
      total.value = response.data.total
      tableData.value = response.data.records
    })
    .finally(() => (getListLoading.value = false))
}

// Release handle
function releaseNotice (data) {
  ElMessageBox.confirm(i18n.global.t('mosAssist.sureToPublishThisAnnouncement'), {
    confirmButtonText: i18n.global.t('common.confirm'),
    cancelButtonText: i18n.global.t('common.cancel'),
    type: 'warning'
  })
    .then(() => {
      releaseGreenting(data).then((item) => {
        if (item.data === false) {
          ElMessage.error(i18n.global.t('common.doNotRepost'))
          getList()
        } else {
          ElMessage.success(i18n.global.t('common.success'))
          getList()
        }
      })
    })
    .catch(() => { })
}
// 删除公告
function handleDelete (data) {
  if (Object.keys(data).length === 0) {
    ElMessage.error(i18n.global.t('common.pleaseSelectDeleteItem'))
    console.log(data)
    return
  }
  ElMessageBox.confirm(i18n.global.t('common.confirmDelete'), {
    confirmButtonText: i18n.global.t('common.confirm'),
    cancelButtonText: i18n.global.t('common.cancel'),
    type: 'warning'
  })
    .then(() => {
      deleteGreenting(data).then((item) => {
        ElMessage.success(i18n.global.t('common.success'))
        getList()
      })
    })
    .catch(() => { })
}
const addBannerFormRules = reactive({
  title: [
    { required: true, message: i18n.global.t('common.cannotBeEmpty'), trigger: 'blur' },
    { max: 50, message: i18n.global.t('common.charactersExceeds') + '50', trigger: 'blur' }
  ],
  greetingType: [{ required: true, message: i18n.global.t('common.cannotBeEmpty'), trigger: 'change' }],
  lang: [{ required: true, message: i18n.global.t('common.cannotBeEmpty'), trigger: 'change' }],
  author: [
    { required: true, message: i18n.global.t('common.cannotBeEmpty'), trigger: 'blur' },
    { max: 8, message: i18n.global.t('common.charactersExceeds') + '8', trigger: 'blur' }
  ],
  images: [{ required: true, message: i18n.global.t('common.cannotBeEmpty'), trigger: ['change', 'blur'] }],
  summary: [
    { required: true, message: i18n.global.t('common.cannotBeEmpty'), trigger: 'blur' },
    { min: 0, max: 180, message: i18n.global.t('common.charactersExceeds') + '180', trigger: 'blur' }
  ]
})
getList()
</script>

<style lang="scss" scoped>
.image-uploader {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);

    &:hover {
        border-color: var(--el-color-primary);
    }

    .avatar-uploader-icon {
        font-size: 28px;
        color: #8c939d;
        width: 100px;
        height: 100px;
        text-align: center;
    }

    .image {
        width: 100px;
        height: 100px;
        display: block;
    }
}

.el-image {
    margin: 0 5px 5px 0;
}

.el-form {
    // max-height: 800px;
    // overflow-y: auto;
}

.limit-num {
    width: 100%;
    color: #999;
    text-align: right;
}
.detail-content {
  padding: 5px 15px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background: #f5f7fa;
  min-height: 33px;
  min-width: 200px;
  font-size: 14px;
  :deep(img) {
    width: 100%;
  }
}
</style>
<style scoped lang="scss">
:deep(.hide .el-upload--picture-card) {
    display: none;
}

// 去掉动画效果
:deep(.el-list-enter-active),
:deep(.el-list-leave-active) {
    transition: all 0s;
}

:deep(.el-list-enter, .el-list-leave-active) {
    opacity: 0;
    transform: translateY(0);
}
</style>
