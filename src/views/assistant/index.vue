<template>
  <div class="app-container consumer-container">
    <el-form :model="queryParams" ref="queryFormRef" :inline="true">
      <el-form-item label="">
        <el-select
          style="width: 150px"
          v-model="queryParams.searchKey"
          clearable
          class="m-2"
          :placeholder="$t('common.select')"
        >
          <el-option :label="$t('common.userNickname')" value="nickName" />
          <el-option :label="$t('common.userId')" value="id" />
        </el-select>
        <el-input
          v-model="queryParams.searchValue"
          :placeholder="$t('common.pleaseEnter')"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('common.state')">
        <el-select
          style="width: 150px"
          v-model="queryParams.helperStatus"
          clearable
          class="m-2"
          :placeholder="$t('common.select')"
        >
          <el-option :label="$t('mosAssist.pending')" :value="0" />
          <el-option :label="$t('mosAssist.processed')" :value="1" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('mosAssist.feedbackTime')" style="width: 300px">
        <el-date-picker
          v-model="queryParams.timeRanges"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          :start-placeholder="$t('common.startDate')"
          :end-placeholder="$t('common.endDate')"
        >
        </el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >{{ $t("common.search") }}
        </el-button>
        <el-button icon="Refresh" @click="resetQuery">{{
          $t("common.reset")
        }}</el-button>
      </el-form-item>
    </el-form>

    <!-- 表格数据 -->
    <el-table v-loading="state.getListLoading" :data="state.tableData">
      <el-table-column
        :label="$t('mosAssist.nickname')"
        prop="nickname"
        align="center"
      />
      <el-table-column
        :label="$t('common.phoneNumber')"
        prop="phone"
        :show-overflow-tooltip="true"
      >
        <template #default="scope">
          <template v-if="scope.row.dialCode && scope.row.phone">
            <span v-if="scope.row.phone.includes('_')">{{
              `+${scope.row.dialCode} ${scope.row.phone.split("_")[1]}`
            }}</span>
            <span v-else>{{
              `+${scope.row.dialCode} ${scope.row.phone}`
            }}</span>
          </template>
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.userId')" prop="idNumber" />
      <el-table-column
        :label="$t('mosAssist.feedbackMessage')"
        prop="name"
        width="100"
      >
        <template #default="scope">
          <el-button
            type="text"
            size="small"
            @click="histroyRecordHanlder(scope.row)"
            >{{ $t("mosAssist.viewMore") }}</el-button
          >
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('common.state')"
        prop="helperStatus"
        width="100"
      >
        <template #default="scope">
          <span v-if="scope.row.helperStatus === 0" style="color: #3291f8">{{
            $t("mosAssist.pending")
          }}</span>
          <span v-if="scope.row.helperStatus === 1">{{
            $t("mosAssist.processed")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('mosAssist.feedbackTime')"
        align="center"
        prop="helperUpdateTime"
        width="160"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.helperUpdateTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('common.operate')"
        align="center"
        width="160"
        fixed="right"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button type="text" @click="showDetail(scope.row)">{{
            $t("common.details")
          }}</el-button>
          <template v-if="scope.row.helperStatus === 0">
            <el-button
              type="text"
              @click="dealHanlder(scope.row)"
              >{{ $t("mosAssist.dealWith") }}</el-button
            >
            <el-button
              type="text"
              @click="showReplyDialog(scope.row)"
              >{{ $t("msg.reply") }}</el-button
            >
          </template>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="state.total > 0"
      :total="state.total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 更多操作 对话框 -->
    <el-dialog
      v-if="state.moreDialogVisible"
      v-model="state.moreDialogVisible"
      :width="state.moreDialogWidth"
      :title="
        state.moreDialogType === '详情'
          ? i18n.global.t('common.details')
          : state.moreDialogType === '反馈信息'
          ? i18n.global.t('mosAssist.feedbackMessage')
          : state.moreDialogType
      "
      append-to-body
      custom-class="el-dialog-no-header"
    >
      <Info
        v-if="state.moreDialogType === '详情'"
        :selectedRowData="state.selectedRowData"
        @close="closeMoreDialog"
        @review="histroyRecordHanlder"
      />
      <History
        v-if="state.moreDialogType === '反馈信息'"
        :selectedRowData="state.selectedRowData"
        @close="closeMoreDialog"
      />
    </el-dialog>
    <!-- 回复对话框 -->
    <el-dialog v-model="state.dialogVisibleReply" :title="$t('user.feedbackContent')" width="420px">
      <el-form ref="replyFormRef" :model="replyForm" :rules="rules" @submit.prevent>
        <el-form-item prop="content">
          <el-input v-model="replyForm.content" type="textarea" :placeholder="$t('common.pleaseEnterContent')" :autosize="{ minRows: 2, maxRows: 10 }" maxlength="1000" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="state.dialogVisibleReply = false">{{ $t('user.close') }}</el-button>
          <el-button type="primary" :loading="loading" @click="handleReply">{{ $t('msg.reply') }}</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="User">
import { nextTick, reactive, ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import Info from './components/Info.vue'
import History from './components/History.vue'

import { helperList, dealHelper, helperReplay } from '@/api/message/message'
import i18n from '../../i18n'

const queryFormRef = ref()
const loading = ref(false)


// 搜索参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  searchKey: 'nickName',
  searchValue: '',
  status: null,
  startTime: null,
  endTime: null,
  timeRanges: []
})

const state = reactive({
  getListLoading: true,
  tableData: [],
  total: 0,
  selectedIds: [],
  moreDialogTitle: '',
  moreDialogVisible: false,
  moreDialogType: '',
  moreDialogWidth: '500px',
  selectedRowData: null,
  dialogVisibleReply: false,
  details: {}
})

// 获取列表数据
function getList () {
  state.getListLoading = true
  if (queryParams.value.searchKey === 'nickName') {
    queryParams.value.nickname = queryParams.value.searchValue
    queryParams.value.id = null
  }
  if (queryParams.value.searchKey === 'id') {
    queryParams.value.nickname = null
    queryParams.value.id = queryParams.value.searchValue
  }
  const [startTime, endTime] = queryParams.value.timeRanges || []
  queryParams.value.startTime =
    startTime === undefined ? null : +new Date(startTime + ' 00:00:00')
  queryParams.value.endTime =
    endTime === undefined ? null : +new Date(endTime + ' 23:59:59')
  helperList(queryParams.value)
    .then((res) => {
      state.tableData = res.data.records
      state.total = res.data.total
    })
    .finally(() => (state.getListLoading = false))
}

// 查询数据
function handleQuery () {
  queryParams.value.pageNum = 1
  getList()
}

// 重置查询
function resetQuery () {
  if (queryFormRef.value) {
    queryFormRef.value.resetFields()
  }
  queryParams.value.searchKey = ''
  queryParams.value.searchValue = ''
  queryParams.value.timeRanges = []
  queryParams.value.helperStatus = null
  queryParams.value.id = ''
  queryParams.value.nickname = ''

  handleQuery()
}

// 详情
function showDetail (row) {
  state.selectedRowData = row
  state.moreDialogWidth = '800px'
  state.moreDialogType = '详情'
  state.moreDialogVisible = true
}

// 反馈信息
function histroyRecordHanlder (row) {
  console.log(row.id)
  state.selectedRowData = row
  state.moreDialogWidth = '800px'
  state.moreDialogType = '反馈信息'
  state.moreDialogVisible = true
}

function closeMoreDialog (type) {
  state.moreDialogVisible = false
  state.moreDialogType = null
  if (type === 'init') {
    getList()
  }
}

const validateContent = (rule, value, callback) => {
  if (!value || !value.trim()) {
    return callback(new Error(i18n.global.t('common.pleaseEnterContent')))
  }
  callback()
}
const rules = reactive({
  content: [{ validator: validateContent, trigger: ['blur', 'change'] }]
})

const replyForm = ref({
  content: ''
})
async function showReplyDialog (row) {
  state.dialogVisibleReply = true
  await nextTick()
  replyFormRef.value.resetFields()
  state.details = row
}
const replyFormRef = ref()
function handleReply () {
  replyFormRef.value.validate((valid) => {
    if (valid) {
      loading.value=true
      const params = {
        ...replyForm.value,
        userId: state.details.id
      }
      helperReplay(params).then(() => {
        state.dialogVisibleReply = false
        setTimeout(() => {
          loading.value = false
        }, 2000)
        ElMessage({
          type: 'success',
          message: i18n.global.t('common.success')
        })
        getList()
      }).catch(() => {
          loading.value=false
      })
    }
  })
}

function dealHanlder (row) {
  ElMessageBox.confirm(
    i18n.global.t('mosAssist.sureToMarkAsProcessed'),
    i18n.global.t('common.tips'),
    {
      confirmButtonText: i18n.global.t('common.confirm'),
      cancelButtonText: i18n.global.t('common.cancel'),
      type: 'warning'
    }
  )
    .then(() => {
      const params = {
        userId: row.id
      }
      dealHelper(params).then((res) => {
        ElMessage({
          type: 'success',
          message: i18n.global.t('common.success')
        })
        getList()
      })
    })
    .catch(() => {
      // ElMessage.error({
      //   message: '操作失败！'
      // })
    })
}

getList()
</script>
<style>
.consumer-container .el-table .el-table__cell {
  z-index: unset;
}
</style>
