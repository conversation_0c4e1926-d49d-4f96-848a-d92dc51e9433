<template>
  <div class="app-container">
    <!-- form search -->
    <el-form :model="queryParams" class="wrapper-form-auto-reply">
      <el-form-item :label="$t('mosAssist.keyword')"><el-input v-model="queryParams.Bykeyword" :placeholder="$t('common.pleaseEnter')" /></el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="onSearch">{{ $t('common.search') }}</el-button>
        <el-button icon="Refresh" @click="onReset">{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>
    <!-- form -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd(ruleFormRef)">{{ $t('common.add') }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete(ids)">{{ $t('common.delete') }}</el-button>
      </el-col>
    </el-row>
    <el-table v-loading="isLoading" :data="dataTable" style="width: 100%" @selection-change="handleTableSelectionChange">
      <el-table-column type="selection" />
      <el-table-column :label="$t('mosAssist.keyword')" prop="keyword" align="center" show-overflow-tooltip>
      </el-table-column>
      <el-table-column :label="$t('mosAssist.language')" align="center" prop="language">
        <template #default="scope">
          <span v-if="scope.row.language === 'English'">{{$t('mosAssist.english')}}</span>
          <span v-if="scope.row.language === 'Chinese'">{{$t('mosAssist.chinese')}}</span>
          <span v-if="scope.row.language === 'All'">{{$t('mosAssist.all')}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('mosAssist.replyContent')" prop="replyContent" align="center" show-overflow-tooltip>
      </el-table-column>
      <el-table-column :label="$t('common.state')" align="center" prop="status">
        <template #default="scope">
          <span v-if="scope.row.status === true">{{$t('mosAssist.on')}}</span>
          <span v-if="scope.row.status === false">{{$t('mosAssist.off')}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.createdTime')" align="center">
        <template #default="scope">{{ parseTime(scope.row.createTime) }}</template>
      </el-table-column>
      <el-table-column :label="$t('common.operate')" align="center">
        <template #default="scope">
          <el-button type="text" icon="FullScreen" @click="handleDetail(scope.row, ruleFormRef)">{{ $t('common.details') }}</el-button>
          <el-button v-if="scope.row.status === false" type="text" icon="Edit"
            @click="handleEdit(scope.row, ruleFormRef)">{{ $t('common.edit') }}</el-button>
          <el-button type="text" icon="Delete" @click="handleDelete([scope.row.id])">{{ $t('common.delete') }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
      @pagination="getList" />
    <!-- Modal -->
    <el-dialog v-model="addRef" :title="titleDialog === '详情'?$t('common.details'):(titleDialog === '新增'?$t('common.add'):$t('common.edit'))" width="700px">
      <!-- form search -->
      <el-row>
        <el-col :span="22">
          <el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="120px"
            :disabled="titleDialog === '详情'">
            <el-form-item :label="$t('mosAssist.keyword')" prop="keyword">
              <el-input v-model="ruleForm.keyword" />
            </el-form-item>
            <el-form-item :label="$t('mosAssist.language')" prop="language">
              <el-select v-model="ruleForm.language" :placeholder="$t('common.select')">
                <el-option :label="$t('mosAssist.chinese')" value="Chinese" />
                <el-option :label="$t('mosAssist.english')" value="English" />
                <!-- <el-option label="全部" value="All" /> -->
              </el-select>
            </el-form-item>
            <el-form-item :label="$t('mosAssist.replyContent')" prop="replyContent">
              <el-input v-model="ruleForm.replyContent" type="textarea" maxlength="1000" show-word-limit />
            </el-form-item>
            <el-form-item :label="$t('common.state')" prop="status">
              <el-radio-group v-model="ruleForm.status" class="ml-4">
                <el-radio :label="true">{{$t('mosAssist.on')}}</el-radio>
                <el-radio :label="false">{{$t('mosAssist.off')}}</el-radio>
              </el-radio-group>
            </el-form-item>

          </el-form>
        </el-col>
      </el-row>
      <!-- form -->
      <template #footer>
        <div class="dialog-footer" v-if="titleDialog === '详情'">
          <el-button @click="addRef = false">{{$t('mosAssist.close')}}</el-button>
        </div>
        <div class="dialog-footer" v-else>
          <el-button type="primary" @click="HandleAdd" :loading="loading">{{ $t('common.confirm') }}</el-button>
          <el-button @click="resetForm(ruleFormRef)">{{ $t('common.cancel') }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="autoReplySetting">
import { limitWordCounts } from "../../../utils/limitWordCounts";
import { getLists, AddData, makeDelete } from "@/api/assistant/autoReply"
import { ref, reactive } from 'vue'
import { ElTable, ElMessage, ElMessageBox } from 'element-plus'
import i18n from "../../../i18n";
const method = ref('')
const addRef = ref(false)
const loading = ref(false)
const titleDialog = ref('新增')
const queryParams = ref({
  Bykeyword: '',
  pageNum: 1,
  pageSize: 10,
})
const ruleFormRef = ref()
const ruleForm = ref({
  id: '',
  keyword: '',
  language: '',
  status: false,
  replyContent: ''
})
const state = reactive({
  info: null,
  filesList: []
})
const isLoading = ref(false)
const total = ref();
const editRef = ref(false)
const dataTable = ref([])
const resetForm = (formEl) => {
  if (!formEl) return
  formEl.resetFields()
  addRef.value = false
}
// handle message
const handleMessageError = () => {
  ElMessage.error(i18n.global.t('common.pleaseSelectDeleteItem'))
}

// search
const onSearch = () => {
  getList()
}
// reset
const onReset = () => {
  queryParams.value.Bykeyword = ''
  getList()
}
const ids = ref([])
const handleTableSelectionChange = (e) => {
  ids.value = e.map((item) => item.id)
}
function handleDelete(data) {
  if (Object.keys(data).length === 0) {
    ElMessage.error(i18n.global.t('common.pleaseSelectDeleteItem'));
    return;
  }
  ElMessageBox.confirm(i18n.global.t('common.confirmDelete'), i18n.global.t('system.warning'), {
    confirmButtonText: i18n.global.t('common.confirm'),
    cancelButtonText: i18n.global.t('common.cancel'),
    type: 'warning'
  })
    .then(() => {
      makeDelete(data)
        .then(() => {
          ElMessage.success(i18n.global.t('common.success'));
          getList();
        })
        .catch(() => {
          handleMessageError();
        });
    })
    .catch(() => {
      // Do nothing if cancelled
    });
}

const HandleAdd = () => {
  if (!addRef.value || !ruleFormRef.value) return
  ruleFormRef.value.validate((valid, fields) => {
    if (valid && method.value) {
      loading.value = true
      const params = ruleForm.value
      AddData(params, method.value).then((res) => {
        loading.value = false
        addRef.value = false
        state.filesList = []
        ElMessage.success(i18n.global.t('common.success'))
        getList()
      })
      .catch(()=>{
        loading.value = false
      })
    }
  })
}
// detail handle
const handleDetail = (row, formEl) => {
  titleDialog.value = '详情'
  addRef.value = true
  ruleForm.value = { ...row }
  if (formEl) {
    formEl.resetFields()
  }
}

// Add Handle
const handleAdd = (formEl) => {
  titleDialog.value = '新增'
  addRef.value = true
  method.value = 'POST'
  ruleForm.value = {}
  if (formEl) {
    formEl.resetFields()
  }
}

// Edit handle
const handleEdit = (row, formEl) => {
  titleDialog.value = '编辑'
  addRef.value = true
  editRef.value = true
  ruleForm.value = { ...row }
  method.value = 'PUT'
  if (formEl) {
    formEl.resetFields()
  }
}
const getList = () => {
  isLoading.value = true
  getLists(queryParams.value)
    .then(response => {
      total.value = response.data.total
      dataTable.value = response.data.records
    })
    .finally(() => (isLoading.value = false))
}
const rules = reactive({
  keyword: [
    { required: true, message: i18n.global.t('common.pleaseEnter'), trigger: 'blur' },
    { max: 300, message: i18n.global.t('common.keywordExceed'), trigger: 'blur' }
  ],
  language: [
    {
      required: true,
      message: i18n.global.t('common.select'),
      trigger: 'change'
    }
  ],
  status: [
    {
      required: true,
      message: i18n.global.t('common.select'),
      trigger: 'change'
    }
  ],
  replyContent: [
    { required: true, message: i18n.global.t('common.pleaseEnter'), trigger: 'blur' },
    { max: 1000, message: i18n.global.t('common.replyExceed'), trigger: 'blur' }
  ]
})
getList()
</script>
<style>
.wrapper-form-auto-reply {
  display: flex;
  justify-content: space-between;
}

.wrapper-form-dielog {
  display: flex;
  gap: 20px;
}

.wrapper-form-dielog .el-form-item__label {
  width: 70px;
}
</style>
