<template>
  <div class="app-container consumer-container">
    <el-form
      :model="queryParams"
      ref="queryFormRef"
      v-show="state.showSearch"
      :inline="true"
    >
      <el-form-item :label="$t('common.createdTime')" style="width: 308px">
        <el-date-picker
          v-model="queryParams.timeRanges"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          :start-placeholder="$t('common.startDate')"
          :end-placeholder="$t('common.endDate')"
        >
        </el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >{{ $t("common.search") }}
        </el-button>
        <el-button icon="Refresh" @click="resetQuery">{{
          $t("common.reset")
        }}</el-button>
      </el-form-item>
    </el-form>

    <!-- 表格数据 -->
    <el-table
      v-loading="state.getListLoading"
      :data="state.tableData"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        :label="$t('userManage.nickname')"
        prop="nickname"
        align="center"
      />
      <el-table-column
        :label="$t('userManage.avatar')"
        prop="headPortrait"
      >
        <template #default="scope">
          <el-image
            style="width: 60px; height: 60px"
            :src="scope.row.headPortrait"
            :zoom-rate="1.2"
            :preview-src-list="[scope.row.headPortrait]"
            :initial-index="4"
            fit="cover"
          />
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('common.createdTime')"
        align="center"
        prop="createTime"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('userManage.lastOnlineTime')"
        prop="lastUpTime"
        align="center"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.lastUpTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('userManage.state')"
        prop="status"
        align="center"
      >
        <template #default="scope">
          <span style="color: red" v-if="scope.row.status === 2">{{
            $t("userManage.deactivated")
          }}</span>
          <span v-else-if="!scope.row.isFreeze">{{
            $t("userManage.normal")
          }}</span>
          <span v-else="scope.row.isFreeze">{{ $t("common.freeze") }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="state.total > 0"
      :total="state.total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改对话框 -->
    <el-dialog
      :title="state.dialogTitle"
      v-model="state.dialogVisible"
      width="500px"
      append-to-body
    >
      <el-form
        ref="formRef"
        :model="formParams"
        :rules="formRules"
        label-width="120px"
      >
        <el-form-item :label="$t('userManage.firstName')">
          <el-input
            v-model="formParams.firstName"
            :placeholder="$t('common.pleaseEnter')"
          />
        </el-form-item>
        <el-form-item :label="$t('userManage.lastName')">
          <el-input
            v-model="formParams.lastName"
            :placeholder="$t('common.pleaseEnter')"
          />
        </el-form-item>
        <el-form-item :label="$t('common.phoneNumber')" prop="phone">
          <el-input
            v-model="formParams.phone"
            :placeholder="$t('common.pleaseEnter')"
          />
        </el-form-item>
        <el-form-item :label="$t('userManage.dialCode')" prop="dialCode">
          <el-input
            v-model="formParams.dialCode"
            :placeholder="$t('common.pleaseEnter')"
          />
        </el-form-item>
        <el-form-item :label="$t('userManage.password')" prop="password">
          <el-input
            type="password"
            v-model="formParams.password"
            :placeholder="$t('common.pleaseEnter')"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">{{
            $t("common.confirm")
          }}</el-button>
          <el-button @click="cancel">{{ $t("common.cancel") }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Consumer">
import { reactive, ref, getCurrentInstance } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { countryCode } from "@/utils/enum.js";
import i18n from "../../i18n";

import {
  listUser,
  saveUser,
  artificialRecharge,
  userBan,
  userFreeze,
} from "@/api/consumer/imuser";

const { proxy } = getCurrentInstance();

const queryFormRef = ref();
const formRef = ref();

// 搜索参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  type: "GUEST",
  timeRanges: [],
});

const state = reactive({
  showSearch: true,
  getListLoading: true,
  tableData: [],
  total: 0,
  selectedIds: [],
  dialogVisible: false,
  dialogTitle: "",
  moreDialogTitle: "",
  moreDialogVisible: false,
  moreDialogType: "",
  moreDialogWidth: "500px",
});



// 获取列表数据
function getList() {
  state.getListLoading = true;
  const [startTime, endTime] = queryParams.value.timeRanges || [];
  queryParams.value.startTime =
    startTime === undefined ? null : +new Date(startTime + " 00:00:00");
  queryParams.value.endTime =
    endTime === undefined ? null : +new Date(endTime + " 23:59:59");
  listUser(queryParams.value)
    .then((res) => {
      state.tableData = res.data.records;
      state.total = res.data.total;
    })
    .finally(() => (state.getListLoading = false));
}

// 查询数据
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

function nameTextStr(row) {
  const a1 = row.firstName ? row.firstName : "";
  const a2 = row.lastName ? row.lastName : "";
  const a3 = a1 && a2 ? "_" : "";
  return a1 + a3 + a2;
}
// 重置查询
function resetQuery() {
  if (queryFormRef.value) {
    queryFormRef.value.resetFields();
  }
  queryParams.value.searchValue = "";
  queryParams.value.timeRanges = [];
  queryParams.value.accountStatus = null;
  queryParams.value.onLineState = null;
  queryParams.value.isMember = null;
  queryParams.value.searchKey = null;
  queryParams.value.status = null;
  queryParams.value.appOnLineState = null;
  queryParams.value.webOnLineState = null;

  handleQuery();
}

getList();
</script>
<style>
.consumer-container .el-table .el-table__cell {
  z-index: unset;
}
.el-dialog-no-header .el-table .el-table__cell {
  z-index: unset !important;
}
</style>
