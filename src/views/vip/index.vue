<template>
  <div class="app-container consumer-container">
    <el-form :model="queryParams" ref="queryFormRef" v-show="state.showSearch" :inline="true">
      <el-form-item :label="$t('vip.type')">
        <el-select style="width: 150px" v-model="queryParams.level" clearable class="m-2"
          :placeholder="$t('common.select')">
          <el-option v-for="item in VipBillingCycleEnum" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('vip.firstPurchaseDiscount')">
        <el-select style="width: 150px" clearable class="m-2" v-model="queryParams.firstPurchaseDiscountFlag"
          :placeholder="$t('common.select')">
          <el-option :label="$t('common.on')" :value="true" />
          <el-option :label="$t('common.off')" :value="false" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('vip.status')">
        <el-select style="width: 150px" clearable class="m-2" v-model="queryParams.enabled"
          :placeholder="$t('common.select')">
          <el-option :label="$t('common.on')" :value="true" />
          <el-option :label="$t('common.off')" :value="false" />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">{{ $t("common.search") }}
        </el-button>
        <el-button icon="Refresh" @click="resetQuery">{{
          $t("common.reset")
          }}</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleShowModal('add')">{{ $t("common.add") }}</el-button>
        <!-- <el-button type="warning" plain icon="Download" @click="handleExport"
          >{{ $t("common.export") }}
        </el-button> -->
      </el-col>
      <right-toolbar v-model:showSearch="state.showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 表格数据 -->
    <el-table v-loading="state.getListLoading" :data="state.tableData">
      <el-table-column :label="$t('vip.type')" prop="billingCycle" align="center" :formatter="formatType" />
      <el-table-column :label="$t('vip.firstPurchaseDiscount')" prop="firstPurchaseDiscountFlag" align="center">
        <template #default="scope">
          <template v-if="
              [
                'CONTINUOUS_MONTHLY_SUBSCRIPTION',
                'CONTINUOUS_QUARTERLY_SUBSCRIPTION',
                'CONTINUOUS_YEARLY_SUBSCRIPTION',
              ].includes(scope.row.billingCycle)
            ">
            <el-switch v-model="scope.row.firstPurchaseDiscountFlag" @change="
                handleEnable(
                  'enabledDiscount',
                  scope.row
                )
              "></el-switch>
          </template>
          <template v-else> / </template>
        </template>
      </el-table-column>
      <el-table-column :label="$t('vip.discount') + '(OFF)'" prop="firstPurchaseDiscount" align="center"
        :formatter="formatDiscount" />
      <el-table-column :label="$t('vip.payableAmount') + '($)'" prop="payableAmount" align="center" width="160" />
      <el-table-column :label="$t('vip.crossedAmount') + '($)'" prop="crossedAmount" align="center" width="160">
        <template #default="scope">
          <template v-if="scope.row.crossedAmount != null">
            {{ scope.row.crossedAmount }}
          </template>
          <template v-else> / </template>
        </template>
      </el-table-column>
      <el-table-column :label="$t('vip.status')" prop="enabled" align="center" width="160">
        <template #default="scope">
          <el-switch v-model="scope.row.enabled" @change="
              handleEnable('enabledStatus', scope.row)
            ">
          </el-switch>
        </template>
      </el-table-column>
      <el-table-column :label="$t('userManage.operate')" align="center" width="220" fixed="right"
        class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button type="text" v-if="!scope.row.enabled" @click="handleShowModal('edit', scope.row)">{{
            $t("common.edit") }}</el-button>
          <el-button type="text" @click="handleAction('delete', scope.row.id)">{{ $t("common.delete") }}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="state.total > 0" :total="state.total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改对话框 -->
    <el-dialog :title="state.dialogTitle" v-model="state.dialogVisible" width="600px" append-to-body
      :before-close="handleDialogClose">
      <el-form ref="formRef" :model="formParams" :rules="formRules" label-width="120px"
        :validate-on-rule-change="false">
        <el-form-item :label="$t('vip.type')" prop="level">
          <el-select v-model="formParams.level" :placeholder="$t('common.select')" class="full-width"
            @change="handleLevelChange">
            <el-option v-for="item in VipBillingCycleEnum" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <template v-if="
            [
              'CONTINUOUS_MONTHLY_SUBSCRIPTION',
              'CONTINUOUS_QUARTERLY_SUBSCRIPTION',
              'CONTINUOUS_YEARLY_SUBSCRIPTION',
            ].includes(formParams.level)
          ">
          <el-form-item :label="$t('vip.firstPurchaseDiscount')" prop="firstPurchaseDiscountFlag">
            <el-switch v-model="formParams.firstPurchaseDiscountFlag"></el-switch>
          </el-form-item>
          <el-form-item :label="$t('vip.discount')" prop="firstPurchaseDiscount">
            <el-input v-model="formParams.firstPurchaseDiscount" :placeholder="$t('common.pleaseEnter')"
              :disabled="['audit'].includes(state.dialogType)">
              <template #append>%OFF</template>
            </el-input>
          </el-form-item>
        </template>
        <el-form-item :label="$t('vip.payableAmount') + '($)'" prop="payableAmount">
          <el-input v-model="formParams.payableAmount" :placeholder="$t('common.pleaseEnter')" />
        </el-form-item>
        <el-form-item :label="$t('vip.crossedAmount') + '($)'" prop="crossedAmount">
          <el-input v-model="formParams.crossedAmount" :placeholder="$t('common.pleaseEnter')" />
        </el-form-item>
        <el-form-item :label="$t('vip.status')" prop="enabled">
          <el-switch required v-model="formParams.enabled"></el-switch>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">{{
            $t("common.confirm")
            }}</el-button>
          <el-button @click="handleDialogClose">{{
            $t("common.cancel")
            }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="VIP">
import { ref, computed } from "vue";
import i18n from "@/i18n";
import echarts from "@/plugins/echarts";
import { VipBillingCycleEnum } from "@/utils/enum";
const { proxy } = getCurrentInstance();
import { ElMessage, ElMessageBox } from "element-plus";

import {
  pageVipPackageList,
  addVipPackage,
  editVipPackage,
  deleteVipPackage,
  enableLevel,
  enableDiscount,
} from "@/api/vip/vip";
const queryFormRef = ref();
const formRef = ref();

const state = reactive({
  showSearch: true,
  getListLoading: true,
  tableData: [],
  total: 0,
  dialogVisible: false,
  dialogTitle: "",
  dialogType: "",
  dataDialogVisible: false,
});
// 搜索参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  level: null,
  firstPurchaseDiscountFlag: null,
  enabled: null,
});

// 获取列表数据
function getList() {
  state.getListLoading = true;
  pageVipPackageList(queryParams.value)
    .then((res) => {
      state.tableData = res.data.records;
      state.total = res.data.total;
    })
    .finally(() => (state.getListLoading = false));
}

// 查询数据
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

function resetQuery() {
  queryParams.value.level = null;
  queryParams.value.firstPurchaseDiscountFlag = null;
  queryParams.value.enabled = null;
  handleQuery();
}

// 导出按钮操作
// function handleExport() {
//   proxy.download(
//     "vipLevel/exportVipLevel",
//     {
//       ...queryParams.value,
//     },
//     `VIP_PACKAGE_${proxy.parseTime(new Date(), "{y}/{m}/{d}")}.xlsx`
//   );
// }

function formatDiscount(row, column, cellValue, index) {
  if (cellValue === "0") {
    return "/";
  }
  return cellValue * 100 + `%`;
}

function formatType(row, column, cellValue, index) {
  return VipBillingCycleEnum.find((item) => item.value === cellValue).label;
}

async function handleEnable(type, row) {
  const params = {
    id: row.id,
    enabled: row.enabled,
  };
  if (type === "enabledStatus") {
    await enableLevel(params).catch(() => {
      row.enabled = !params.enabled
    })
  }
  if (type === "enabledDiscount") {
    params.enabled = row.firstPurchaseDiscountFlag
    await enableDiscount(params).catch(() => {
      row.firstPurchaseDiscountFlag = !params.enabled
    })
  }
}

async function handleAction(type, id) {
  const params = {
    id,
  };
  if (type === "delete") {
    await ElMessageBox.confirm(
      i18n.global.t("common.confirmDelete"),
      i18n.global.t("common.tips"),
      {
        confirmButtonText: i18n.global.t("common.confirm"),
        cancelButtonText: i18n.global.t("common.cancel"),
        type: "warning",
      }
    );
    await deleteVipPackage(params);
    ElMessage.success(i18n.global.t("common.success"));
    getList();
  }
}

// 新增参数
const formParams = ref({
  level: null,
  firstPurchaseDiscountFlag: true,
  firstPurchaseDiscount: null,
  payableAmount: null,
  crossedAmount: null,
  enabled: true,
});

function resetForm() {
  formParams.value = {
    level: null,
    firstPurchaseDiscountFlag: true,
    firstPurchaseDiscount: null,
    payableAmount: null,
    crossedAmount: null,
    enabled: true,
  };
  if (formRef.value) {
    formRef.value.resetFields();
  }
}

function handleDialogClose() {
  resetForm();
  state.dialogVisible = false;
}

function validateForm(formNameRef) {
  return new Promise((resolve, reject) => {
    if (!formNameRef.value) return Promise.resolve(true);
    formNameRef.value.validate((valid, fields) => {
      if (valid) {
        return resolve();
      } else {
        reject(new Error(fields));
      }
    });
  });
}

async function submitForm() {
  const validFormList = [validateForm(formRef)];
  try {
    await Promise.all(validFormList);

    const params = {
      ...formParams.value,
    };
    if (
      [
        "ONE_YEAR_SUBSCRIPTION",
        "ONE_QUARTER_SUBSCRIPTION",
        "ONE_MONTH_SUBSCRIPTION",
      ].includes(params.level)
    ) {
      params.firstPurchaseDiscountFlag = false;
      params.firstPurchaseDiscount = "0";
    }
    params.firstPurchaseDiscount = params.firstPurchaseDiscount / 100;
    if (state.dialogType === "add") {
      await addVipPackage(params);
      ElMessage.success(i18n.global.t("common.success"));
    }
    if (state.dialogType === "edit") {
      await editVipPackage(params);
      ElMessage.success(i18n.global.t("common.success"));
    }
    handleDialogClose();
    getList();
  } catch (error) {
    console.log("🚀 检验不通过", error);
  }
}

function handleShowModal(type, item) {
  state.dialogType = type;
  resetForm();
  state.dialogTitle = i18n.global.t("vip.package");
  if (type === "edit") {
    console.log("🚀 edit", item);
    formParams.value = {
      ...item,
      firstPurchaseDiscount: Number(item.firstPurchaseDiscount) * 100 + "",
      level: item.billingCycle,
    };
  }
  state.dialogVisible = true;
}

function handleLevelChange(val) {
  const isRequired = [
    "CONTINUOUS_MONTHLY_SUBSCRIPTION",
    "CONTINUOUS_QUARTERLY_SUBSCRIPTION",
    "CONTINUOUS_YEARLY_SUBSCRIPTION",
  ].includes(formParams.value.level);
  if (!isRequired) {
    formParams.value.firstPurchaseDiscountFlag = false;
    formParams.value.firstPurchaseDiscount = "0";
  }
}

const formRules = computed(() => {
  const isRequired = [
    "CONTINUOUS_MONTHLY_SUBSCRIPTION",
    "CONTINUOUS_QUARTERLY_SUBSCRIPTION",
    "CONTINUOUS_YEARLY_SUBSCRIPTION",
  ].includes(formParams.value.level);
  return {
    level: [
      {
        required: true,
        message: i18n.global.t("common.select"),
        trigger: "change",
      },
    ],

    firstPurchaseDiscount: [
      {
        required: isRequired,
        message: i18n.global.t("common.pleaseEnterContent"),
        trigger: "blur",
      },
      {
        validator: (rule, value, callback) => {
          if ((value && !/^[0-9]+$/.test(value)) || value < 1 || value > 100) {
            callback(new Error(i18n.global.t("vip.checkDiscount")));
          } else {
            callback();
          }
        },
      },
    ],

    payableAmount: [
      {
        required: true,
        message: i18n.global.t("common.pleaseEnterContent"),
        trigger: "blur",
      },
      {
        validator: (rule, value, callback) => {
          if (
            (value && !/^[0-9]+(\.[0-9]{1,2})?$/.test(value)) ||
            value <= 0 ||
            value > 99999.99
          ) {
            callback(new Error(i18n.global.t("vip.checkPayableAmount")));
          } else {
            callback();
          }
        },
      },
    ],

    crossedAmount: [
      {
        validator: (rule, value, callback) => {
          if (value === null || value === "") {
            callback();
          }
          if (
            (value && !/^[0-9]+(\.[0-9]{1,2})?$/.test(value)) ||
            value <= 0 ||
            value > 99999.99
          ) {
            callback(new Error(i18n.global.t("vip.checkPayableAmount")));
          } else {
            if (
              value &&
              Number(value) <= Number(formParams.value.payableAmount)
            ) {
              callback(new Error(i18n.global.t("vip.checkCrossedAmount")));
            } else {
              callback();
            }
          }
        },
      },
    ],
  };
});
getList();
</script>
