<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryFormRef"
      v-show="state.showSearch"
      :inline="true"
    >
      <el-form-item :label="$t('wallet.selectWallet')" prop="walletType">
        <el-select
          style="width: 150px"
          v-model="queryParams.walletType"
          :placeholder="$t('common.select')"
        >
          <el-option
            v-for="item in walletTypeArr"
            :label="item.value"
            :value="item.key"
            :key="item.key"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('wallet.selectCurrency')" prop="currency">
        <el-select
          style="width: 150px"
          v-model="queryParams.currency"
          :placeholder="$t('common.select')"
        >
          <el-option
            v-for="item in currencyArr"
            :label="item.value"
            :value="item.key"
            :key="item.key"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('wallet.transactionTime')" style="width: 350px" prop="timeRanges">
        <el-date-picker
          v-model="queryParams.timeRanges"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          :start-placeholder="$t('common.startTime')"
          :end-placeholder="$t('common.endTime')"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >{{ $t('common.search') }}</el-button
        >
        <el-button icon="Refresh" @click="resetQuery(queryFormRef)"
          >{{ $t('common.reset') }}</el-button
        >
        <el-button type="warning" plain icon="Download" @click="handleExport"
          >{{$t('common.export')}}
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 表格数据 -->
    <el-table v-loading="state.getListLoading" :data="state.tableData">
      <el-table-column :label="$t('wallet.date')" align="center" prop="createTime" >
        <template #default="scope">
          {{ parseTime(scope.row.createTime, "{y}-{m}-{d}") }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('wallet.totalAccountAmount')" align="center" prop="userBalance" />
      <el-table-column :label="$t('wallet.addedAccountAmount')" align="center" prop="addAmount" >
        <template #default="scope">
          {{ scope.row.addAmount === null ? '-' : scope.row.addAmount }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('wallet.userAddRewardAmount')" align="center" prop="rewardAmount">
        <template #default="scope">
          {{ scope.row.rewardAmount === null ? '0' : scope.row.rewardAmount }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('wallet.userAddRewardCount')" align="center" prop="rewardCount">
        <template #default="scope">
          {{ scope.row.rewardCount === null ? '0' : scope.row.rewardCount }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('wallet.addedRedPacketAmount')" align="center" prop="redPacketAmount" />
      <el-table-column :label="$t('wallet.addedRedPacketTimes')" align="center" prop="redPacketCount" />
      <el-table-column :label="$t('wallet.addedTransferAmount')" align="center" prop="userTransferAmount" />
      <el-table-column :label="$t('wallet.addedTransferTimes')" align="center" prop="userTransferCount" />
      <el-table-column :label="$t('wallet.totalUsers')" align="center" prop="accumulateUser" />
      <el-table-column :label="$t('wallet.addedUsers')" align="center" prop="userCount" />
    </el-table>

    <pagination
      v-show="state.total > 0"
      :total="state.total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="DailyRecords">
import { reactive, ref, getCurrentInstance } from 'vue'
import { getWalletStatisticsPage } from '@/api/purse/index'
import i18n from "../../i18n";

const { proxy } = getCurrentInstance()

const queryFormRef = ref()

const walletTypeArr = ref([
  {
    key: 'MOS',
    value: i18n.global.t('wallet.mosappWallet')
  },
  {
    key: 'UPAY',
    value: i18n.global.t('wallet.uPayWallet')
  }
])

const currencyArr = ref([
  {
    key: 'USD',
    value: 'USD'
  },
  {
    key: 'KHR',
    value: 'KHR'
  }
])

// 搜索参数
const queryParams = ref({
  walletType: 'UPAY',
  currency: 'USD',
  timeRanges: [],
  pageNum: 1,
  pageSize: 10
})

const state = reactive({
  showSearch: true,
  dialogVisible: false,
  dialogType: '',
  dialogTitle: '',
  row: {}
})

// 获取列表数据
function getList () {
  state.getListLoading = true
  const params = {
    ...queryParams.value
  }
  for (const key in params) {
    if (!params[key]) params[key] = null
  }
  if (params.timeRanges && params.timeRanges.length !== 0) {
    params.startDate = +new Date(params.timeRanges[0] + ' 00:00:00')
    params.endDate = +new Date(params.timeRanges[1] + ' 23:59:59')
    delete params.timeRange
  }
  getWalletStatisticsPage(params)
    .then((response) => {
      state.tableData = response.data.records
      state.total = response.data.total
    })
    .finally(() => {
      state.getListLoading = false
    })
}
// 查询数据
function handleQuery () {
  queryParams.value.pageNum = 1
  getList()
}
// 重置查询
const resetQuery = (formEl) => {
  if (!formEl) return
  console.log(formEl)
  formEl.resetFields()
  getList()
}
function handleExport () {
  const params = {
    ...queryParams.value
  }
  for (const key in params) {
    if (!params[key]) params[key] = null
  }
  const walletType = walletTypeArr.value.find(i => i.key === params.walletType)?.value
  const currency = currencyArr.value.find(i => i.key === params.currency)?.value
  let title = `${walletType}_${currency}_`
  if (params.timeRanges && params.timeRanges.length !== 0) {
    params.startDate = +new Date(params.timeRanges[0] + ' 00:00:00')
    params.endDate = +new Date(params.timeRanges[1] + ' 23:59:59')
    title = `${title}_${params.timeRanges[0]}_${params.timeRanges[1]}`
    delete params.timeRange
  }
  title = `${title}_dailyTransactionData.xlsx`
  proxy.download(
    '/wallet/exportWalletStatisticsList',
    {
      ...params
    },
    title
  )
}

getList()
</script>
