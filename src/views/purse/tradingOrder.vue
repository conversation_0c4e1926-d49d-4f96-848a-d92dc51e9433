<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryFormRef"
      v-show="state.showSearch"
      :inline="true"
    >
      <el-form-item :label="$t('wallet.orderID')" prop="orderNo">
        <el-input
          v-model.trim="queryParams.orderNo"
          :placeholder="$t('common.pleaseEnter')"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('wallet.orderType')" prop="orderType">
        <el-select
          style="width: 150px"
          v-model="queryParams.orderType"
          :placeholder="$t('common.select')"
        >
          <el-option
            v-for="item in orderTypeArr"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('wallet.usersInformation')" prop="userInfo">
        <el-input
          v-model.trim="queryParams.userInfo"
          :placeholder="$t('wallet.userAttr')"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item
        :label="$t('wallet.transactionTime')"
        style="width: 350px"
        prop="timeRanges"
      >
        <el-date-picker
          v-model="queryParams.timeRanges"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          :start-placeholder="$t('common.startTime')"
          :end-placeholder="$t('common.endTime')"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item :label="$t('wallet.selectWallet')" prop="walletType">
        <el-select
          style="width: 150px"
          v-model="queryParams.walletType"
          :placeholder="$t('common.select')"
        >
          <el-option
            v-for="item in walletTypeArr"
            :label="item.value"
            :value="item.key"
            :key="item.key"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('wallet.selectCurrency')" prop="currency">
        <el-select
          style="width: 150px"
          v-model="queryParams.currency"
          :placeholder="$t('common.select')"
        >
          <el-option
            v-for="item in currencyArr"
            :label="item.value"
            :value="item.key"
            :key="item.key"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('wallet.orderState')" prop="orderStatus">
        <el-select
          style="width: 150px"
          v-model="queryParams.orderStatus"
          class="m-2"
          :placeholder="$t('common.pleaseSelectState')"
        >
          <el-option
            v-for="item in statusArr"
            :label="item.value"
            :value="item.key"
            :key="item.key"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">{{
          $t("common.search")
        }}</el-button>
        <el-button icon="Refresh" @click="resetQuery(queryFormRef)">{{
          $t("common.reset")
        }}</el-button>
        <el-button type="warning" plain icon="Download" @click="handleExport"
          >{{ $t("common.export") }}
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 表格数据 -->
    <el-table
      v-loading="state.getListLoading"
      show-summary
      :summary-method="getSummaries"
      @selection-change="handleSelectionChange"
      :data="state.tableData"
    >
      <el-table-column type="selection" width="55"> </el-table-column>
      <el-table-column
        :label="$t('wallet.orderID')"
        align="center"
        prop="orderNo"
      />
      <el-table-column
        :label="$t('wallet.orderType')"
        prop="transactionType"
        align="center"
      >
        <template #default="scope">
          <div>
            {{
              orderTypeArr.find((i) => i.key === scope.row.transactionType)
                ?.value
            }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('common.userNickname')"
        align="center"
        prop="userName"
      />
      <el-table-column
        :label="$t('common.userId')"
        align="center"
        prop="userNumber"
      />
      <el-table-column
        :label="$t('common.phoneNumber')"
        align="center"
        prop="mobile"
      >
        <template #default="scope">
          <div>+{{ scope.row.dailCode }} {{ scope.row.mobile }}</div>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('wallet.transactionTime')"
        align="center"
        prop="transactionTime"
      >
        <template #default="scope">
          {{ parseTime(scope.row.transactionTime) }}
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('wallet.wallet')"
        prop="walletType"
        align="center"
      >
        <template #default="scope">
          <div>
            {{
              walletTypeArr.find((i) => i.key === scope.row.walletType)?.value
            }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('wallet.currency')"
        prop="currency"
        align="center"
      >
        <template #default="scope">
          <div>
            {{ currencyArr.find((i) => i.key === scope.row.currency)?.value }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('wallet.transactionAmount')"
        align="center"
        prop="transactionAmount"
        min-width="200"
      >
        <template #header>
          <div>{{ $t("wallet.transactionAmount") }}</div>
          <div class="overflow-ellipsis-1" :title="state.totalAmount">
            {{ $t("wallet.totalAmount") }}:{{ state.totalAmount }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('common.state')"
        prop="orderStatus"
        align="center"
      >
        <template #default="scope">
          <el-tag
            :type="
              statusArr.find((i) => i.key === scope.row.orderStatus)?.color
            "
          >
            {{ statusArr.find((i) => i.key === scope.row.orderStatus)?.value }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('common.operate')"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button type="text" @click="showDetail(scope.row)">{{
            $t("common.details")
          }}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      v-if="state.dialogVisible"
      v-model="state.dialogVisible"
      width="1000px"
      :title="$t('wallet.orderDetails')"
    >
      <RechargeView v-if="state.dialogType === 1" :row="state.row" />
      <TransferView v-if="state.dialogType === 2" :row="state.row" />
      <RedView v-if="state.dialogType === 3" :row="state.row" />
      <BonusView v-if="state.dialogType === 4 || state.dialogType === 6" :row="state.row" />
      <RedView v-if="state.dialogType === 5" :row="state.row" />
    </el-dialog>

    <pagination
      v-show="state.total > 0"
      :total="state.total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="FundWithdrawalReview">
import { reactive, ref, getCurrentInstance } from "vue";
import { getTransactionOrderList } from "@/api/purse/index";

import RechargeView from "./components/recharge.vue";
import BonusView from "./components/bonus.vue";
import TransferView from "./components/transfer.vue";
import RedView from "./components/red.vue";
import { ElMessage } from "element-plus";
import i18n from "../../i18n";

const { proxy } = getCurrentInstance();

const queryFormRef = ref();

const orderTypeArr = ref([
  {
    key: null,
    value: i18n.global.t("common.all"),
  },
  {
    key: 1,
    value: i18n.global.t("wallet.topUp"),
  },
  {
    key: 2,
    value: i18n.global.t("wallet.transfer"),
  },
  {
    key: 3,
    value: i18n.global.t("wallet.redPacket"),
  },
  {
    key: 4,
    value: i18n.global.t("wallet.referralBonus"),
  },
  {
    key: 5,
    value: i18n.global.t("wallet.channelRedPacket"),
  },
  {
    key: 6,
    value: i18n.global.t("wallet.rewardOrder"),
  }
]);

const walletTypeArr = ref([
  // {
  //   key: null,
  //   value: i18n.global.t('common.all')
  // },
  {
    key: "UPAY",
    value: i18n.global.t("wallet.uPayWallet"),
  },
  {
    key: "MOS",
    value: i18n.global.t("wallet.mosappWallet"),
  },
]);

const currencyArr = ref([
  {
    key: "USD",
    value: "USD",
  },
  {
    key: "KHR",
    value: "KHR",
  },
]);

const statusArr = ref([
  {
    key: null,
    value: i18n.global.t("common.all"),
  },
  {
    key: "SUCCESS",
    value: i18n.global.t("wallet.succesfully"),
    color: "success",
  },
  {
    key: "ING",
    value: i18n.global.t("wallet.inProgress"),
    color: "info",
  },
  {
    key: "FAILURE",
    value: i18n.global.t("wallet.failed"),
    color: "danger",
  },
]);

// 搜索参数
const queryParams = ref({
  orderNo: null,
  orderType: null,
  userInfo: null,
  walletType: "UPAY",
  currency: "USD",
  orderStatus: null,
  timeRanges: [],
  pageNum: 1,
  pageSize: 10,
});

const state = reactive({
  showSearch: true,
  dialogVisible: false,
  dialogType: "",
  dialogTitle: "",
  row: {},
  totalAmount: 0,
});

const showDetail = (row) => {
  state.dialogVisible = true;
  state.dialogType = row.transactionType;
  state.row = { ...row };
};

// 获取列表数据
function getList() {
  state.getListLoading = true;
  const params = {
    ...queryParams.value,
  };
  for (const key in params) {
    if (!params[key]) params[key] = null;
  }
  if (params.timeRanges && params.timeRanges.length !== 0) {
    params.startDate = +new Date(params.timeRanges[0] + " 00:00:00");
    params.endDate = +new Date(params.timeRanges[1] + " 23:59:59");
    delete params.timeRange;
  }
  getTransactionOrderList(params)
    .then((response) => {
      state.tableData = response.data.transactionPage.records;
      state.total = response.data.transactionPage.total;
      state.totalAmount = response.data.totalAmount;
    })
    .finally(() => {
      state.getListLoading = false;
    });
}
// 查询数据
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}
// 重置查询
const resetQuery = (formEl) => {
  if (!formEl) return;
  console.log(formEl);
  formEl.resetFields();
  getList();
};
function getDiffDay(date1, date2) {
  let totalDays = null;
  let diffDate = null;
  const myDate1 = Date.parse(date1);
  const myDate2 = Date.parse(date2);
  diffDate = Math.abs(myDate1 - myDate2);
  totalDays = Math.floor(diffDate / (1000 * 3600 * 24));
  return totalDays;
}
function handleExport() {
  const params = {
    ...queryParams.value,
  };
  for (const key in params) {
    if (!params[key]) params[key] = null;
  }
  if (params.timeRanges && params.timeRanges.length !== 0) {
    params.startDate = +new Date(params.timeRanges[0] + " 00:00:00");
    params.endDate = +new Date(params.timeRanges[1] + " 23:59:59");
    const diff = getDiffDay(params.startDate, params.endDate);
    if (diff > 35) {
      return ElMessage.warning(i18n.global.t("wallet.exportTimeLimit"));
    }
    delete params.timeRange;
  } else {
    return ElMessage.warning(i18n.global.t("wallet.selectTimeLimit"));
  }
  proxy.download(
    "/wallet/exportTransactionOrderList",
    {
      ...params,
    },
    `order_${new Date().getTime()}.xlsx`
  );
}
// 计算 transactionAmount 列的总和
function getSummaries(param) {
  const { columns, data } = param;
  const sums = [];
  columns.forEach((column, index) => {
    if (index === 1) {
      sums[index] = i18n.global.t('wallet.pageTotal');
      if (multipleSelection.value.length > 0) {
        sums[index] = i18n.global.t('wallet.selectTotal');
      }
      return;
    } else if (index === 9) {
      let arr = data;
      if (multipleSelection.value.length > 0) {
        arr = multipleSelection.value;
      }
      const values = arr.map((item) => Number(item[column.property]));
      let total = 0;
      if (!values.every((value) => isNaN(value))) {
        total = values.reduce((prev, curr) => {
          const value = Number(curr);
          if (!isNaN(value)) {
            return prev + curr;
          } else {
            return prev;
          }
        }, 0);
      }
      sums[index] = parseFloat(total).toFixed(2);
    } else {
      return;
    }
  });
  return sums;
}
const multipleSelection = ref([]);
function handleSelectionChange(val) {
  multipleSelection.value = val;
  console.log(`selection-change: `, val);
}
getList();
</script>
<style lang="scss" scoped>
.el-table {
  :deep(.el-table__footer) {
    .el-table__cell {
      background-color: transparent;
    }
    .el-table__cell:nth-child(2) {
      font-weight: bold;
    }
  }
}
</style>
