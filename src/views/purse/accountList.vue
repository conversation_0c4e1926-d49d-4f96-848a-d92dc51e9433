<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryFormRef"
      v-show="state.showSearch"
      :inline="true"
    >
      <el-form-item :label="$t('wallet.usersInformation')" prop="userInfo">
        <el-input
          v-model.trim="queryParams.userInfo"
          :placeholder="$t('wallet.userAttr')"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('wallet.selectWallet')" prop="walletType">
        <el-select
          style="width: 150px"
          v-model="queryParams.walletType"
          :placeholder="$t('common.select')"
        >
          <el-option
            v-for="item in walletTypeArr"
            :label="item.value"
            :value="item.key"
            :key="item.key"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('wallet.selectCurrency')" prop="currency">
        <el-select
          style="width: 150px"
          v-model="queryParams.currency"
          :placeholder="$t('common.select')"
        >
          <el-option
            v-for="item in currencyArr"
            :label="item.value"
            :value="item.key"
            :key="item.key"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >{{ $t('common.search') }}</el-button
        >
        <el-button icon="Refresh" @click="resetQuery(queryFormRef)"
          >{{ $t('common.reset') }}</el-button
        >
        <el-button type="warning" plain icon="Download" @click="handleExport"
          >{{$t('common.export')}}
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 表格数据 -->
    <el-table v-loading="state.getListLoading" :data="state.tableData">
      <el-table-column :label="$t('wallet.usersAccountID')" align="center" prop="accountNo" />
      <el-table-column :label="$t('wallet.usersState')" prop="userStatus" align="center">
        <template #default="scope">
          <el-tag
            :type="statusArr.find((i) => i.key === scope.row.userStatus)?.color"
          >
            {{ statusArr.find((i) => i.key === scope.row.userStatus)?.value }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.userNickname')" align="center" prop="userNickName" />
      <el-table-column :label="$t('common.userId')" align="center" prop="userIdNumber" />
      <el-table-column :label="$t('common.phoneNumber')" align="center" prop="mobile">
        <template #default="scope">
          <div>+{{ scope.row.dailCode }} {{ scope.row.mobile }}</div>
        </template>
      </el-table-column>
      <el-table-column :label="$t('wallet.usersLevel')" align="center" prop="userLevel">
        <template #default="scope">
          <div>
            {{ levelArr.find((i) => i.key === scope.row.userLevel)?.value }}
          </div>
        </template>
      </el-table-column>
      <el-table-column :label="$t('wallet.wallet')" prop="walletType" align="center">
        <template #default="scope">
          <div>
            {{
              walletTypeArr.find((i) => i.key === scope.row.walletType)?.value
            }}
          </div>
        </template>
      </el-table-column>
      <el-table-column :label="$t('wallet.currency')" prop="currency" align="center">
        <template #default="scope">
          <div>
            {{ currencyArr.find((i) => i.key === scope.row.currency)?.value }}
          </div>
        </template>
      </el-table-column>
      <el-table-column :label="$t('wallet.accountAmount')" align="center" prop="balance" />
      <el-table-column
        :label="$t('wallet.totalReceiveAmount')"
        align="center"
        prop="totalIncomeAmount"
      />
      <el-table-column
        :label="$t('wallet.totalTransferAmount')"
        align="center"
        prop="totalTransferAmount"
      />
      <el-table-column
        :label="$t('wallet.totalsendRedPacketAmount')"
        align="center"
        prop="totalRedPacketAmount"
      />
    </el-table>

    <pagination
      v-show="state.total > 0"
      :total="state.total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="AccountList">
import { reactive, ref, getCurrentInstance } from 'vue'
import { getWalletAccountList } from '@/api/purse/index'
import i18n from "../../i18n";

const { proxy } = getCurrentInstance()

const queryFormRef = ref()

const walletTypeArr = ref([
  // {
  //   key: null,
  //   value: i18n.global.t('common.all')
  // },
  {
    key: 'MOS',
    value: i18n.global.t('wallet.mosappWallet')
  }
  // {
  //   key: 'UPAY',
  //   value: 'U-Pay钱包'
  // }
])

const currencyArr = ref([
  {
    key: null,
    value: i18n.global.t('common.all')
  },
  {
    key: 'USD',
    value: 'USD'
  },
  {
    key: 'KHR',
    value: 'KHR'
  }
])

const statusArr = ref([
  {
    key: 0,
    value: i18n.global.t('userManage.normal'),
    color: 'success'
  },
  {
    key: 1,
    value: i18n.global.t('common.freeze'),
    color: 'danger'
  },
  {
    key: 2,
    value: i18n.global.t('userManage.deactivated'),
    color: 'info'
  }
])
const levelArr = ref([
  {
    key: 'NORMAL',
    value: i18n.global.t('wallet.normalUser')
  }
])

// 搜索参数
const queryParams = ref({
  walletType: 'MOS',
  currency: null,
  pageNum: 1,
  pageSize: 10
})

const state = reactive({
  showSearch: true,
  dialogVisible: false,
  dialogType: '',
  dialogTitle: '',
  row: {}
})

// 获取列表数据
function getList () {
  state.getListLoading = true
  const params = {
    ...queryParams.value
  }
  for (const key in params) {
    if (!params[key]) params[key] = null
  }
  getWalletAccountList(params)
    .then((response) => {
      state.tableData = response.data.records
      state.total = response.data.total
    })
    .finally(() => {
      state.getListLoading = false
    })
}
// 查询数据
function handleQuery () {
  queryParams.value.pageNum = 1
  getList()
}
// 重置查询
const resetQuery = (formEl) => {
  if (!formEl) return
  console.log(formEl)
  formEl.resetFields()
  getList()
}
function handleExport () {
  const params = {
    ...queryParams.value
  }
  for (const key in params) {
    if (!params[key]) params[key] = null
  }
  proxy.download(
    '/wallet/exportWalletAccountList',
    {
      ...params
    },
    `account_${new Date().getTime()}.xlsx`
  )
}

getList()
</script>
