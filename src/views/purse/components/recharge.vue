<template>
  <div>
    <el-descriptions  v-loading="state.getListLoading" :column="2" border>
      <el-descriptions-item :label="$t('wallet.orderID')">{{
        detail.orderNo
      }}</el-descriptions-item>
      <el-descriptions-item :label="$t('wallet.country')">{{
        countryCodeText(detail.country)
      }}</el-descriptions-item>
      <el-descriptions-item :label="$t('wallet.orderType')">{{
        orderTypeArr.find((i) => i.key === detail.transactionType)?.value
      }}</el-descriptions-item>
      <el-descriptions-item :label="$t('wallet.currency')">{{
        detail.currency
      }}</el-descriptions-item>
      <el-descriptions-item :label="$t('common.userNickname')">{{
        detail.userName
      }}</el-descriptions-item>
      <el-descriptions-item :label="$t('wallet.transactionAmount')">{{
        detail.transactionAmount
      }}</el-descriptions-item>
      <el-descriptions-item :label="$t('common.userId')">{{
        detail.userNumber
      }}</el-descriptions-item>
      <el-descriptions-item :label="$t('wallet.orderState')">{{
        statusArr.find((i) => i.key === detail.orderStatus)?.value
      }}</el-descriptions-item>
      <el-descriptions-item :label="$t('common.phoneNumber')"
        >+{{ detail.dailCode }} {{ detail.mobile }}</el-descriptions-item
      >
      <!-- <el-descriptions-item :label="$t('wallet.paymentMethod')">{{
        i18n.global.t('wallet.' + detail.payType)
      }}</el-descriptions-item> -->
      <el-descriptions-item :label="$t('wallet.transactionTime')">{{
        parseTime(detail.transactionTime)
      }}</el-descriptions-item>
      <!-- <el-descriptions-item
        v-if="detail.payType !== 'KHQR收款'"
        :label="$t('wallet.payAccount')"
        >+{{detail.dailCode}} {{ detail.mobile }}</el-descriptions-item
      >
      <el-descriptions-item :label="$t('common.remark')">{{
        detail.desc
      }}</el-descriptions-item> -->
      <!-- <el-descriptions-item
        v-if="detail.payType !== 'KHQR收款'"
        :label="$t('wallet.payAccountName')"
        >{{ detail.payAccountName }}</el-descriptions-item
      > -->
    </el-descriptions>
  </div>
</template>

<script setup name="FundWithdrawalReview">
import { reactive, ref, defineProps } from 'vue'
import { getRechargeOrderInfo } from '@/api/purse/index'
import { countryCode } from '@/utils/enum.js'
import i18n from "../../../i18n";

const props = defineProps(['row'])

// 搜索参数
const detail = ref({})

const state = reactive({
  getListLoading: true,
  tableData: [],
  total: 0
})
const orderTypeArr = ref([
  {
    key: 1,
    value: i18n.global.t('wallet.topUp')
  },
  {
    key: 2,
    value: i18n.global.t('wallet.transfer')
  },
  {
    key: 3,
    value: i18n.global.t('wallet.redPacket')
  },
  {
    key: 4,
    value: i18n.global.t('wallet.referralBonus')
  }
])
const statusArr = ref([
  {
    key: null,
    value: i18n.global.t('common.all')
  },
  {
    key: 'SUCCESS',
    value: i18n.global.t('wallet.succesfully')
  },
  {
    key: 'ING',
    value: i18n.global.t('wallet.inProgress')
  },
  {
    key: 'FAILURE',
    value: i18n.global.t('wallet.failed')
  }
])

function countryCodeText (row) {
  const list = countryCode.filter((i) => i.shortName === row)
  let txt = ''
  if (list.length) {
    txt=i18n.global.locale === 'en' ? list[0].en : list[0].name
  }
  return txt
}

// 获取列表数据
function getList () {
  state.getListLoading = true
  getRechargeOrderInfo({ id: props.row.id })
    .then((response) => {
      detail.value = { ...props.row, ...response.data }
    })
    .finally(() => {
      state.getListLoading = false
    })
}
getList()
</script>
<style scoped>
:deep(.el-descriptions__content.el-descriptions__cell.is-bordered-content) {
  width: 37%;
}

</style>
