<template>
  <div class="app-container home">
    <div class="common-module">
      <el-form
        :model="queryParams"
        ref="queryFormRef"
        :inline="true"
        class="search-head"
      >
        <el-form-item :label="$t('wallet.selectWallet')" prop="walletType">
          <el-select
            style="width: 150px"
            v-model="queryParams.walletType"
            :placeholder="$t('common.select')"
          >
            <el-option :label="$t('wallet.uPayWallet')" value="UPAY" />
            <el-option :label="$t('wallet.mosappWallet')" value="MOS" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('wallet.selectCurrency')" prop="currency">
          <el-select
            style="width: 150px"
            v-model="queryParams.currency"
            :placeholder="$t('common.select')"
          >
            <el-option label="USD" value="USD" />
            <el-option label="KHR" value="KHR" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('wallet.selectDate')" style="width: 350px" prop="timeRanges">
          <el-date-picker
            v-model="queryParams.timeRanges"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            :start-placeholder="$t('common.startTime')"
            :end-placeholder="$t('common.endTime')"
          >
          </el-date-picker>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery"
            >{{ $t('common.search') }}</el-button
          >
          <el-button icon="Refresh" @click="resetQuery(queryFormRef)"
            >{{ $t('common.reset') }}</el-button
          >
        </el-form-item>
      </el-form>
    </div>
    <div class="statistics-container common-module">
      <div class="content">
        <div
          class="card user-num"
          v-for="(item, index) in countArr"
          :key="index"
        >
          <div class="card-lf">
            <div class="icon-box">
              <el-icon
                ><WalletFilled v-if="item.type === 'money'" /><Histogram v-else
              /></el-icon>
            </div>
          </div>
          <div class="card-rh">
            <p>{{ item.title }}</p>
            <strong>{{item.type === 'money' ? state.nowData.unit : ''}} {{ state.nowData[item.key] }}</strong>
            <p>{{$t('wallet.addedYesterday')}}：{{item.type === 'money' ? state.yesterdayData.unit : ''}} {{ state.yesterdayData[item.key] }}</p>
          </div>
        </div>
      </div>
    </div>

    <div class="register-chart-container common-module">
      <div class="common-module-header">
        <div class="hd-lf">
          <h3 class="title">{{$t('wallet.accountBalance')}}</h3>
        </div>
        <div class="hd-rh">
          <div class="radio-group-container" style="width: 500px">
            <el-radio-group
              fill="#0bb976"
              @change="changeAmountType"
              v-model="amountQuery.dateType"
            >
              <el-radio-button label="sevenDay">{{$t('wallet.sevenDays')}}</el-radio-button>
              <el-radio-button label="fifteenDay">{{$t('wallet.fifteenDays')}}</el-radio-button>
              <el-radio-button label="oneMonth">{{$t('wallet.oneMonth')}}</el-radio-button>
              <el-radio-button label="threeMonth">{{$t('wallet.threeMonths')}}</el-radio-button>
              <el-radio-button label="oneYear">{{$t('wallet.oneYear')}}</el-radio-button>
            </el-radio-group>
          </div>
          <el-date-picker
            v-model="amountQuery.timeRanges"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            :start-placeholder="$t('common.startTime')"
            :end-placeholder="$t('common.endTime')"
            @change="changeAmountDate"
          >
          </el-date-picker>
        </div>
      </div>
      <div class="content">
        <div id="amount-chart"></div>
      </div>
    </div>
    <div class="register-chart-container common-module">
      <div class="common-module-header">
        <div class="hd-lf">
          <h3 class="title">{{$t('wallet.addedUsers')}}</h3>
        </div>
        <div class="hd-rh">
          <div class="radio-group-container" style="width: 500px">
            <el-radio-group
              fill="#0bb976"
              @change="changeUserType"
              v-model="userQuery.dateType"
            >
              <el-radio-button label="yesterDay">{{$t('wallet.yesterday')}}</el-radio-button>
              <el-radio-button label="today">{{$t('wallet.today')}}</el-radio-button>
              <el-radio-button label="sevenDay">{{$t('wallet.sevenDays')}}</el-radio-button>
              <el-radio-button label="threeMonth">{{$t('wallet.threeMonths')}}</el-radio-button>
              <el-radio-button label="oneYear">{{$t('wallet.oneYear')}}</el-radio-button>
            </el-radio-group>
          </div>
          <el-date-picker
            v-model="userQuery.timeRanges"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            :start-placeholder="$t('common.startTime')"
            :end-placeholder="$t('common.endTime')"
            @change="changeUserDate"
          >
          </el-date-picker>
        </div>
      </div>
      <div class="content">
        <div id="user-chart"></div>
      </div>
    </div>
  </div>
</template>

<script setup name="PurseDashboard">
import { ref, reactive, onMounted } from 'vue'
import echarts from '../../plugins/echarts'
import {
  getWalletStatistics,
  getWalletStatisticsYesterday,
  getWalletBalanceLineChart,
  getWalletUserCountLineChart
} from '@/api/purse/index.js'
import I18n from "../../i18n";
const { proxy } = getCurrentInstance()

const queryFormRef = ref()
// 搜索参数
const queryParams = ref({
  walletType: 'UPAY',
  currency: 'USD',
  timeRanges: []
})

const countArr = [
  {
    key: 'userBalance',
    type: 'money',
    title: I18n.global.t('wallet.accountBalance')
  },
  {
    key: 'rewardAmount',
    type: 'money',
    title: I18n.global.t('wallet.rewardAmount')
  },
  {
    key: 'userTransferAmount',
    type: 'money',
    title: I18n.global.t('wallet.userTransferAmount')
  },
  {
    key: 'redPacketAmount',
    type: 'money',
    title: I18n.global.t('wallet.redPacketAmount')
  },
  {
    key: 'tradeCount',
    type: 'number',
    title: I18n.global.t('wallet.transactionTimes')
  },
  {
    key: 'rewardCount',
    type: 'number',
    title: I18n.global.t('wallet.rewardCount')
  },
  {
    key: 'userTransferCount',
    type: 'number',
    title: I18n.global.t('wallet.userTransferTimes')
  },
  {
    key: 'redPacketCount',
    type: 'number',
    title: I18n.global.t('wallet.redPacketTimes')
  },
  {
    key: 'userCount',
    type: 'number',
    title: I18n.global.t('wallet.users')
  },
  {
    key: 'normalUserCount',
    type: 'number',
    title: I18n.global.t('wallet.basicUsers')
  },
  {
    key: 'kycUserCount',
    type: 'number',
    title: I18n.global.t('wallet.kycUsers')
  },
  {
    key: 'fullKycUserCount',
    type: 'number',
    title: I18n.global.t('wallet.fullKYCUsers')
  }
]

const state = reactive({
  nowData: [],
  yesterdayData: []
})

function getList () {
  const params = {
    ...queryParams.value
  }
  if (params.timeRanges && params.timeRanges.length !== 0) {
    params.startDate = +new Date(params.timeRanges[0]+' 00:00:00')
    params.endDate = +new Date(params.timeRanges[1]+' 23:59:59')
  }
  getWalletStatistics(params).then((response) => {
    state.nowData = {
      ...response.data,
      userBalance:
        queryParams.value.walletType === 'UPAY'
          ? '--'
          : response.data.userBalance,
      userRechargeAmount:
        queryParams.value.walletType === 'UPAY'
          ? '--'
          : response.data.userRechargeAmount,
      userRechargeCount:
        queryParams.value.walletType === 'UPAY'
          ? '--'
          : response.data.userRechargeCount,
      normalUserCount:
        queryParams.value.walletType === 'MOS'
          ? '--'
          : response.data.normalUserCount,
      kycUserCount:
        queryParams.value.walletType === 'MOS'
          ? '--'
          : response.data.kycUserCount,
      fullKycUserCount:
        queryParams.value.walletType === 'MOS'
          ? '--'
          : response.data.fullKycUserCount,
      unit: queryParams.value.currency === 'USD' ? '$' : '៛'
    }
  })
  getWalletStatisticsYesterday(params).then((response) => {
    state.yesterdayData = {
      ...response.data,
      userBalance:
        queryParams.value.walletType === 'UPAY'
          ? '--'
          : response.data.userBalance,
      userRechargeAmount:
        queryParams.value.walletType === 'UPAY'
          ? '--'
          : response.data.userRechargeAmount,
      userRechargeCount:
        queryParams.value.walletType === 'UPAY'
          ? '--'
          : response.data.userRechargeCount,
      normalUserCount:
        queryParams.value.walletType === 'MOS'
          ? '--'
          : response.data.normalUserCount,
      kycUserCount:
        queryParams.value.walletType === 'MOS'
          ? '--'
          : response.data.kycUserCount,
      fullKycUserCount:
        queryParams.value.walletType === 'MOS'
          ? '--'
          : response.data.fullKycUserCount,
      unit: queryParams.value.currency === 'USD' ? '$' : '៛'
    }
  })
}
// 查询数据
function handleQuery () {
  getList()
  changeAmountChart()
  changeUserChart()
}
// 重置查询
const resetQuery = (formEl) => {
  if (!formEl) return
  formEl.resetFields()
  handleQuery()
}

const amountQuery = reactive({
  dateType: 'sevenDay',
  timeRanges: []
})
const userQuery = reactive({
  dateType: 'yesterDay',
  timeRanges: []
})

let amountChart
let userChart

const changeAmountType = () => {
  amountQuery.timeRanges = []
  changeAmountChart()
}
const changeAmountDate = () => {
  amountQuery.dateType =
    amountQuery.timeRanges === null ? 'sevenDay' : 'timeRange'
  changeAmountChart()
}
const changeAmountChart = () => {
  const params = {
    ...amountQuery,
    ...queryParams.value
  }
  if (amountQuery.timeRanges && amountQuery.timeRanges.length !== 0) {
    params.startDate = +new Date(amountQuery.timeRanges[0])
    params.endDate = +new Date(amountQuery.timeRanges[1])
  }
  getWalletBalanceLineChart(params).then((response) => {
    const xA = []
    const yA = []
    response.data.forEach((v) => {
      xA.push(proxy.parseTime(v.timeRange, '{y}/{m}/{d}'));
      yA.push(v.number)
    })
    const option = {
      legend: {
        bottom: 20
      },
      xAxis: {
        type: 'category',
        data: xA
      },
      yAxis: {
        type: 'value'
      },
      tooltip: {
        trigger: 'axis'
      },
      grid: {
        left: '100px',
        right: '30px'
      },
      series: [
        {
          data: yA,
          type: 'line',
          itemStyle: {
            color: '#0bb976'
          }
        }
      ]
    }
    amountChart.setOption(option)
  })
}

const changeUserType = () => {
  userQuery.timeRanges = []
  changeUserChart()
}
const changeUserDate = () => {
  userQuery.dateType =
    userQuery.timeRanges === null ? 'yesterDay' : 'timeRange'
  changeUserChart()
}
const changeUserChart = () => {
  const params = {
    ...userQuery,
    ...queryParams.value
  }
  if (userQuery.timeRanges && userQuery.timeRanges.length !== 0) {
    params.startDate = +new Date(userQuery.timeRanges[0])
    params.endDate = +new Date(userQuery.timeRanges[1])
  }
  getWalletUserCountLineChart(params).then((response) => {
    const xA = []
    const yA = []
    const timeFormatStr = ['sevenDay', 'threeMonth', 'oneYear','timeRange'].includes(params.dateType) ? '{y}/{m}/{d}' : '{h}:{i}'
    response.data.forEach((v) => {
      xA.push(proxy.parseTime(v.timeRange, timeFormatStr));
      yA.push(v.number)
    })
    const option = {
      legend: {
        bottom: 20
      },
      xAxis: {
        type: 'category',
        data: xA
      },
      yAxis: {
        type: 'value'
      },
      tooltip: {
        trigger: 'axis'
      },
      grid: {
        left: '50px',
        right: '30px'
      },
      series: [
        {
          data: yA,
          type: 'line',
          itemStyle: {
            color: '#0bb976'
          }
        }
      ]
    }
    userChart.setOption(option)
  })
}

onMounted(() => {
  const amout = document.querySelector('#amount-chart')
  amout.removeAttribute('_echarts_instance_')
  amountChart = echarts.init(document.getElementById('amount-chart'))
  const user = document.querySelector('#user-chart')
  user.removeAttribute('_echarts_instance_')
  userChart = echarts.init(document.getElementById('user-chart'))
  handleQuery()
})
</script>

<style lang="scss" scoped>
.home {
  background: #f4f4f4;
  :deep(el-radio-button__inner:hover) {
    color: #35c758;
  }
  .common-module {
    margin-bottom: 20px;
    background: #fff;
    border-radius: 4px;
    .common-module-header {
      display: flex;
      align-items: center;
      padding: 15px 20px;
      .hd-lf {
        flex: 1;
      }
      .hd-rh {
        flex: 2;
        display: flex;
        justify-content: flex-end;
      }
    }
    .search-head {
      padding: 15px 20px;
      padding-bottom: 0;
    }
    .title {
      font-size: 16px;
      font-weight: bold;
      color: #101010;
    }

    .card {
      overflow: hidden;
      margin: 0 8px 8px;
      // border: 1px solid #e4e7ed;
    }

    @media screen and (max-width: 768px) {
      .card {
        width: 100% !important;
      }
    }
  }

  .statistics-container {
    background: #fff;
    .content {
      display: flex;
      padding: 0 20px;
      flex-wrap: wrap;
      // margin: 0 -8px;
      // flex-wrap: wrap;
    }
    .card {
      width: calc(25% - 15px);
      margin: 20px 20px 20px 0;
      text-align: center;
      color: #aeaeb2;
      font-size: 12px;
      display: flex;
      align-items: center;
      border-right: 1px solid #ddd;
      &:nth-child(4n) {
        margin-right: 0;
        border-right: 0;
      }
      &:nth-child(-n + 4) {
        .icon-box {
          background: #007aff;
        }
      }
      &:nth-child(n + 5) {
        .icon-box {
          background: #ff9502;
        }
      }
      &:nth-child(n + 9) {
        .icon-box {
          background: #35c758;
        }
      }
      strong {
        font-weight: 400;
        font-size: 18px;
        display: block;
        margin: 10px 0;
        color: #313131;
      }
    }
    .card-lf {
      // flex: 1;
      .icon-box {
        width: 55px;
        height: 55px;
        color: #fff;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
      }
    }
    .card-rh {
      flex: 1;
      text-align: left;
      padding-left: 20px;
    }
    // .user-num {
    //   background: linear-gradient(to right, #6a84fe, #24ccfd);
    // }
    // .group-num {
    //   background: linear-gradient(to right, #fe988f, #ff738a);
    // }
    // .message-num {
    //   background: linear-gradient(to right, #04bade, #20eca5);
    // }
  }

  .daibang-container {
    .content {
      display: flex;
      margin: 0 -8px;
      flex-wrap: wrap;
    }
    .card {
      display: flex;
      .icon-wrap {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100px;
        font-size: 26px;
        background: #ea5b56;
        color: #fff;
        &.tixiang {
          background-color: #fdb93a;
        }
      }
      .text-wrap {
        width: 150px;
        padding: 15px;
        font-size: 12px;
        strong {
          display: block;
          margin-bottom: 6px;
          font-size: 18px;
        }
      }
    }
  }

  .register-chart-container {
    .radio-group-container {
      text-align: right;
      .el-radio-group {
        margin-right: 20px;
      }
    }
    #amount-chart {
      height: 500px;
    }
    #user-chart {
      height: 500px;
    }
  }
}
</style>
