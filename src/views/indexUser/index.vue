<template>
  <div class="app-container consumer-container">
    <el-form
      :model="queryParams"
      ref="queryFormRef"
      v-show="state.showSearch"
      :inline="true"
    >
      <el-form-item label="">
        <el-select
          style="width: 150px"
          v-model="queryParams.searchKey"
          clearable
          class="m-2"
          :placeholder="$t('common.select')"
        >
          <el-option :label="$t('userManage.nickname')" value="nickName" />
          <el-option :label="$t('userManage.phoneNumber')" value="phone" />
          <!-- <el-option :label="$t('common.userId')" value="id" /> -->
        </el-select>
        <el-input
          v-model="queryParams.searchValue"
          :placeholder="$t('userManage.pleaseEnter')"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item
        :label="$t('userManage.registrationTime')"
        style="width: 368px"
      >
        <el-date-picker
          v-model="queryParams.timeRanges"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          :start-placeholder="$t('common.startDate')"
          :end-placeholder="$t('common.endDate')"
        >
        </el-date-picker>
      </el-form-item>

      <el-form-item :label="$t('userManage.lastOnlineTime')" style="width: 368px">
        <el-date-picker
          v-model="queryParams.upTimeRanges"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          :start-placeholder="$t('common.startDate')"
          :end-placeholder="$t('common.endDate')"
        >
        </el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >{{ $t("common.search") }}
        </el-button>
        <el-button icon="Refresh" @click="resetQuery">{{
          $t("common.reset")
        }}</el-button>
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['user:export']"
          >{{$t('common.export')}}
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 表格数据 -->
    <el-table
      v-loading="state.getListLoading"
      :data="state.tableData"
      @selection-change="handleSelectionChange"
    >
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <el-table-column
        label="ID"
        prop="idNumber"
        align="center"
        width="100"
      />
      <el-table-column
        :label="$t('userManage.nickname')"
        prop="nickname"
        align="center"
        width="100"
      />
      <el-table-column
        :label="$t('userManage.phoneNumber')"
        prop="phone"
        :show-overflow-tooltip="true"
        width="150"
      >
        <template #default="scope">
          <template v-if="scope.row.dialCode && scope.row.phone">
            <span v-if="scope.row.phone.includes('_')">{{
              `+${scope.row.dialCode} ${scope.row.phone.split("_")[1]}`
            }}</span>
            <span v-else>{{ `+${scope.row.dialCode} ${scope.row.phone}` }}</span>
          </template>
        </template>
      </el-table-column>
      <!--      <el-table-column :label="$t('common.userId')" prop="id" :show-overflow-tooltip="true" width="100" />-->
      <el-table-column
        :label="$t('userManage.country')"
        prop="dialCode"
        width="100"
      >
        <template #default="scope">
          <span>{{
            scope.row.dialCode ? countryCodeText(scope.row) : ""
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('userManage.avatar')"
        prop="headPortrait"
        width="150"
      >
        <template #default="scope">
          <el-image
            style="width: 60px; height: 60px"
            :src="scope.row.headPortrait"
            :zoom-rate="1.2"
            :preview-src-list="[scope.row.headPortrait]"
            :initial-index="4"
            fit="cover"
          />
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('userManage.friendCount')"
        prop="friendCount"
        align="center"
        width="100"
      >
        <template #default="scope">
          <span v-if="scope.row.friendCount === 0">{{
            scope.row.friendCount
          }}</span>
          <el-button
            v-else
            type="text"
            @click="showFriendsList(scope.row.id)"
            >{{ scope.row.friendCount }}</el-button
          >
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('userManage.registrationTime')"
        align="center"
        prop="createTime"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('userManage.appOnlineState')"
        prop="appOnLineState"
        align="center"
        width="100"
      >
        <template #default="scope">
          <span v-if="scope.row.appOnLineState === 1">{{
            $t("userManage.online")
          }}</span>
          <span v-if="scope.row.appOnLineState === 0">{{
            $t("userManage.offline")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('userManage.pcOnlineState')"
        prop="webOnLineState"
        align="center"
        width="100"
      >
        <template #default="scope">
          <span v-if="scope.row.webOnLineState === 1">{{
            $t("userManage.online")
          }}</span>
          <span v-if="scope.row.webOnLineState === 0">{{
            $t("userManage.offline")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('userManage.lastOnlineTime')"
        prop="lastUpTime"
        align="center"
        width="100"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.lastUpTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('userManage.userType')"
        prop="type"
        align="center"
        width="108"
      >
        <template #default="scope">
          <span  v-if="scope.row.type === 'NORMAL'">{{$t('userManage.normalUser')}}</span>
          <span  v-if="scope.row.type === 'GUEST'">{{$t('userManage.guest')}}</span>
        </template>
      </el-table-column>

      <el-table-column
        :label="$t('userManage.state')"
        prop="status"
        align="center"
        width="100"
      >
        <template #default="scope">
          <span style="color: red" v-if="scope.row.status === 2">{{
            $t("userManage.deactivated")
          }}</span>
          <span v-else-if="!scope.row.isFreeze">{{
            $t("userManage.normal")
          }}</span>
          <span v-else="scope.row.isFreeze">{{ $t("common.freeze") }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="state.total > 0"
      :total="state.total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
    <!-- 更多操作 对话框 -->
    <el-dialog
      v-if="state.moreDialogVisible"
      v-model="state.moreDialogVisible"
      :width="state.moreDialogWidth"
      append-to-body
      custom-class="el-dialog-no-header"
    >
      <FriendList
        v-if="state.moreDialogType === '好友列表'"
        :params="seleectedRowData"
        @close="closeMoreDialog"
      ></FriendList>
    </el-dialog>
  </div>
</template>

<script setup name="Consumer">
import { reactive, ref, getCurrentInstance } from "vue";
import { countryCode } from "@/utils/enum.js";
import i18n from "../../i18n";
import FriendList from '../consumer/components/friendList.vue'

import {
  listUser,
  getUserId
} from "@/api/consumer/imuser";
const props = defineProps({
  // 类型
  type: {
    type: Number,
    default: null
  }
})
const { proxy } = getCurrentInstance();

const queryFormRef = ref();
const formRef = ref();

// 搜索参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  searchKey: "nickName",
  searchValue: "",
  nickname: null,
  phone: null,
  id: null,
  ids: null,
  startTime: null,
  endTime: null,
  upStartTime:null,
  upEndTime:null,
  accountStatus: null,
  onLineState: null,
  isMember: null,
  timeRanges: [],
  upTimeRanges: []
});

const state = reactive({
  showSearch: true,
  getListLoading: true,
  tableData: [],
  total: 0,
  selectedIds: [],
  dialogVisible: false,
  dialogTitle: "",
  moreDialogTitle: "",
  moreDialogVisible: false,
  moreDialogType: "",
  moreDialogWidth: "500px",
});

// 获取用户id
async function getUserIdAPI(){
  const res = await getUserId(props.type)
  queryParams.value.ids = res
  getList()
}
getUserIdAPI()


// 获取列表数据
function getList() {
  state.getListLoading = true;
  if (queryParams.value.searchKey === "nickName") {
    queryParams.value.nickname = queryParams.value.searchValue;
    queryParams.value.phone = null;
    queryParams.value.id = null;
  }
  if (queryParams.value.searchKey === "phone") {
    queryParams.value.nickname = null;
    queryParams.value.phone = queryParams.value.searchValue;
    queryParams.value.id = null;
  }
  if (queryParams.value.searchKey === "id") {
    queryParams.value.nickname = null;
    queryParams.value.phone = null;
    queryParams.value.id = queryParams.value.searchValue;
  }
  if (queryParams.value.searchValue === "") {
    queryParams.value.nickname = null;
    queryParams.value.phone = null;
    queryParams.value.id = null;
  }
  const [startTime, endTime] = queryParams.value.timeRanges || [];
  queryParams.value.startTime =
    startTime === undefined ? null : +new Date(startTime + " 00:00:00");
  queryParams.value.endTime =
    endTime === undefined ? null : +new Date(endTime + " 23:59:59");
      const [upStartTime, upEndTime] = queryParams.value.upTimeRanges || [];
  queryParams.value.upStartTime =
    upStartTime === undefined ? null : +new Date(upStartTime + " 00:00:00");
  queryParams.value.upEndTime =
    upEndTime === undefined ? null : +new Date(upEndTime + " 23:59:59");
  listUser(queryParams.value)
    .then((res) => {
      state.tableData = res.data.records;
      state.total = res.data.total;
    })
    .finally(() => (state.getListLoading = false));
}

// 查询数据
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

function nameTextStr(row) {
  const a1 = row.firstName ? row.firstName : "";
  const a2 = row.lastName ? row.lastName : "";
  const a3 = a1 && a2 ? "_" : "";
  return a1 + a3 + a2;
}
// 重置查询
function resetQuery() {
  if (queryFormRef.value) {
    queryFormRef.value.resetFields();
  }
  queryParams.value.searchValue = "";
  queryParams.value.timeRanges = [];
  queryParams.value.upTimeRanges = [];
  queryParams.value.accountStatus = null;
  queryParams.value.onLineState = null;
  queryParams.value.isMember = null;
  queryParams.value.searchKey = null;
  queryParams.value.status = null;
  queryParams.value.appOnLineState = null;
  queryParams.value.webOnLineState = null;

  handleQuery();
}

// 导出按钮操作
function handleExport() {
  if (queryParams.value.searchKey === "nickName") {
    queryParams.value.nickname = queryParams.value.searchValue;
    queryParams.value.phone = null;
    queryParams.value.id = null;
  }
  if (queryParams.value.searchKey === "phone") {
    queryParams.value.nickname = null;
    queryParams.value.phone = queryParams.value.searchValue;
    queryParams.value.id = null;
  }
  if (queryParams.value.searchKey === "id") {
    queryParams.value.nickname = null;
    queryParams.value.phone = null;
    queryParams.value.id = queryParams.value.searchValue;
  }
  if (queryParams.value.searchValue === "") {
    queryParams.value.nickname = null;
    queryParams.value.phone = null;
    queryParams.value.id = null;
  }
  const [startTime, endTime] = queryParams.value.timeRanges || [];
  queryParams.value.startTime =
    startTime === undefined ? null : startTime + " 00:00:00";
  queryParams.value.endTime =
    endTime === undefined ? null : endTime + " 23:59:59";

  const [upStartTime, upEndTime] = queryParams.value.upTimeRanges || [];
  queryParams.value.upStartTime =
    upStartTime === undefined ? null : upStartTime + " 00:00:00";
  queryParams.value.upEndTime =
    upEndTime === undefined ? null : upEndTime + " 23:59:59";
  proxy.download(
    "/imUser/exportUser",
    {
      ...queryParams.value,
    },
    `user_${new Date().getTime()}.xlsx`
  );
}

function countryCodeText(row) {
  const list = countryCode.filter((i) => i.tel === row.dialCode);
  let txt = "";
  if (list.length) {
    txt = i18n.global.locale === "en" ? list[0].en : list[0].name;
  }
  return txt;
}


const seleectedRowData = ref({
  userId: null,
  nickname: null
})

// 好友列表
function showFriendsList (userId) {
  seleectedRowData.value.id = userId
  state.moreDialogWidth = '1200px'
  state.moreDialogType = '好友列表'
  state.moreDialogVisible = true
}
</script>
<style>
.consumer-container .el-table .el-table__cell {
  z-index: unset;
}
.el-dialog-no-header .el-table .el-table__cell {
  z-index: unset !important;
}
</style>
