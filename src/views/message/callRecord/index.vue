<template>
  <div class="app-container">
    <!-- 搜索 -->
    <el-form :model="queryParams" ref="queryFormRef" v-show="state.showSearch" :inline="true">
      <el-form-item :label="$t('msg.senderID')">
        <el-input v-model.trim="queryParams.fromClientId" :placeholder="$t('common.pleaseEnter')" clearable
          style="width: 120px" @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('msg.receiverID')">
        <el-input v-model.trim="queryParams.toClientId" :placeholder="$t('common.pleaseEnter')" clearable
          style="width: 120px" @keyup.enter="handleQuery" />
      </el-form-item>

      <el-form-item :label="$t('msg.roomID')">
        <el-input v-model.trim="channelId" :placeholder="$t('common.pleaseEnter')" clearable style="width: 120px"
          @keyup.enter="handleQuery" />
      </el-form-item>

      <el-form-item :label="$t('msg.type')">
        <el-select style="width: 120px" v-model="queryParams.callType" class="m-2" :placeholder="$t('common.select')">
          <el-option v-for="item in callTypeOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">{{ $t('common.search') }}</el-button>
        <el-button icon="Refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <!-- 功能区 -->
    <!-- <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['system:role:export']">{{$t('common.export')}}</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="state.showSearch" @queryTable="getList"></right-toolbar>
    </el-row> -->

    <!-- 表格数据 -->
    <el-table v-loading="state.getListLoading" :data="state.tableData" @selection-change="handleSelectionChange">
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <el-table-column :label="$t('msg.senderID')" align="center" prop="fromClientId" />
      <el-table-column :label="$t('msg.senderName')" align="center" prop="fromClientName" />
      <el-table-column :label="$t('msg.receiverID')" align="center" prop="toClientId" />
      <el-table-column :label="$t('msg.receiverName')" align="center" prop="toClientName" />
      <el-table-column :label="$t('msg.roomID')" align="center" prop="channelId" />
      <el-table-column :label="$t('msg.type')" align="center">
        <template #default="scope">
          <span v-if="scope.row.callType===1">{{$t('msg.video')}}</span>
          <span v-else>{{$t('msg.audio')}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('msg.joinTime')" align="center">
        <template #default="scope">
          <span v-if="scope.row.startTime!=null">{{parseTime(scope.row.startTime)}}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('msg.endTime')" align="center" prop="endTime">
        <template #default="scope">
          <span v-if="scope.row.endTime!=null">{{parseTime(scope.row.endTime)}}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('msg.duration')" align="center" width="100">
        <template #default="scope">
          <span v-if="scope.row.endTime != null && scope.row.startTime != null">
            {{ formatDuration((new Date(scope.row.endTime) - new Date(scope.row.startTime)) / 1000) }}
          </span>
          <span v-else>00:00:00</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('msg.startTime')" align="center" prop="createTime">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="state.total > 0" :total="state.total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />
  </div>
</template>

<script setup name="User">
import { reactive, ref, getCurrentInstance } from 'vue'
import { pageRtcRecords } from '@/api/message/message.js'
import i18n from "../../../i18n";

const proxy = getCurrentInstance()

const queryFormRef = ref()

// 搜索参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  fromClientId: null,
  toClientId: null,
  channelId: null,
  callType: '',
  isFolder: false
})

const callTypeOptions = [
  {
    label: i18n.global.t('common.all'),
    value: ''
  },
  {
    label: i18n.global.t('msg.video'),
    value: '1'
  },
  {
    label: i18n.global.t('msg.audio'),
    value: '2'
  }
]

const state = reactive({
  showSearch: true,
  getListLoading: true,
  tableData: [],
  total: 0,
  selectedIds: [],
  dialogVisible: false,
  dialogTitle: ''
})

const channelId = computed({
  set(v) {
    if (/^[0-9]{0,}$/.test(v)) queryParams.value.channelId = v
  },
  get() {
    return queryParams.value.channelId
  }
})

// 获取列表数据
function getList() {
  state.getListLoading = true
  const params = queryParams.value
  for (const key in params) {
    if (params[key] === "") {
      params[key] = null;
    }
  }
  pageRtcRecords(queryParams.value)
    .then((response) => {
      state.total = response.data.total
      state.tableData = response.data.records
    })
    .finally(() => (state.getListLoading = false))
}
// 查询数据
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}
// 重置查询
function resetQuery() {
  if (queryFormRef.value) {
    queryFormRef.value.resetFields()
  }
  queryParams.value.fromClientId = null
  queryParams.value.toClientId = null
  queryParams.value.channelId = null
  queryParams.value.callType = null
  handleQuery()
}

function formatDuration(seconds) {
  const hrs = Math.floor(seconds / 3600);
  const mins = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;
  return (
    pad(hrs, 2) + ':' +
    pad(mins, 2) + ':' +
    pad(secs, 2)
  );
}

function pad(value, width, paddingChar = '0') {
  value = value + '';
  return value.length >= width ? value : new Array(width - value.length + 1).join(paddingChar) + value;
}
// 导出按钮操作
// function handleExport () {
//   proxy.download(
//     'system/role/export',
//     {
//       ...queryParams.value
//     },
//     `role_${new Date().getTime()}.xlsx`
//   )
// }
// // 多选框选中数据
// function handleSelectionChange (selection) {
//   state.selectedIds = selection.map((item) => item.roleId)
// }

getList()
</script>
