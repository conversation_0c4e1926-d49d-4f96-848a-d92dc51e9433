<template>
  <div class="app-container">
    <!-- 搜索 -->
    <el-form
      :model="queryParams"
      ref="queryFormRef"
      v-show="state.showSearch"
      :inline="true"
    >
      <el-form-item :label="$t('msg.senderID')">
        <el-input
          v-model="queryParams.senderIdNumber"
          :placeholder="$t('common.pleaseEnter')"
          clearable
          style="width: 120px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('msg.chatID_GroupName')">
        <el-select
          v-model="queryParams.conversationId"
          clearable
          filterable
          remote
          reserve-keyword
          :placeholder="$t('common.pleaseEnter')"
          :remote-method="getConversation"
          :loading="state.loading">
          <el-option
            v-for="item in state.options"
            :key="item.id"
            :label="item.chatType == 2||item.chatType ==7 ? item.name : item.id"
            :value="item.id">
            <span style="float: left">{{ item.id }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px" v-if="item.chatType == 2||item.chatType ==7"> {{ item.name }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('msg.messageType')">
        <el-select
          style="width: 120px"
          clearable
          v-model="queryParams.msgType"
          class="m-2"
          :placeholder="$t('common.select')"
        >
          <el-option
            v-for="item in messageTypeEnum"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('msg.encryptedType')">
        <el-select
          style="width: 120px"
          clearable
          v-model="queryParams.encryptType"
          class="m-2"
          :placeholder="$t('common.select')"
        >
          <el-option :label="$t('msg.encrypted')" :value="true" />
          <el-option :label="$t('msg.unencrypted')" :value="false" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('msg.sendTimeRange')">
        <el-date-picker
          v-model="queryParams.sendDate"
          value-format="YYYY-MM-DD"
          type="daterange"
          :start-placeholder="$t('common.startDate')"
          :end-placeholder="$t('common.endDate')"
          :default-value="[new Date(), new Date()]"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >{{ $t('common.search') }}</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <!-- 功能区 -->
    <el-row style="height: 30px" :gutter="10" class="mb8">
      <right-toolbar
        v-model:showSearch="state.showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 表格数据 -->
    <el-table
      v-loading="state.getListLoading"
      :data="state.tableData"
      @selection-change="handleSelectionChange"
    >
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <el-table-column :label="$t('msg.senderID')" prop="senderIdNumber" width="120" />
      <el-table-column
        :label="$t('msg.senderName')"
        prop="fromUserName"
        align="center"
        width="100"
      />
      <el-table-column
        :label="$t('msg.groupNickName')"
        prop="groupName"
        align="center"
        width="100"
      />
      <el-table-column
        :label="$t('msg.messageContent')"
        prop="content"
        align="center"
        width="150"
        show-overflow-tooltip
      >
        <template #default="scope">
          <span v-if="scope.row.encryptType === 'ENCRYPTED'"
            >{{ $t('msg.notShow') }}</span
          >
          <span v-else-if="scope.row.content&&scope.row.msgType===-1">{{ scope.row.content }}</span>
          <el-button type="text" v-else @click="reviewMessageHandler(scope.row)"
            >{{ $t('msg.view') }}</el-button
          >
        </template>
      </el-table-column>
      <el-table-column :label="$t('msg.messageContent')" align="center" width="120">
        <template #default="scope">
          <el-button @click="copyContent(scope.row.content)" v-if="scope.row.content">{{ $t('msg.copy') }}</el-button>
        </template>
      </el-table-column>
      <el-table-column :label="$t('msg.messageType')" prop="msgType" align="center">
        <template #default="scope">
          {{ msgTypeStr(scope.row) }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('msg.chatType')" prop="chatType" align="center">
        <template #default="scope">
          <span v-if="scope.row.encryptType === 'ENCRYPTED'">{{ $t('msg.encryptionGroup') }}</span>
          <span v-else>{{ chatTypeStr(scope.row) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('msg.encryptedType')" align="center" prop="encryptType">
        <template #default="scope">
          <span v-if="scope.row.encryptType === 'ENCRYPTED'">{{ $t('msg.encrypted') }}</span>
          <span v-else>{{ $t('msg.unencrypted') }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('msg.sendTime')" align="center" prop="msgSendTime">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column :label="$t('msg.readState')" align="center" prop="readStatus" width="90">
        <template #default="scope">
          <span v-if="scope.row.readStatus === 0">{{$t('msg.unread')}}</span>
          <span v-if="scope.row.readStatus === 1">{{$t('msg.read')}}</span>
        </template>
      </el-table-column> -->
      <el-table-column :label="$t('msg.deleteState')" align="center" prop="deleted">
        <template #default="scope">
          <span v-if="scope.row.deleted === 0">{{$t('msg.notDeleted')}}</span>
          <span v-if="scope.row.deleted === 1">{{$t('msg.deleted')}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('msg.recallState')" align="center" prop="withdraw">
        <template #default="scope">
          <span v-if="scope.row.withdraw === false">{{$t('msg.notWithdrawn')}}</span>
          <span v-if="scope.row.withdraw === true">{{$t('msg.Withdrawn')}}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="state.total > 0"
      :total="state.total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog
      :title="$t('common.view')"
      v-model="state.dialogVisible"
      width="600px"
      :before-close="handleClose"
    >
      <div class="dialog-content">
        <MsgTypeView :selectedRowData="state.selecteDataRow" v-if="state.dialogVisible"/>
      </div>
    </el-dialog>
  </div>
</template>

<script setup name="User">
import { reactive, ref, getCurrentInstance } from 'vue'
import { pageGroupChat, getConversationNameAndId } from '@/api/message/message.js'
import { useClipboard } from '@vueuse/core'
import { ElMessage } from 'element-plus'
import { messageTypeEnum, chatTypeEnum } from '@/utils/enum.js'
import MsgTypeView from '@/views/assistant/components/MsgTypeView.vue'
import i18n from "../../../i18n";

const proxy = getCurrentInstance()

const queryFormRef = ref()

// 搜索参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  senderIdNumber: null,
  conversationId: null,
  msgType: null,
  chatType: [2],
  groupName: null,
  encryptType: null,
  sendDate: null,
  id: ''
})

// 搜索群主id和群主昵称参数
const queryKeyWordParams = ref({
  keyword: null
})

const state = reactive({
  showSearch: true,
  getListLoading: true,
  tableData: [],
  total: 0,
  selectedIds: [],
  dialogVisible: false,
  dialogTitle: '',
  selecteDataRow: {},
  contentJson: {},
  options: [],
  loading: false
})

// 获取列表数据
function getList () {
  state.getListLoading = true
  const params = queryParams.value
  for (const key in params) {
    if (!params[key] && params[key] !== false) params[key] = null
  }
  pageGroupChat(queryParams.value)
    .then((response) => {
      state.tableData = response.data.records
      state.total = response.data.total
    })
    .finally(() => (state.getListLoading = false))
}
// 查询数据
function handleQuery () {
  queryParams.value.pageNum = 1
  if (queryParams.value.sendDate) {
    queryParams.value.beginTime = +new Date(queryParams.value.sendDate[0]+' 00:00:00')
    queryParams.value.endTime = +new Date(queryParams.value.sendDate[1]+' 23:59:59')
  } else {
    queryParams.value.beginTime = null
    queryParams.value.endTime = null
  }
  getList()
}

/** 搜索群昵称或接收者ID */
function getConversation(query) {
    if (query !== '') {
      state.loading = true;
      setTimeout(() => {
        queryKeyWordParams.value.keyword = query;
        getConversationNameAndId(queryKeyWordParams.value)
          .then((result) => {
            state.options = result.data;
            console.log(result.data);
          })
          .finally(() => {
            state.loading = false;
          });
      }, 200);
    } else {
      state.options = [];
    }
  }
function msgTypeStr (row) {
  let txt = row.msgType
  const list = messageTypeEnum.filter((i) => i.value === row.msgType)
  if (list?.length) {
    txt = list[0].label
  }
  return txt
}

function chatTypeStr (row) {
  let txt = row.chatType
  const list = chatTypeEnum.filter((i) => i.value === row.chatType)
  if (list?.length) {
    txt = list[0].label
  }
  return txt
}

// 重置查询
function resetQuery () {
  if (queryFormRef.value) {
    queryFormRef.value.resetFields()
  }
  queryParams.value.senderIdNumber = null
  queryParams.value.conversationId = null
  queryParams.value.msgType = null
  queryParams.value.encryptType = null
  queryParams.value.groupName = null
  queryParams.value.sendDate = null
  queryParams.value.beginTime = null
  queryParams.value.endTime = null
  handleQuery()
}

// 删除按钮操作
function handleDelete (row) {
  // const roleIds = row.roleId || ids.value
  // proxy.$modal.confirm('是否确认删除角色编号为"' + roleIds + '"的数据项?').then(function () {
  //   return delRole(roleIds)
  // }).then(() => {
  //   getList()
  //   proxy.$modal.msgSuccess(i18n.global.t('common.success'))
  // }).catch(() => {})
}
// 导出按钮操作
function handleExport () {
  proxy.download(
    'system/role/export',
    {
      ...queryParams.value
    },
    `role_${new Date().getTime()}.xlsx`
  )
}
// 多选框选中数据
function handleSelectionChange (selection) {
  state.selectedIds = selection.map((item) => item.roleId)
}

function copyContent (text) {
  const InputDom = document.createElement('input')
  InputDom.value = text
  document.body.appendChild(InputDom)
  InputDom.select() // 选择对象
  document.execCommand('Copy') // 执行浏览器复制命令
  InputDom.remove()
  ElMessage.success(i18n.global.t('msg.copied'))
}

function reviewMessageHandler (row) {
  console.log(row)
  state.selecteDataRow = row
  state.contentJson = JSON.parse(row.contentJson)
  state.dialogVisible = true
  console.log(state.contentJson)
}

function faceMadeUrl (item) {
  // 地址生成规则：存储域名/桶名 / md5的末尾三位 / md5的第二个末尾三位 / jpeg_md5（png_md5、gif_md5）具体值
  // 如：https://test-file.wecloud.cn/uim-phiz/a49/53a/fec34cb3fbb54952fee445753953aa49.jpg
  const { faceMd5, faceType, faceWidth, faceHeight } = item.attrs
  console.log(import.meta.env)
  const host = import.meta.env.VITE_APP_MINIO_ENDPOINT
  const addr1 = 'mos-phiz'
  const addr2 = faceMd5.substr(-3)
  const addr3 = faceMd5.substr(-6, 3)
  const url = `${host}/${addr1}/${addr2}/${addr3}/${faceMd5}.${faceType}`
  console.log(url)
  return url
  // return {
  //   url,
  //   faceWidth,
  //   faceHeight
  // }
}

getList()
</script>
<style lang="scss" scoped>
.dialog-content {
  padding-bottom: 30px;
  display: flex;
  justify-content: center;
 .el-image{
    width: 200px;
  }
}
.dialog-footer {
  display: flex;
  justify-content: center;
}
</style>
