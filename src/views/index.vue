<template>
  <div class="app-container home">
    <div class="statistics-container common-module">
      <!-- <h3 class="title">统计</h3> -->
      <div class="content">
        <div class="card user-num">
          <div class="card-lf">
            <div class="icon-box box1">
              <el-icon><Histogram /></el-icon>
            </div>
          </div>
          <div class="card-rh">
            <p>{{ $t("homePage.totalUsers") }}</p>
            <strong>{{ state.countInfo.user }}</strong>
          </div>
        </div>

        <div class="card group-num">
          <div class="card-lf">
            <div class="icon-box box2">
              <el-icon><Histogram /></el-icon>
            </div>
          </div>
          <div @click="openTableDialog(1)" class="card-rh pointer">
            <p>{{ $t("homePage.newUsersToday") }}</p>
            <strong>{{ state.countInfo.todayRegisterUser }}</strong>
          </div>
        </div>
        <div class="card message-num">
          <div class="card-lf">
            <div class="icon-box box3">
              <el-icon><Histogram /></el-icon>
            </div>
          </div>
          <div @click="openTableDialog(2)" class="card-rh pointer">
            <p>{{ $t("homePage.newUsersYesterday") }}</p>
            <strong>{{ state.countInfo.yesterdayRegisterUser }}</strong>
          </div>
        </div>
        <div class="card message-num">
          <div class="card-lf">
            <div class="icon-box box4">
              <el-icon><Histogram /></el-icon>
            </div>
          </div>
          <div @click="openTableDialog(3)" class="card-rh pointer">
            <p>{{ $t("homePage.activeUsersToday") }}</p>
            <strong>{{ state.countInfo.todayActiveUser }}</strong>
          </div>
        </div>
        <div class="card message-num">
          <div class="card-lf">
            <div class="icon-box box5">
              <el-icon><Histogram /></el-icon>
            </div>
          </div>
          <div @click="openTableDialog(4)" class="card-rh pointer">
            <p>{{ $t("homePage.activeUsersYesterday") }}</p>
            <strong>{{ state.countInfo.yesterdayActiveUser }}</strong>
          </div>
        </div>
      </div>
    </div>

    <div class="register-chart-container common-module">
      <div class="common-module-header">
        <div class="hd-lf">
          <h3 class="title">{{ $t("homePage.registeredUsers") }}</h3>
        </div>
        <div class="hd-rh">
          <el-select
            v-model="state.registerForm.sourceType"
            :placeholder="$t('common.select')"
            @change="changeRegisterSource"
            style="margin-right: 20px; width: 80px"
          >
            <el-option key="0" :label="$t('common.all')" value="0"></el-option>
          </el-select>
          <div class="radio-group-container">
            <el-radio-group
              fill="#0bb976"
              @change="changeRegisterType"
              v-model="state.registerForm.type"
            >
              <el-radio-button label="yesterday">{{
                $t("homePage.yesterday")
              }}</el-radio-button>
              <el-radio-button label="today">{{
                $t("homePage.today")
              }}</el-radio-button>
              <el-radio-button label="sevenDay">{{
                $t("homePage.sevenDays")
              }}</el-radio-button>
              <el-radio-button label="threeMonth">{{
                $t("homePage.threeMonths")
              }}</el-radio-button>
              <el-radio-button label="oneYear">{{
                $t("homePage.oneYear")
              }}</el-radio-button>
            </el-radio-group>
          </div>
          <el-date-picker
            @change="changeRegisterDate"
            v-model="state.registerForm.date"
            type="date"
            :placeholder="$t('common.selectDate')"
          />
        </div>
      </div>
      <div class="content">
        <div id="register-chart"></div>
      </div>
    </div>
    <div class="common-module register-chart-container">
      <div class="common-module-header">
        <div class="hd-lf">
          <h3 class="title">{{ $t("homePage.activeUsersEcharts") }}</h3>
        </div>
        <div class="hd-rh">
          <el-select
            v-model="state.userActivity.deviceType"
            :placeholder="$t('common.select')"
            @change="changeUserActiveDevice"
            style="margin-right: 20px; width: 80px"
          >
            <el-option
              v-for="item in deviceTypeOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <div class="radio-group-container">
            <el-radio-group
              fill="#0bb976"
              @change="changeUserActiveCountType"
              v-model="state.userActivity.type"
            >
              <el-radio-button label="yesterday">{{
                $t("homePage.yesterday")
              }}</el-radio-button>
              <el-radio-button label="today">{{
                $t("homePage.today")
              }}</el-radio-button>
              <el-radio-button label="sevenDay">{{
                $t("homePage.sevenDays")
              }}</el-radio-button>
              <el-radio-button label="threeMonth">{{
                $t("homePage.threeMonths")
              }}</el-radio-button>
              <el-radio-button label="oneYear">{{
                $t("homePage.oneYear")
              }}</el-radio-button>
            </el-radio-group>
          </div>
          <el-date-picker
            @change="changeUserActiveCountDate"
            v-model="state.userActivity.date"
            type="date"
            :placeholder="$t('common.selectDate')"
          />
        </div>
      </div>
      <div class="content">
        <div id="active-chart"></div>
      </div>
    </div>
    <div class="common-module register-chart-container">
      <div class="common-module-header">
        <div class="hd-lf">
          <h3 class="title">{{ $t("homePage.retentionRate") }}</h3>
        </div>
        <div class="hd-rh">
          <el-select
            v-model="state.userRetention.retentionType"
            :placeholder="$t('common.select')"
            @change="changeRetentionType"
            style="margin-right: 20px; width: 200px"
          >
            <el-option
              key="1"
              :label="$t('homePage.nextDayRetentionRate')"
              value="1"
            ></el-option>
            <el-option
              key="2"
              :label="$t('homePage.sevenDayRetentionRate')"
              value="7"
            ></el-option>
            <el-option
              key="3"
              :label="$t('homePage.thirtyDayRetentionRate')"
              value="30"
            ></el-option>
          </el-select>
          <div class="radio-group-container">
            <el-radio-group
              fill="#0bb976"
              @change="changeUserRetentionCountType"
              v-model="state.userRetention.type"
            >
              <el-radio-button label="sevenDay">{{
                $t("homePage.sevenDays")
              }}</el-radio-button>
              <el-radio-button label="thirtyDay">{{
                $t("homePage.thirtyDays")
              }}</el-radio-button>
              <el-radio-button label="ninetyDay">{{
                $t("homePage.ninetyDays")
              }}</el-radio-button>
            </el-radio-group>
          </div>
          <el-date-picker
            @change="changeUserRetentionCountDate"
            v-model="state.userRetention.date"
            type="date"
            :placeholder="$t('common.selectDate')"
          />
        </div>
      </div>
      <div class="content">
        <div id="retention-chart"></div>
      </div>
    </div>
  <el-dialog
    v-model="state.dialogVisible"
    :title="state.type==1?$t('homePage.newUsersToday')
    :(state.type==2?$t('homePage.newUsersYesterday')
    :(state.type==3?$t('homePage.activeUsersToday')
    :(state.type==4?$t('homePage.activeUsersYesterday'):(''))))"
    width="1500px"
    append-to-body
    destroy-on-close
  >
    <indexUser :type="state.type" />
  </el-dialog>
  </div>
</template>

<script setup name="Index">
// import { right } from '@popperjs/core'
import { ref, reactive, onMounted } from "vue";
import echarts from "../plugins/echarts";
import i18n from "../i18n";
import {
  getUserRegistry,
  userActiveCount,
  userRetentionCount,
  groupCount,
} from "@/api/index.js";
import IndexUser from "@/views/indexUser";

const { proxy } = getCurrentInstance()
const state = reactive({
  countInfo: {
    group: 0,
    todayActiveUser: 0,
    todayRegisterUser: 0,
    user: 0,
    yesterdayActiveUser: 0,
    yesterdayRegisterUser: 0,
  },
  registerForm: {
    type: "yesterday",
    date: "",
    sourceType: "0",
  },
  userActivity: {
    type: "yesterday",
    date: "",
    deviceType: "0",
  },
  userRetention: {
    type: "sevenDay",
    date: "",
    retentionType: "1",
  },
  dialogVisible: false,
  type: null
});

// 设别类型
const deviceTypeOption = [
  { label: i18n.global.t("common.all"), value: "0" },
  { label: "iOS", value: "1" },
  { label: "Android", value: "2" },
  { label: "Mac", value: "5" },
  { label: "Windows", value: "4" },
];

// 来源类型
const sourceTypeMap = {
  0: i18n.global.t("homePage.unknown"),
  1: "Fackbok",
};

let registerChart;
let activeChart;
let retentionChart;

const changeRegisterType = () => {
  state.registerForm.date = "";
  changeRegisterChart(state.registerForm.type);
};
const changeRegisterDate = () => {
  state.registerForm.type = "";
  const queryValue = getUtcTimestamp(state.registerForm.date);
  changeRegisterChart(queryValue);
};
const changeRegisterSource = () => {
  let queryValue = "";
  if (state.registerForm.date) {
    queryValue = getUtcTimestamp(state.registerForm.date);
  }
  if (state.registerForm.type) {
    queryValue = state.registerForm.type;
  }
  changeRegisterChart(queryValue);
};
const changeRegisterChart = (queryValue) => {
  const params = {
    queryValue,
    sourceType: state.registerForm.sourceType,
  }
  getUserRegistry(params).then((response) => {
    const xA = [];
    const yA = [];
    const timeFormatStr = ['sevenDay', 'threeMonth', 'oneYear'].includes(queryValue) ? '{y}/{m}/{d}' : '{h}:{i}'
    response.forEach((v) => {
      xA.push(proxy.parseTime(v.timeRange, timeFormatStr));
      yA.push(v.number);
    });
    const option = {
      legend: {
        bottom: 20,
      },
      xAxis: {
        type: "category",
        data: xA,
      },
      yAxis: {
        type: "value",
      },
      tooltip: {
        trigger: "axis",
      },
      grid: {
        left: "70px",
        right: "20px",
      },
      series: [
        {
          name: i18n.global.t("homePage.registeredUsersToday"),
          data: yA,
          type: "line",
          itemStyle: {
            color: "#0bb976",
          },
        },
      ],
    };
    registerChart.setOption(option);
  });
};

const changeUserActiveCountType = () => {
  state.userActivity.date = "";
  changeUserActiveCount(state.userActivity.type);
};
const changeUserActiveCountDate = () => {
  state.userActivity.type = "";
  const queryValue = getUtcTimestamp(state.userActivity.date);
  changeUserActiveCount(queryValue);
};
const changeUserActiveDevice = () => {
  let queryValue = "";
  if (state.userActivity.date) {
    queryValue = getUtcTimestamp(state.userActivity.date);
  }
  if (state.userActivity.type) {
    queryValue = state.userActivity.type;
  }
  changeUserActiveCount(queryValue);
};
const changeUserActiveCount = (queryValue) => {
  userActiveCount({
    queryValue,
    deviceType: state.userActivity.deviceType,
  }).then((response) => {
    const xA = [];
    const yA = [];
    const timeFormatStr = ['sevenDay', 'threeMonth', 'oneYear'].includes(queryValue) ? '{y}/{m}/{d}' : '{h}:{i}'
    response.forEach((v) => {
      xA.push(proxy.parseTime(v.timeRange, timeFormatStr));
      yA.push(v.number);
    });
    const option = {
      legend: {
        bottom: 20,
      },
      xAxis: {
        type: "category",
        data: xA,
      },
      yAxis: {
        type: "value",
      },
      tooltip: {
        trigger: "axis",
      },
      grid: {
        left: "50px",
        right: "20px",
      },
      series: [
        {
          name: i18n.global.t("homePage.activeUsers"),
          data: yA,
          type: "bar",
          itemStyle: {
            color: "#ff9502",
          },
        },
      ],
    };
    activeChart.setOption(option);
  });
};

const changeUserRetentionCountType = () => {
  state.userRetention.date = "";
  changeUserRetentionCount(state.userRetention.type);
};
const changeUserRetentionCountDate = () => {
  state.userRetention.type = "";
  const queryValue = getUtcTimestamp(state.userRetention.date);
  changeUserRetentionCount(queryValue);
};
const changeRetentionType = () => {
  let queryValue = "";
  if (state.userRetention.date) {
    queryValue = getUtcTimestamp(state.userRetention.date);
  }
  if (state.userRetention.type) {
    queryValue = state.userRetention.type;
  }
  changeUserRetentionCount(queryValue);
};

const changeUserRetentionCount = (queryValue) => {
  userRetentionCount({
    queryValue,
    type: state.userRetention.retentionType,
  }).then((response) => {
    const option = {
      legend: {
        bottom: 20,
      },
      xAxis: {
        type: "category",
      },
      yAxis: {
        type: "value",
      },
      tooltip: {
        trigger: "axis",
      },
      grid: {
        left: "50px",
        right: "20px",
      },
      series: [],
    };

    // 取第一条数据作为x轴
    if (response && Object.keys(response).length > 0) {
      const timeFormatStr =  '{y}/{m}/{d}'
      option.xAxis.data = Object.values(response)[0].map((x) => proxy.parseTime(x.timeRange, timeFormatStr));

      option.series = Object.entries(response).map(([key, vals]) => ({
        name: sourceTypeMap[key],
        data: vals.map((x) => x.number),
        type: "line",
        itemStyle: {
          color: "#ff9502",
        },
      }));
    }
    retentionChart.setOption(option);
  });
};

const getCount = () => {
  groupCount().then((res) => {
    state.countInfo = res;
    // feedback.value = response.feedback
    // drawCash.value = response.drawCash
  });
};

const getUtcTimestamp = (date) => {
  const localDate = new Date(date);
  return localDate.getTime() - localDate.getTimezoneOffset() * 60 * 1000;
};

const openTableDialog = (data) => {
  state.type = data
  state.dialogVisible = true;
};

onMounted(() => {
  getCount();
  registerChart = echarts.init(document.getElementById("register-chart"));
  activeChart = echarts.init(document.getElementById("active-chart"));
  retentionChart = echarts.init(document.getElementById("retention-chart"));

  changeRegisterChart("yesterday");
  changeUserActiveCount("yesterday");
  changeUserRetentionCount("sevenDay");
});
</script>

<style scoped lang="scss">
.home {
  background: #f4f4f4;
  :deep(el-radio-button__inner:hover) {
    color: #35c758;
  }
  .common-module {
    margin-bottom: 20px;
    background: #fff;
    border-radius: 4px;
    .common-module-header {
      display: flex;
      align-items: center;
      padding: 15px 20px;
      .hd-lf {
        flex: 1;
      }
      .hd-rh {
        flex: 2;
        display: flex;
        justify-content: flex-end;
      }
    }
    .title {
      font-size: 16px;
      font-weight: bold;
      color: #101010;
    }

    .card {
      overflow: hidden;
      margin: 0 8px 8px;
      border-radius: 4px;
      // border: 1px solid #e4e7ed;
    }

    @media screen and (max-width: 768px) {
      .card {
        width: 100% !important;
      }
    }
  }

  .statistics-container {
    background: #fff;
    .content {
      display: flex;
      padding: 0 20px;
      // margin: 0 -8px;
      // flex-wrap: wrap;
    }
    .card {
      flex: 1;
      margin: 20px 20px 20px 0;
      text-align: center;
      color: #aeaeb2;
      font-size: 14px;
      display: flex;
      align-items: center;
      border-right: 1px solid #ddd;
      &:last-child {
        margin-right: 0;
        border-right: 0;
      }
      strong {
        font-weight: 400;
        font-size: 14px;
        display: block;
        margin-top: 5px;
      }
    }
    .card-lf {
      // flex: 1;
      .icon-box {
        width: 45px;
        height: 45px;
        color: #fff;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        &.box1 {
          background: #007aff;
        }
        &.box2 {
          background: #fe3b30;
        }
        &.box3 {
          background: #ff9502;
        }
        &.box4 {
          background: #35c758;
        }
        &.box5 {
          background: #5856d7;
        }
      }
    }
    .card-rh {
      flex: 1;
      text-align: left;
      padding-left: 20px;
    }
    // .user-num {
    //   background: linear-gradient(to right, #6a84fe, #24ccfd);
    // }
    // .group-num {
    //   background: linear-gradient(to right, #fe988f, #ff738a);
    // }
    // .message-num {
    //   background: linear-gradient(to right, #04bade, #20eca5);
    // }
  }

  .daibang-container {
    .content {
      display: flex;
      margin: 0 -8px;
      flex-wrap: wrap;
    }
    .card {
      display: flex;
      .icon-wrap {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100px;
        font-size: 26px;
        background: #ea5b56;
        color: #fff;
        &.tixiang {
          background-color: #fdb93a;
        }
      }
      .text-wrap {
        width: 150px;
        padding: 15px;
        font-size: 12px;
        strong {
          display: block;
          margin-bottom: 6px;
          font-size: 18px;
        }
      }
    }
  }

  .register-chart-container {
    .radio-group-container {
      text-align: right;
      .el-radio-group {
        margin-right: 20px;
      }
    }
    #register-chart {
      height: 500px;
    }
    #active-chart {
      height: 500px;
    }
    #retention-chart {
      height: 500px;
    }
  }
}
</style>
