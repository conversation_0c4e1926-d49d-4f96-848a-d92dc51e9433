<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      v-show="showSearch"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item :label="$t('common.caption')" prop="title">
        <el-input
          v-model="queryParams.title"
          :placeholder="$t('common.pleaseEnter')"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item label="" prop="timeRange">
        <el-date-picker
          v-model="queryParams.timeRange"
          type="datetimerange"
          :range-separator="$t('common.to')"
          :start-placeholder="$t('common.startTime')"
          :end-placeholder="$t('common.endTime')"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >{{ $t('common.search') }}</el-button
        >
        <el-button icon="Refresh" @click="resetForm(queryFormRef)"
          >{{ $t('common.reset') }}</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="showAddBannerForm"
          v-hasPermi="['system:notice:add']"
          >{{ $t('common.add') }}</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:notice:remove']"
          >{{ $t('common.delete') }}</el-button
        >
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="getListLoading"
      :data="tableDataList"
      @selection-change="handleTableSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column
        :label="$t('common.caption')"
        align="center"
        prop="title"
        width="200"
        :show-overflow-tooltip="true"
      />

      <el-table-column label="图片" align="center" prop="link" width="200">
        <template #default="scope">
          <el-image
            style="width: 100px; height: 60px"
            :src="scope.row.img"
            fit="contain"
          />
        </template>
      </el-table-column>

      <el-table-column
        label="h5链接"
        align="center"
        prop="link"
        width="300"
        show-overflow-tooltip
      >
        <template #default="scope">
          <a :href="scope.row.link" target="_blank">{{ scope.row.link }}</a>
        </template>
      </el-table-column>

      <el-table-column label="排序" align="center" prop="order" width="100" />

      <el-table-column label="点击" align="center" prop="click" width="100" />

      <el-table-column
        label="开启状态"
        align="center"
        prop="status"
        width="100"
      >
        <template #default="scope">
          <el-switch :value="scope.row.status" size="large" />
        </template>
      </el-table-column>

      <el-table-column label="创建日期" align="center" prop="time" width="100">
        <template #default="scope">
          <span>{{ parseTime(scope.row.time, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('common.operate')"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button type="text" icon="Edit" @click="handleView(scope.row)"
            >查看</el-button
          >
          <el-button type="text" icon="Delete" @click="handleDelete(scope.row)"
            >{{ $t('common.delete') }}</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 查看或新增banner -->
    <el-dialog
      :title="addBannerDialogTitle"
      v-model="addBannerDialogVisible"
      width="700px"
      append-to-body
    >
      <el-form
        ref="addBannerFormRef"
        :model="addBannerParams"
        :rules="addBannerFormRules"
        label-width="100px"
        :disabled="addBannerDialogTitle === '详情'"
      >
        <el-row>
          <el-col :span="14">
            <el-form-item :label="$t('common.caption')" prop="title">
              <el-input
                v-model="addBannerParams.title"
                :placeholder="$t('common.pleaseEnter')"
              />
            </el-form-item>
            <el-form-item label="banner图片" prop="img">
              <el-upload
                class="image-uploader"
                action="https://jsonplaceholder.typicode.com/posts/"
                :show-file-list="false"
              >
                <el-image
                  class="image"
                  v-if="addBannerParams.link"
                  :src="addBannerParams.link"
                  :preview-src-list="[addBannerParams.link]"
                ></el-image>
                <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
              </el-upload>
            </el-form-item>
            <el-form-item label="链接" prop="link">
              <el-input
                type="textarea"
                v-model="addBannerParams.link"
                :placeholder="$t('common.pleaseEnter')"
                rows="3"
                autosize
              />
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="排序" prop="order">
              <el-input-number
                v-model="addBannerParams.order"
                :min="1"
                :max="100"
              />
            </el-form-item>
            <el-form-item :label="$t('common.state')" prop="order">
              <el-switch
                v-model="addBannerParams.status"
                active-text="开启"
                inactive-text="关闭"
              />
            </el-form-item>

            <el-form-item
              label="点击"
              prop="order"
              v-if="addBannerDialogTitle === '详情'"
            >
              {{ addBannerParams.click }}
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer" v-if="addBannerDialogTitle === '详情'">
          <el-button @click="addBannerDialogVisible = false">{{ $t('common.close') }}</el-button>
        </div>

        <div class="dialog-footer" v-else>
          <el-button type="primary" @click="addBanner">{{ $t('common.confirm') }}</el-button>
          <el-button @click="addBannerDialogVisible = false">{{ $t('common.cancel') }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="BannerManage">
import { reactive, ref, toRaw } from 'vue'
import i18n from "../../../i18n";

const showSearch = ref(true)
const queryParams = reactive({
  title: '',
  timeRange: '',
  pageNum: 1,
  pageSize: 10
})
const total = ref(999)
const getListLoading = ref(false)
const tableDataList = ref([
  {
    title: '标题1',
    img: 'https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fwww.hongsejipuzjy.com%2Fuploadfile%2F2020%2F0622%2F20200622104629716.jpg&refer=http%3A%2F%2Fwww.hongsejipuzjy.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1652425756&t=5dea5079f07901cbf8e6e7d867faba35',
    link: 'https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fwww.hongsejipuzjy.com%2Fuploadfile%2F2020%2F0622%2F20200622104629716.jpg&refer=http%3A%2F%2Fwww.hongsejipuzjy.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1652425756&t=5dea5079f07901cbf8e6e7d867faba35',
    order: 1,
    click: 5,
    status: false,
    time: 20220411
  }
])

const addBannerDialogVisible = ref(false)
const addBannerDialogTitle = ref('新增')
const addBannerParams = ref({
  title: '',
  img: '',
  link: '',
  order: 1,
  status: false
})

const addBannerFormRules = reactive({
  title: [
    { required: true, message: i18n.global.t('common.cannotBeEmpty'), trigger: 'blur' }

  ],
  img: [
    { required: true, message: i18n.global.t('common.cannotBeEmpty'), trigger: 'blur' }
  ]
})

const addBannerFormRef = ref()

// 查询
const handleQuery = () => {
  console.log('查询', toRaw(queryParams))
}

const queryFormRef = ref()
// 重置查询
const resetForm = (formEl) => {
  if (!formEl) return
  formEl.resetFields()
}

// 获取数据
const getList = () => {

}

const handleTableSelectionChange = (e) => {
  ids.value = selection.map(item => item.noticeId)
}

const showAddBannerForm = () => {
  addBannerDialogTitle.value = '新增'
  addBannerFormRef.value && resetForm(addBannerFormRef.value)
  addBannerDialogVisible.value = true
}

const addBanner = async () => {
  if (!addBannerFormRef.value) return
  addBannerFormRef.value.validate((valid, fields) => {
    if (valid) {
      console.log('submit!')
    } else {
      console.log('error submit!', fields)
    }
  })
}

const handleView = (data) => {
  addBannerDialogTitle.value = '详情'
  addBannerParams.value = JSON.parse(JSON.stringify(data))
  addBannerDialogVisible.value = true
}

</script>

<style lang="scss" scoped>
.image-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);

  &:hover {
    border-color: var(--el-color-primary);
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100px;
    height: 100px;
    text-align: center;
  }

  .image {
    width: 100px;
    height: 100px;
    display: block;
  }
}
</style>
