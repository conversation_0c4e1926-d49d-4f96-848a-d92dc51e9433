<template>
  <div>
    <!-- 添加或修改角色配置对话框 -->
    <el-form ref="roleRef" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="菜单权限">
        <el-checkbox
          v-model="menuExpand"
          @change="handleCheckedTreeExpand($event, 'menu')"
          >{{ $t('system.expandCollapse') }}</el-checkbox
        >
        <el-checkbox
          v-model="menuNodeAll"
          @change="handleCheckedTreeNodeAll($event, 'menu')"
          >{{ $t('system.selectAllDeselectAll') }}</el-checkbox
        >
        <el-checkbox
          v-model="form.menuCheckStrictly"
          @change="handleCheckedTreeConnect($event, 'menu')"
          >{{ $t('system.parentChildLinkage') }}</el-checkbox
        >
        <el-tree
          class="tree-border"
          :data="menuOptions"
          show-checkbox
          ref="menuRef"
          node-key="id"
          :check-strictly="!form.menuCheckStrictly"
          :empty-text="$t('system.loading')"
          :props="{ label: 'label', children: 'children' }"
        ></el-tree>
      </el-form-item>
    </el-form>

    <div class="footer-options">
      <el-button type="primary" @click="submitForm">{{ $t('common.confirm') }}</el-button>
      <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
    </div>
  </div>
</template>

<script setup name="permissionEdit">
import { getRole, updateRole } from '@/api/system/role'
import { roleMenuTreeselect } from '@/api/system/menu'
import { roleDeptTreeselect } from '@/api/system/dept'

const { proxy } = getCurrentInstance()
const { sys_normal_disable } = proxy.useDict('sys_normal_disable')

const props = defineProps({
  roleId: Number
})

const emit = defineEmits(['close'])

const menuOptions = ref([])
const menuExpand = ref(false)
const menuNodeAll = ref(false)
const deptExpand = ref(true)
const deptNodeAll = ref(false)
const deptOptions = ref([])
const menuRef = ref(null)
const deptRef = ref(null)

const data = reactive({
  form: {}
})

const { form, rules } = toRefs(data)

/** 重置新增的表单以及其他数据  */
function reset () {
  if (menuRef.value != undefined) {
    menuRef.value.setCheckedKeys([])
  }
  menuExpand.value = false
  menuNodeAll.value = false
  deptExpand.value = true
  deptNodeAll.value = false
  form.value = {
    roleId: undefined,
    roleName: undefined,
    roleKey: undefined,
    roleSort: 0,
    status: '0',
    menuIds: [],
    deptIds: [],
    menuCheckStrictly: true,
    deptCheckStrictly: true,
    remark: undefined
  }
  proxy.resetForm('roleRef')
}

/** 修改角色 */
function handleUpdate () {
  reset()
  const roleId = props.roleId
  const roleMenu = getRoleMenuTreeselect(roleId)
  getRole(roleId).then(response => {
    form.value = response.data
    form.value.roleSort = Number(form.value.roleSort)
    open.value = true
    nextTick(() => {
      roleMenu.then((res) => {
        const checkedKeys = res.checkedKeys
        checkedKeys.forEach((v) => {
          nextTick(() => {
            menuRef.value.setChecked(v, true, false)
          })
        })
      })
    })
  })
}

handleUpdate()

/** 根据角色ID查询菜单树结构 */
function getRoleMenuTreeselect (roleId) {
  return roleMenuTreeselect(roleId).then(response => {
    menuOptions.value = response.menus
    return response
  })
}
/** 根据角色ID查询部门树结构 */
function getRoleDeptTreeselect (roleId) {
  return roleDeptTreeselect(roleId).then(response => {
    deptOptions.value = response.depts
    return response
  })
}
/** 树权限（展开/折叠） */
function handleCheckedTreeExpand (value, type) {
  if (type == 'menu') {
    const treeList = menuOptions.value
    for (let i = 0; i < treeList.length; i++) {
      menuRef.value.store.nodesMap[treeList[i].id].expanded = value
    }
  } else if (type == 'dept') {
    const treeList = deptOptions.value
    for (let i = 0; i < treeList.length; i++) {
      deptRef.value.store.nodesMap[treeList[i].id].expanded = value
    }
  }
}
/** 树权限（全选/全不选） */
function handleCheckedTreeNodeAll (value, type) {
  if (type == 'menu') {
    menuRef.value.setCheckedNodes(value ? menuOptions.value : [])
  } else if (type == 'dept') {
    deptRef.value.setCheckedNodes(value ? deptOptions.value : [])
  }
}
/** 树权限（父子联动） */
function handleCheckedTreeConnect (value, type) {
  if (type == 'menu') {
    form.value.menuCheckStrictly = !!value
  } else if (type == 'dept') {
    form.value.deptCheckStrictly = !!value
  }
}
/** 所有菜单节点数据 */
function getMenuAllCheckedKeys () {
  // 目前被选中的菜单节点
  const checkedKeys = menuRef.value.getCheckedKeys()
  // 半选中的菜单节点
  const halfCheckedKeys = menuRef.value.getHalfCheckedKeys()
  checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys)
  return checkedKeys
}
/** 提交按钮 */
function submitForm () {
  proxy.$refs.roleRef.validate(valid => {
    if (valid) {
      form.value.menuIds = getMenuAllCheckedKeys()
      updateRole(form.value).then(response => {
        emit('close')
        proxy.$modal.msgSuccess(i18n.global.t('common.success'))
      })
    }
  })
}
/** 取消按钮 */
function cancel () {
  emit('close')
}

</script>

<style lang="postcss" scoped>
.footer-options {
  margin-top: 50px;
  text-align: right;
}
</style>
