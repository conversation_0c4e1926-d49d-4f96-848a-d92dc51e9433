<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryFormRef" v-show="state.showSearch" :inline="true">
      <el-form-item label="">
        <el-select style="width: 100px" v-model="queryParams.languageType" class="m-2" :placeholder="$t('common.select')">
          <el-option :label="$t('user.simplifiedChinese')" :value="1"/>
          <el-option :label="$t('user.traditionalChinese')" :value="2"/>
          <el-option :label="$t('user.english')" :value="3"/>
          <el-option :label="$t('user.cambodian')" :value="4"/>
        </el-select>
        <el-input v-model.trim="queryParams.title" :placeholder="$t('common.pleaseEnter')" clearable style="width: 240px"
                  @keyup.enter="handleQuery"/>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">{{ $t('common.search') }}</el-button>
        <el-button icon="Refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['system:role:add']">{{ $t('common.add') }}
        </el-button>
      </el-col>
    </el-row>

    <!-- 表格数据 -->
    <el-table v-loading="state.getListLoading" :data="state.tableData">
      <el-table-column :label="$t('common.caption') +'(' + $t('user.simplifiedChinese') + ')'" prop="titleSimplifiedChinese"/>
      <el-table-column :label="$t('common.caption') +'(' + $t('user.traditionalChinese') + ')'" prop="titleTraditionalChinese"/>
      <el-table-column :label="$t('common.caption') +'(' + $t('user.cambodian') + ')'" align="center" prop="titleCambodian"/>
      <el-table-column :label="$t('common.caption') +'(' + $t('user.english') + ')'" align="center" prop="titleEnglish"/>
      <el-table-column :label="$t('user.addTime')" align="center" prop="createTime">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, "{y}-{m}-{d} {h}:{i}:{s}") }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.operate')" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button type="text" @click="handleView(scope.row)">{{ $t('common.details') }}</el-button>
          <el-button type="text" @click="edit(scope.row)">{{ $t('common.edit') }}</el-button>
          <el-button type="text" @click="deleteQuestion(scope.row)">{{ $t('common.delete') }}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="state.total > 0" :total="state.total" v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize" @pagination="getList"/>

    <!-- 添加或修改对话框 -->
    <el-dialog :title="state.dialogTitle" v-model="state.dialogVisible" width="1000px" append-to-body @opened="createEditor">
      <el-form ref="formRef" :model="formParams" label-width="100px" v-if="state.dialogVisible">
        <el-form-item :label="$t('common.caption') +'(' + $t('user.simplifiedChinese') + ')'" prop='titleSimplifiedChinese'>
          <el-input v-model="formParams.titleSimplifiedChinese" :placeholder="$t('common.pleaseEnter')" maxlength="200"/>
        </el-form-item>
        <el-form-item :label="$t('common.caption') +'(' + $t('user.traditionalChinese') + ')'" prop='titleTraditionalChinese'>
          <el-input v-model="formParams.titleTraditionalChinese" :placeholder="$t('common.pleaseEnter')" maxlength="200"/>
        </el-form-item>
        <el-form-item :label="$t('common.caption') +'(' + $t('user.cambodian') + ')'" prop='titleCambodian'>
          <el-input v-model="formParams.titleCambodian" :placeholder="$t('common.pleaseEnter')" maxlength="200"/>
        </el-form-item>
        <el-form-item :label="$t('common.caption') +'(' + $t('user.english') + ')'" prop='titleEnglish'>
          <el-input v-model="formParams.titleEnglish" :placeholder="$t('common.pleaseEnter')" maxlength="200"/>
        </el-form-item>
        <el-form-item :label="$t('user.description') +'(' + $t('user.simplifiedChinese') + ')'" prop="contentSimplifiedChinese">
          <div id="editor1" style="width: 100%; height: 300px"></div>
        </el-form-item>
        <el-form-item :label="$t('user.description') +'(' + $t('user.traditionalChinese') + ')'" prop="contentTraditionalChinese">
          <div id="editor2" style="width: 100%; height: 300px"></div>
        </el-form-item>
        <el-form-item :label="$t('user.description') +'(' + $t('user.cambodian') + ')'" prop="contentCambodian">
          <div id="editor3" style="width: 100%; height: 300px"></div>
        </el-form-item>
        <el-form-item :label="$t('user.description') +'(' + $t('user.english') + ')'" prop="contentEnglish">
          <div id="editor4" style="width: 100%; height: 300px"></div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">{{ $t('common.confirm') }}</el-button>
          <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog :title="state.dialogTitle" v-model="state.detailDialog" width="1000px" append-to-body>
      <el-form ref="formRef" :model="formParams" label-width="100px">
        <el-form-item :label="$t('common.caption') +'(' + $t('user.simplifiedChinese') + ')'" prop='titleSimplifiedChinese'>
          <div id="titleSimplifiedChinese" v-html="formParams.titleSimplifiedChinese" style="width: 100%; border: 1px solid #dcdfe6;padding: 8px"></div>
        </el-form-item>
        <el-form-item :label="$t('common.caption') +'(' + $t('user.traditionalChinese') + ')'" prop='titleTraditionalChinese'>
          <div id="titleTraditionalChinese" v-html="formParams.titleTraditionalChinese" style="width: 100%; border: 1px solid #dcdfe6;padding: 8px"></div>
        </el-form-item>
        <el-form-item :label="$t('common.caption') +'(' + $t('user.cambodian') + ')'" prop='titleCambodian'>
          <div id="titleCambodian" v-html="formParams.titleCambodian" style="width: 100%; border: 1px solid #dcdfe6;padding: 8px"></div>
        </el-form-item>
        <el-form-item :label="$t('common.caption') +'(' + $t('user.english') + ')'" prop='titleEnglish'>
          <div id="titleEnglish" v-html="formParams.titleEnglish" style="width: 100%; border: 1px solid #dcdfe6;padding: 8px"></div>
        </el-form-item>
        <el-form-item :label="$t('user.description') +'(' + $t('user.simplifiedChinese') + ')'" prop="contentSimplifiedChinese">
          <div id="contentSimplifiedChinese" v-html="formParams.contentSimplifiedChinese" style="width: 100%; border: 1px solid #dcdfe6;padding: 8px"></div>
        </el-form-item>
        <el-form-item :label="$t('user.description') +'(' + $t('user.traditionalChinese') + ')'" prop="contentTraditionalChinese">
          <div id="contentTraditionalChinese" v-html="formParams.contentTraditionalChinese" style="width: 100%; border: 1px solid #dcdfe6;padding: 8px"></div>
        </el-form-item>
        <el-form-item :label="$t('user.description') +'(' + $t('user.cambodian') + ')'" prop="contentCambodian">
          <div id="contentCambodian" v-html="formParams.contentCambodian" style="width: 100%; border: 1px solid #dcdfe6;padding: 8px"></div>
        </el-form-item>
        <el-form-item :label="$t('user.description') +'(' + $t('user.english') + ')'" prop="contentEnglish">
          <div id="contentEnglish" v-html="formParams.contentEnglish" style="width: 100%; border: 1px solid #dcdfe6;padding: 8px"></div>
        </el-form-item>
      </el-form>
    </el-dialog>

  </div>
</template>

<script setup name="FundWithdrawalReview">
import { reactive, ref } from 'vue'
import {
  pageCommonProblem,
  saveCommonProblem,
  deleteCommonProblem
} from '@/api/commonProblem/commonProblem'
import { ElMessage, ElMessageBox } from 'element-plus'
import Quill from 'quill'
import 'quill/dist/quill.snow.css'
import i18n from '../../i18n'

// const { proxy } = getCurrentInstance()

const queryFormRef = ref()
// const formRef = ref()

let cEditor1
let cEditor2
let cEditor3
let cEditor4

const createEditor = () => {
  const toolbarOptions = [
    ['bold', 'italic', 'underline', 'strike'], // toggled buttons
    ['blockquote', 'code-block'],
    [{ header: 1 }, { header: 2 }], // custom button values
    [{ list: 'ordered' }, { list: 'bullet' }],
    [{ script: 'sub' }, { script: 'super' }], // superscript/subscript
    [{ indent: '-1' }, { indent: '+1' }], // outdent/indent
    [{ direction: 'rtl' }], // text direction
    [{ size: ['small', false, 'large', 'huge'] }], // custom dropdown
    [{ header: [1, 2, 3, 4, 5, 6, false] }],
    [{ color: [] }, { background: [] }], // dropdown with defaults from theme
    [{ align: [] }],
    ['clean'] // remove formatting button
  ]
  cEditor1 = new Quill('#editor1', {
    theme: 'snow',
    modules: {
      toolbar: toolbarOptions
    },
    placeholder: i18n.global.t('common.pleaseEnter')
  })

  cEditor2 = new Quill('#editor2', {
    theme: 'snow',
    modules: {
      toolbar: toolbarOptions
    },
    placeholder: i18n.global.t('common.pleaseEnter')
  })

  cEditor3 = new Quill('#editor3', {
    theme: 'snow',
    modules: {
      toolbar: toolbarOptions
    },
    placeholder: i18n.global.t('common.pleaseEnter')
  })

  cEditor4 = new Quill('#editor4', {
    theme: 'snow',
    modules: {
      toolbar: toolbarOptions
    },
    placeholder: i18n.global.t('common.pleaseEnter')
  })
}

// 搜索参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  languageType: 1,
  title: null
})

const state = reactive({
  showSearch: true,
  getListLoading: true,
  tableData: [],
  total: 0,
  selectedIds: [],
  dialogVisible: false,
  dialogTitle: '',
  detailDialog: false
})

// 获取列表数据
function getList () {
  state.getListLoading = true
  const params = queryParams.value
  for (const key in params) {
    if (!params[key]) params[key] = null
  }
  pageCommonProblem(queryParams.value)
    .then((response) => {
      state.tableData = response.data.records
      state.total = response.data.total
    })
    .finally(() => {
      state.getListLoading = false
    })
}

// 查询数据
function handleQuery () {
  queryParams.value.pageNum = 1
  getList()
}

// 重置查询
function resetQuery () {
  if (queryFormRef.value) {
    queryFormRef.value.resetFields()
  }
  queryParams.value.languageType = 1
  queryParams.value.title = null
  handleQuery()
}

// 多选框选中数据
// function handleSelectionChange (selection) {
//   state.selectedIds = selection.map((item) => item.roleId)
// }

// 删除
const deleteQuestion = (data) => {
  ElMessageBox.confirm(i18n.global.t('user.sureToDelete'), {
    confirmButtonText: i18n.global.t('common.confirm'),
    cancelButtonText: i18n.global.t('common.cancel'),
    type: 'warning'
  })
    .then(() => {
      deleteCommonProblem(data.id).then((response) => {
        if (response.code === 200) {
          ElMessage.success(i18n.global.t('common.success'))
          getList()
        } else {
          ElMessage.error(i18n.global.t('common.failed'))
        }
      })
    })
    .catch(() => {})
}

// 查看详情
function handleView (data) {
  state.dialogTitle = i18n.global.t('common.details')
  formParams.value = data
  state.detailDialog = true
  setTimeout(() => {
    cEditor1.root.innerHTML = formParams.value.contentSimplifiedChinese
    cEditor2.root.innerHTML = formParams.value.contentTraditionalChinese
    cEditor3.root.innerHTML = formParams.value.contentCambodian
    cEditor4.root.innerHTML = formParams.value.contentEnglish
  }, 500)
}

// 编辑
function edit (data) {
  state.dialogTitle = i18n.global.t('common.edit')
  formParams.value = { ...data }
  state.dialogVisible = true
  setTimeout(() => {
    cEditor1.root.innerHTML = formParams.value.contentSimplifiedChinese
    cEditor2.root.innerHTML = formParams.value.contentTraditionalChinese
    cEditor3.root.innerHTML = formParams.value.contentCambodian
    cEditor4.root.innerHTML = formParams.value.contentEnglish
  }, 500)
}

// 新增相关
function handleAdd () {
  resetForm()
  state.dialogTitle = i18n.global.t('common.add')
  state.dialogVisible = true
}

// 新增问题参数
const formParams = ref({
  id: '',
  titleSimplifiedChinese: '',
  titleTraditionalChinese: '',
  titleEnglish: '',
  titleCambodian: '',
  contentSimplifiedChinese: '',
  contentTraditionalChinese: '',
  contentCambodian: '',
  contentEnglish: ''

})

// 取消按钮
function cancel () {
  state.dialogVisible = false
}

// 提交
async function submitForm () {
  formParams.value.contentSimplifiedChinese = cEditor1.root.innerHTML
  formParams.value.contentTraditionalChinese = cEditor2.root.innerHTML
  formParams.value.contentCambodian = cEditor3.root.innerHTML
  formParams.value.contentEnglish = cEditor4.root.innerHTML
  if (formParams.value.contentSimplifiedChinese.length > 2000) {
    alert(i18n.global.t('common.charactersExceeds') + '2000')
    return
  }
  if (formParams.value.contentTraditionalChinese.length > 2000) {
    alert(i18n.global.t('common.charactersExceeds') + '2000')
    return
  }
  if (formParams.value.contentCambodian.length > 2000) {
    alert(i18n.global.t('common.charactersExceeds') + '2000')
    return
  }
  if (formParams.value.contentEnglish.length > 2000) {
    alert(i18n.global.t('common.charactersExceeds') + '2000')
    return
  }
  saveCommonProblem(formParams.value).then((response) => {
    if (response.code === 200) {
      if (formParams.value.id === '') {
        ElMessage.success(i18n.global.t('common.success'))
      } else {
        ElMessage.success(i18n.global.t('common.success'))
      }
      state.dialogVisible = false
      getList()
    }
  })
}

function resetForm () {
  formParams.value = {
    id: '',
    titleSimplifiedChinese: '',
    titleTraditionalChinese: '',
    titleCambodian: '',
    titleEnglish: '',
    contentSimplifiedChinese: '',
    contentTraditionalChinese: '',
    contentCambodian: '',
    contentEnglish: ''
  }
}

getList()
</script>
