<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryFormRef"
      v-show="state.showSearch"
      :inline="true"
    >
      <el-form-item :label="$t('recommend.status')">
        <el-select
          style="width: 150px"
          v-model="queryParams.status"
          class="m-2"
          :placeholder="$t('recommend.all')"
        >
          <el-option :label="$t('recommend.all')" :value="null" />
          <el-option :label="$t('recommend.recommend')" value="RECOMMEND" />
          <el-option :label="$t('recommend.audit')" value="AUDIT" />
          <el-option :label="$t('recommend.deprecated')" value="DEPRECATED" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('recommend.channelName')">
        <el-select
          v-model="queryParams.conversationId"
          clearable
          filterable
          remote
          reserve-keyword
          :placeholder="$t('common.pleaseEnter')"
          :remote-method="getConversation"
          :loading="state.loading"
        >
          <el-option
            v-for="item in state.options"
            :key="item.id"
            :label="
              item.chatType == 2 || item.chatType == 7 ? item.name : item.id
            "
            :value="item.id"
          >
            <span style="float: left">{{ item.id }}</span>
            <span
              style="float: right; color: #8492a6; font-size: 13px"
              v-if="item.chatType == 2 || item.chatType == 7"
            >
              {{ item.name }}</span
            >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('recommend.createTime')">
        <el-date-picker
          v-model="queryParams.sendDate"
          value-format="YYYY-MM-DD"
          type="daterange"
          :start-placeholder="$t('recommend.startTime')"
          :end-placeholder="$t('recommend.endTime')"
          :default-value="[new Date(), new Date()]"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">{{
          $t("common.search")
        }}</el-button>
        <el-button icon="Refresh" @click="resetQuery">{{
          $t("common.reset")
        }}</el-button>
      </el-form-item>
    </el-form>

    <!-- 表格数据 -->
    <el-table v-loading="state.getListLoading" :data="state.tableData">
      <el-table-column
        :label="$t('recommend.sender')"
        align="center"
        prop="name"
      />
      <el-table-column
        :label="$t('recommend.channelName')"
        align="center"
        prop="channelName"
      />
      <el-table-column :label="$t('recommend.content')" prop="content" align="center" show-overflow-tooltip>
        <template #default="scope">
          <span>{{ getMessageContent(scope.row.msgType, scope.row.content?.text) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('recommend.createTime')" align="center">
        <template #default="scope">
          {{parseTime(scope.row.createTime)}}
        </template>
      </el-table-column>
      <el-table-column :label="$t('recommend.recommendTime')" align="center">
        <template #default="scope">
          {{scope.row.recommendTime ? parseTime(scope.row.recommendTime) : '/'}}
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('recommend.status')"
        prop="status"
        align="center"
        width="120"
      >
        <template #default="scope">
          <span style="color: red" v-if="scope.row.status === 'AUDIT'">{{
            $t("recommend.audit")
          }}</span>
          <span style="color: green" v-if="scope.row.status === 'RECOMMEND'">{{
            $t("recommend.recommend")
          }}</span>
          <span v-if="scope.row.status === 'DEPRECATED'">{{
            $t("recommend.deprecated")
          }}</span>
          <span v-if="scope.row.status === 'DELETE'">{{
            $t("msg.deleted")
          }}</span>
        </template>
      </el-table-column>

      <el-table-column
        :label="$t('common.operate')"
        align="center"
        width="150"
        fixed="right"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            type="text"
            @click="showDialog('AUDIT', scope.row)"
            v-if="scope.row.status === 'AUDIT'"
            >{{ $t("recommend.manualReview") }}</el-button
          >
          <el-button
            type="text"
            @click="showDialog('DETAIL', scope.row)"
            v-if="scope.row.status === 'RECOMMEND'"
            >{{ $t("recommend.detail") }}</el-button
          >
          <el-button
            type="text"
            @click="showDialog('DETAIL', scope.row)"
            v-if="scope.row.status === 'DEPRECATED'"
            >{{ $t("recommend.detail") }}</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="state.total > 0"
      :total="state.total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog
      v-if="state.moreDialogVisible"
      v-model="state.moreDialogVisible"
      :title="dialogTitle"
      :width="state.moreDialogWidth"
      append-to-body
      custom-class="flex-body"
    >
      <div v-loading="detailLoading" class="main-detail rich-text">
        <div v-if="state.currentDetail.msgType === -8">
          <img v-if="state.currentDetail.content?.files?.length" :src="state.currentDetail.content?.files[0].url" alt="" class="rtf-cover" />
          <div v-if="state.currentDetail.content.text" class="rtf-title">{{ state.currentDetail.content.text }}</div>
          <div v-html="detailContent"></div>
        </div>
        <template v-else>
          <div v-if="state.currentDetail.msgType === 6010">
            {{ $t("recommend.redEnvelopeBlessing") }}:{{ detailContent.envelopeInfoDto.blessingWords }}
          </div>
          <div v-if="state.currentDetail.content?.files?.length" class="file-container">
            <template v-for="(item, index) in state.currentDetail.content.files">
              <img v-if="state.currentDetail.msgType === -2" :key="item.url + index" :src="item.url" alt="" class="file-item" />
              <audio v-else-if="state.currentDetail.msgType === -3" :key="item.url + index" :src="item.url" controls class="file-item" />
              <video v-else-if="state.currentDetail.msgType === -4" :key="item.url + index" :src="item.url" controls class="file-item" />
              <a v-else :key="item.url + index" :href="item.sourceUrl || item.url" target="_blank" class="file-item link">{{ item.sourceUrl || item.url }}</a>
            </template>
          </div>
          <div v-if="state.currentDetail.content.text">
            {{ state.currentDetail.content.text }}
          </div>
        </template>
      </div>
      <template v-if="state.currentDetail.status === 'AUDIT'" #footer>
        <div class="dialog-footer">
          <el-button type="danger" @click="handleAudit(false)">{{ $t("recommend.reject") }}</el-button>
          <el-button type="primary" @click="handleAudit(true)">{{ $t("recommend.recommendAudit") }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Group/index">
import { reactive, ref, computed } from 'vue'
import {
  pageDiscoverContent,
  getRtfContent,
  discoverContentAudit,
  getRedEnvelopInfo
} from '@/api/contentAudit'
import { getConversationNameAndId } from '@/api/message/message.js'
import { ElMessage, ElMessageBox } from 'element-plus'
import i18n from '../../i18n'

// const { proxy } = getCurrentInstance()

const queryFormRef = ref()

const queryKeyWordParams = ref({
  keyword: null
})
function getConversation (query) {
  if (query !== '') {
    state.loading = true
    setTimeout(() => {
      queryKeyWordParams.value.keyword = query
      getConversationNameAndId(queryKeyWordParams.value)
        .then((result) => {
          state.options = result.data
          console.log(result.data)
        })
        .finally(() => {
          state.loading = false
        })
    }, 200)
  } else {
    state.options = []
  }
}
// 搜索参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  status: null,
  conversationId: null,
  sendDate: null,
  startTime: null,
  endTime: null
})

const state = reactive({
  showSearch: true,
  getListLoading: false,
  tableData: [],
  total: 0,
  selectedIds: [],
  dialogVisible: false,
  dialogTitle: '',
  moreDialogTitle: '',
  moreDialogVisible: false,
  moreDialogType: '',
  moreDialogWidth: '500px',
  currentDetail: {}
})

// 获取列表数据
function getList () {
  state.getListLoading = true
  if (queryParams.value.sendDate) {
    queryParams.value.beginTime = +new Date(
      queryParams.value.sendDate[0] + ' 00:00:00'
    )
    queryParams.value.endTime = +new Date(
      queryParams.value.sendDate[1] + ' 23:59:59'
    )
  } else {
    queryParams.value.beginTime = null
    queryParams.value.endTime = null
  }
  pageDiscoverContent(queryParams.value)
    .then((response) => {
      state.tableData = response.data.records.map((item) => {
        item.content = JSON.parse(item.content)
        return item
      })
      state.total = response.data.total
    })
    .finally(() => {
      state.getListLoading = false
    })
}
// 查询数据
function handleQuery () {
  queryParams.value.pageNum = 1
  getList()
}
// 重置查询
function resetQuery () {
  if (queryFormRef.value) {
    queryFormRef.value.resetFields()
  }
  queryParams.value.conversationId = null
  queryParams.value.status = null
  queryParams.value.sendDate = null
  handleQuery()
}
function getMessageContent (msgType, text) {
  if (msgType === -1) {
    return text
  } else if (msgType === -2) {
    return `[${i18n.global.t('common.image')}]`
  } else if (msgType === -3) {
    return `[${i18n.global.t('common.voice')}]`
  } else if (msgType === -4) {
    return `[${i18n.global.t('common.video')}]`
  } else if (msgType === -5) {
    return `[${i18n.global.t('common.file')}]`
  } else if (msgType === -6) {
    return `[${i18n.global.t('msg.linkPicture')}]`
  } else if (msgType === -7) {
    return `[${i18n.global.t('msg.linkVideo')}]`
  } else if (msgType === -8) {
    return `[${i18n.global.t('msg.rtf')}]`
  } else if (msgType === 6010) {
    return `[${i18n.global.t('common.redEnvelope')}]`
  }
}
const dialogTitle = computed(() => {
  let title = ''
  switch (state.moreDialogType) {
    case 'DETAIL':
      title = i18n.global.t('recommend.detail')
      break
    case 'AUDIT':
      title = i18n.global.t('marking.audit')
      break
    default:
      title = ''
  }
  return title
})
const detailLoading = ref(false)
const detailContent = ref('')
async function showDialog (type, item) {
  state.moreDialogType = type
  state.currentDetail = { ...item }
  state.moreDialogWidth = '900px'
  state.moreDialogVisible = true
  // 如果为富文本，则需要请求接口获取详情
  if (state.currentDetail.msgType === -8) {
    detailLoading.value = true
    try {
      const res = await getRtfContent({
        rtfId: state.currentDetail.content.attrs.rtfId
      })
      detailContent.value = res.data
      detailLoading.value = false
    } catch (error) {
      detailLoading.value = false
    }
  }else if (state.currentDetail.msgType === 6010) {
    detailLoading.value = true
    try {
      const res = await getRedEnvelopInfo({
        redEnvelopId: state.currentDetail.content.attrs.redEnvelopId
      })
      detailContent.value = res.data
      detailLoading.value = false
    } catch (error) {
      detailLoading.value = false
    }
  }
}



const handleAudit = (isRecommend) => {
  ElMessageBox.confirm(
    isRecommend ? i18n.global.t('recommend.confirmPush') : i18n.global.t('recommend.confirmReject'),
    i18n.global.t('common.tips'),
    {
      confirmButtonText: i18n.global.t('common.confirm'),
      cancelButtonText: i18n.global.t('common.cancel'),
      type: 'warning'
    }
  ).then(() => {
    discoverContentAudit({
      id: state.currentDetail.id,
      status: isRecommend ? 'RECOMMEND' : 'DEPRECATED'
    }).then((res) => {
      state.moreDialogVisible = false
      ElMessage({
        type: 'success',
        message: i18n.global.t('common.success')
      })
      getList()
    }).catch((error) => {
      // 接口异常时也关闭弹窗并刷新列表
      state.moreDialogVisible = false
      getList()
    })
  })
}

getList()
</script>
<style lang="scss" scoped>
.rtf-cover {
  margin-bottom: 16px;
  max-width: 100%;
  height: auto;
}
.rtf-title {
  color: #000;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 16px;
}
.file-container {
  margin-bottom: 10px;
  .link {
    color: blue;
    cursor: pointer;
  }
  .file-item {
    max-width: 100%;
    &:not(:last-child) {
      margin-bottom: 5px;
    }
  }
}
</style>
