<template>
  <div class="app-container">
<el-form :model="queryParams" ref="queryFormRef" v-show="state.showSearch" :inline="true">
      <el-form-item label="">

        <el-input v-model="queryParams.ids" :placeholder="$t('common.pleaseEnter')" clearable style="width: 240px"
                  @keyup.enter="handleQuery"/>
      </el-form-item>
      <el-form-item>
        <el-button type="primary"  @click="handleQuery">清除token</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup name="FundWithdrawalReview">
import {reactive, ref} from 'vue'
import {
  cleanToken
} from '@/api/token/cleanToken'
import {ElMessage, ElMessageBox} from 'element-plus'
import Quill from "quill"
import "quill/dist/quill.snow.css"
import i18n from "../../i18n";

// const { proxy } = getCurrentInstance()

const queryFormRef = ref()
// const formRef = ref()

let cEditor1;
let cEditor2;
let cEditor3;
let cEditor4;


const createEditor = () => {
  const toolbarOptions = [
    ["bold", "italic", "underline", "strike"], // toggled buttons
    ["blockquote", "code-block"],
    [{ header: 1 }, { header: 2 }], // custom button values
    [{ list: "ordered" }, { list: "bullet" }],
    [{ script: "sub" }, { script: "super" }], // superscript/subscript
    [{ indent: "-1" }, { indent: "+1" }], // outdent/indent
    [{ direction: "rtl" }], // text direction
    [{ size: ["small", false, "large", "huge"] }], // custom dropdown
    [{ header: [1, 2, 3, 4, 5, 6, false] }],
    [{ color: [] }, { background: [] }], // dropdown with defaults from theme
    [{ align: [] }],
    ["clean"], // remove formatting button
  ];
  cEditor1 = new Quill("#editor1", {
    theme: "snow",
    modules: {
      toolbar: toolbarOptions,
    },
    placeholder: i18n.global.t('common.pleaseEnter'),
  });

  cEditor2 = new Quill("#editor2", {
    theme: "snow",
    modules: {
      toolbar: toolbarOptions,
    },
    placeholder: i18n.global.t('common.pleaseEnter'),
  });

  cEditor3 = new Quill("#editor3", {
    theme: "snow",
    modules: {
      toolbar: toolbarOptions,
    },
    placeholder: i18n.global.t('common.pleaseEnter'),
  });

  cEditor4 = new Quill("#editor4", {
    theme: "snow",
    modules: {
      toolbar: toolbarOptions,
    },
    placeholder: i18n.global.t('common.pleaseEnter'),
  });
};

// 搜索参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  languageType: 1,
  title: null,
  ids:''
})

const state = reactive({
  showSearch: true,
  getListLoading: true,
  tableData: [],
  total: 0,
  selectedIds: [],
  dialogVisible: false,
  dialogTitle: ''
})


// 获取列表数据
function getList() {
  state.getListLoading = true
  const params = queryParams.value
  for (const key in params) {
    if (!params[key]) params[key] = null
  }
  pageCommonProblem(queryParams.value)
    .then((response) => {
      state.tableData = response.data.records
      state.total = response.data.total
    })
    .finally(() => {
      state.getListLoading = false
    })
}

// 查询数据
function handleQuery() {
  console.log(queryParams.value.ids)
  cleanToken(queryParams.value.ids).then((response) => {
      if (response.code === 200) {

          ElMessage.success(i18n.global.t('common.success'))

      }
    })
}

// 重置查询
function resetQuery() {
  if (queryFormRef.value) {
    queryFormRef.value.resetFields()
  }
  queryParams.value.languageType = 1
  queryParams.value.title = null
  handleQuery()
}

// 多选框选中数据
// function handleSelectionChange (selection) {
//   state.selectedIds = selection.map((item) => item.roleId)
// }

// 删除
const deleteQuestion = (data) => {
  ElMessageBox.confirm(i18n.global.t('common.confirmDelete'), {
    confirmButtonText: i18n.global.t('common.confirm'),
    cancelButtonText: i18n.global.t('common.cancel'),
    type: 'warning'
  })
    .then(() => {
      deleteCommonProblem(data.id).then((response) => {
        if (response.code === 200) {
          ElMessage.success(i18n.global.t('common.success'))
          getList()
        } else {
          ElMessage.error(i18n.global.t('common.failed'))
        }
      })
    })
    .catch(() => {})
}

const memberDialogVisivle = ref(false)
const selectedId = ref('')

//查看详情
function handleView(data) {
  state.dialogTitle = '详情'
  formParams.value = data
  state.detailDialog = true
  setTimeout(() => {
    cEditor1.root.innerHTML = formParams.value.contentSimplifiedChinese;
    cEditor2.root.innerHTML = formParams.value.contentTraditionalChinese;
    cEditor3.root.innerHTML = formParams.value.contentCambodian;
    cEditor4.root.innerHTML = formParams.value.contentEnglish;
  }, 500);
}

//编辑
function edit(data) {
  state.dialogTitle = '编辑'
  formParams.value = {...data}
  state.dialogVisible = true
  setTimeout(() => {
    cEditor1.root.innerHTML = formParams.value.contentSimplifiedChinese;
    cEditor2.root.innerHTML = formParams.value.contentTraditionalChinese;
    cEditor3.root.innerHTML = formParams.value.contentCambodian;
    cEditor4.root.innerHTML = formParams.value.contentEnglish;
  }, 500);
}

//新增相关
function handleAdd() {
  resetForm()
  state.dialogTitle = '新增常见问题'
  state.dialogVisible = true
}

// 新增问题参数
const formParams = ref({
  id:'',
  titleSimplifiedChinese: '',
  titleTraditionalChinese: '',
  titleEnglish: '',
  titleCambodian: '',
  contentSimplifiedChinese:'',
  contentTraditionalChinese:'',
  contentCambodian:'',
  contentEnglish:''

})

// 取消按钮
function cancel() {
  debugger
  state.dialogVisible = false
}

// 提交
async function submitForm() {
  formParams.value.contentSimplifiedChinese = cEditor1.root.innerHTML
  formParams.value.contentTraditionalChinese = cEditor2.root.innerHTML
  formParams.value.contentCambodian = cEditor3.root.innerHTML
  formParams.value.contentEnglish = cEditor4.root.innerHTML
  if(formParams.value.contentSimplifiedChinese.length>2000){
    alert(i18n.global.t('common.charactersExceeds') + '2000')
    return
  }
  if(formParams.value.contentTraditionalChinese.length>2000){
    alert(i18n.global.t('common.charactersExceeds') + '2000')
    return
  }
  if(formParams.value.contentCambodian.length>2000){
    alert(i18n.global.t('common.charactersExceeds') + '2000')
    return
  }
  if(formParams.value.contentEnglish.length>2000){
    alert(i18n.global.t('common.charactersExceeds') + '2000')
    return
  }
  saveCommonProblem(formParams.value).then((response) => {
    if (response.code === 200) {
      if(formParams.value.id === ''){
        ElMessage.success(i18n.global.t('common.success'))
      }else {
        ElMessage.success(i18n.global.t('common.success'))
      }
      state.dialogVisible = false
      getList()
    }
  })
}

function resetForm () {
  formParams.value = {
    id:'',
    titleSimplifiedChinese: '',
    titleTraditionalChinese: '',
    titleCambodian: '',
    titleEnglish: '',
    contentSimplifiedChinese: '',
    contentTraditionalChinese: '',
    contentCambodian: '',
    contentEnglish: ''
  }
}
</script>
