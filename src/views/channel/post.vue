<template>
  <div>
    <el-form :model="queryParams" ref="queryFormRef" :inline="true">
      <el-form-item :label="$t('channelManage.sendTime')" prop="time">
        <el-date-picker
          v-model="queryParams.time"
          value-format="YYYY-MM-DD"
          type="daterange"
          :start-placeholder="$t('common.startDate')"
          :end-placeholder="$t('common.endDate')"
          :default-value="[new Date(), new Date()]"
        />
      </el-form-item>
      <el-form-item :label="$t('channelManage.postType')" prop="msgType">
        <el-select
          style="width: 120px"
          clearable
          v-model="queryParams.msgType"
          :placeholder="$t('common.select')"
        >
          <el-option
            v-for="item in messageTypeEnum.slice(0, 7)"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >{{ $t('common.search') }}</el-button
        >
        <el-button icon="Refresh" @click="resetQuery(queryFormRef)"
          >{{ $t('common.reset') }}</el-button
        >
      </el-form-item>
    </el-form>

    <!-- 表格数据 -->
    <el-table v-loading="state.getListLoading" :data="state.tableData">
      <el-table-column :label="$t('channelManage.postLink')" align="center" prop="link" />
      <el-table-column :label="$t('channelManage.postID')" align="center" prop="msgId" />
      <el-table-column :label="$t('channelManage.postType')" align="center" prop="msgType">
        <template #default="scope">
          <span>{{ msgTypeStr(scope.row) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('channelManage.postTime')" align="center" prop="releaseTime">
        <template #default="scope">
          <span>{{ parseTime(scope.row.releaseTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('channelManage.views')" align="center" prop="viewCount" />
      <el-table-column :label="$t('channelManage.likes')" align="center" prop="likeCount" />
      <el-table-column :label="$t('channelManage.comments')" align="center" prop="commentCount" />
      <el-table-column :label="$t('channelManage.collections')" align="center" prop="collectCount" />
      <el-table-column :label="$t('channelManage.shares')" align="center" prop="forwardCount" />
      <el-table-column :label="$t('channelManage.rewardCount')" align="center" prop="rewardCount">
        <template #default="scope">
          {{ scope.row.rewardCount }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('channelManage.rewardAmoutn')" align="center" prop="rewardAmount">
        <template #default="scope">
          {{ scope.row.rewardAmount }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('channelManage.fee')" align="center" prop="fee">
        <template #default="scope">
          {{ scope.row.fee }}
        </template>
      </el-table-column>
    <el-table-column :label="$t('channelManage.actualAmount')" align="center">
      <template #default="scope">
        {{ Number((scope.row.rewardAmount - scope.row.fee).toFixed(2))  }}
      </template>
    </el-table-column>
    </el-table>

    <pagination
      v-show="state.total > 0"
      :total="state.total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="FundWithdrawalReview">
import { reactive, ref, defineProps } from 'vue'
import { msgList } from '@/api/channel/index'
import { messageTypeEnum } from '@/utils/enum.js'

const props = defineProps(['groupId'])

const queryFormRef = ref()

function msgTypeStr (row) {
  let txt = row.msgType
  const list = messageTypeEnum.filter((i) => i.value === row.msgType)
  if (list?.length) {
    txt = list[0].label
  }
  return txt
}

// 搜索参数
const queryParams = ref({
  time: [],
  msgType: null,
  pageNum: 1,
  pageSize: 10
})

const state = reactive({
  getListLoading: true,
  tableData: [],
  total: 0
})

// 获取列表数据
function getList () {
  state.getListLoading = true
  const params = { ...queryParams.value }
  for (const key in params) {
    if (!params[key]) params[key] = null
  }
  if (params.time && params.time.length !== 0) {
    params.beginTime = +new Date(params.time[0])
    params.endTime = +new Date(params.time[1])
  }
  delete params.time
  msgList({ ...params, groupId: props.groupId })
    .then((response) => {
      state.tableData = response.data.records
      state.total = response.data.total
    })
    .finally(() => {
      state.getListLoading = false
    })
}
// 查询数据
function handleQuery () {
  queryParams.value.pageNum = 1
  getList()
}
// 重置查询
function resetQuery (formEl) {
  if (!formEl) return
  console.log(formEl)
  formEl.resetFields()
  getList()
}

getList()
</script>
<style scoped>
.img-msg {
  width: 60px;
  height: 60px;
  border-radius: 6px;
  background: #eee;
}
:deep(.pagination-container) {
  display: flex;
  justify-content: center;
}
:deep(.pagination-container .el-pagination) {
  position: static;
}
</style>
