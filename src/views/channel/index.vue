<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryFormRef" v-show="state.showSearch" :inline="true">
      <el-form-item :label="$t('channelManage.channelType')" prop="isOpen">
        <el-select style="width: 150px" v-model="queryParams.isOpen" clearable
          :placeholder="$t('channelManage.pleaseSelect')">
          <el-option :label="$t('channelManage.public')" :value="1" />
          <el-option :label="$t('channelManage.private')" :value="0" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('channelManage.channelName')" prop="channelName">
        <el-input v-model.trim="queryParams.channelName" :placeholder="$t('common.pleaseEnter')" clearable
          style="width: 240px" @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item :label="$t('channelManage.channelLink')" prop="channelLink">
        <el-input v-model.trim="queryParams.channelLink" :placeholder="$t('common.pleaseEnter')" clearable
          style="width: 240px" @keyup.enter="handleQuery" />
      </el-form-item>
      <!--      <el-form-item :label="$t('channelManage.ownerID')" prop="ownerId">-->
      <!--        <el-input-->
      <!--          v-model.trim="queryParams.ownerId"-->
      <!--          :placeholder="$t('common.pleaseEnter')"-->
      <!--          clearable-->
      <!--          style="width: 240px"-->
      <!--          @keyup.enter="handleQuery"-->
      <!--        />-->
      <!--      </el-form-item>-->
      <!--      <el-form-item :label="$t('channelManage.ownerName')" prop="ownerNickname">-->
      <!--        <el-input-->
      <!--          v-model.trim="queryParams.ownerNickname"-->
      <!--          :placeholder="$t('common.pleaseEnter')"-->
      <!--          clearable-->
      <!--          style="width: 240px"-->
      <!--          @keyup.enter="handleQuery"-->
      <!--        />-->
      <!--      </el-form-item>-->
      <el-form-item :label="$t('channelManage.channelState')" prop="status">
        <el-select style="width: 150px" v-model="queryParams.status" class="m-2" clearable
          :placeholder="$t('common.pleaseSelectState')">
          <el-option :label="$t('channelManage.normal')" :value="0" />
          <el-option :label="$t('common.freeze')" :value="1" />
          <el-option :label="$t('groupManage.deleted')" :value="2" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">{{
          $t("common.search")
          }}</el-button>
        <el-button icon="Refresh" @click="resetQuery(queryFormRef)">{{
          $t("common.reset")
          }}</el-button>
      </el-form-item>
    </el-form>

    <!-- 表格数据 -->
    <el-table v-loading="state.getListLoading" :data="state.tableData">
      <el-table-column :label="$t('channelManage.channelAvatar')" prop="headPortrait" align="center" width="100px">
        <template #default="scope">
          <el-image :src="scope.row.headPortrait" fit="contain" v-if="scope.row.headPortrait" class="img-msg" />
          <div v-else>{{ $t('channelManage.noAvatar') }}</div>
        </template>
      </el-table-column>
      <el-table-column :label="$t('channelManage.channelName')" align="center" prop="name" />
      <el-table-column :label="$t('channelManage.subscriber')" align="center" prop="memberCount">
        <template #default="scope">
          <el-button @click="showDialog(scope.row.id, 1)" type="text">{{
            scope.row.memberCount
            }}</el-button>
        </template>
      </el-table-column>
      <el-table-column :label="$t('channelManage.postsNumber')" align="center" prop="msgCount">
        <template #default="scope">
          <el-button @click="showDialog(scope.row.id, 2)" type="text">{{
            scope.row.msgCount
            }}</el-button>
        </template>
      </el-table-column>
      <el-table-column :label="$t('channelManage.rewardCount')" align="center" prop="rewardCount">
        <template #default="scope">
          {{ scope.row.rewardCount }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('channelManage.rewardAmoutn')" align="center" prop="rewardAmount">
        <template #default="scope">
          {{ scope.row.rewardAmount }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('channelManage.fee')" align="center" prop="fee">
        <template #default="scope">
          {{ scope.row.fee }}
        </template>
      </el-table-column>
    <el-table-column :label="$t('channelManage.actualAmount')" align="center">
      <template #default="scope">
        {{ Number((scope.row.rewardAmount - scope.row.fee).toFixed(2))  }}
      </template>
    </el-table-column>
      <el-table-column :label="$t('channelManage.channelType')" align="center" prop="isOpen">
        <template #default="scope">
          <div>
            {{
            scope.row.isOpen
            ? $t("channelManage.public")
            : $t("channelManage.private")
            }}
          </div>
        </template>
      </el-table-column>
      <el-table-column :label="$t('channelManage.channelLink')" prop="channelLink" align="center" show-overflow-tooltip
        width="260px">
        <template #default="scope">
          <el-tooltip class="item" effect="dark" :content="$t('channelManage.clickToCopy')" placement="bottom">
            <span style="cursor: pointer" @click="copyContent(scope.row.channelLink)">{{ `${scope.row.channelLink}`
              }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column :label="$t('channelManage.ownerID')" align="center" prop="channelLeaderId">
      </el-table-column>
      <el-table-column :label="$t('channelManage.ownerName')" prop="channelLeaderName" align="center"
        show-overflow-tooltip width="150px" />
      <el-table-column :label="$t('common.createdTime')" prop="createTime" align="center">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('channelManage.officialChannelOrNot')" align="center" prop="isOfficial" width="150px">
        <template #default="scope">
          <el-switch v-model="scope.row.isOfficial" :active-value="1" :inactive-value="0"
            :disabled="scope.row.status == 1" :before-change="() => changeSwitchStatus(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('channelReview.status')" align="center" prop="authStatus" width="150px">
        <template #default="scope">
          <div :class="[`auth-status-${scope.row.authStatus}`]">
            <div v-if="scope.row.authType === 1">{{ $t('channelReview.personal') }}</div>
            <div v-else-if="scope.row.authType === 2">{{ $t('channelReview.enterprise') }}</div>
            <div v-else>{{ $t('channelReview.weiVerify') }}</div>
            <template v-if="scope.row.authType">
              <div v-if="scope.row.authStatus === 1">{{ $t('channelReview.inReview') }}</div>
              <div v-if="scope.row.authStatus === 2">{{ $t('channelReview.notPassed') }}</div>
              <div v-if="scope.row.authStatus === 3">{{ $t('channelReview.passed') }}</div>
              <div v-if="scope.row.authStatus === 4">{{ $t('channelReview.cancelVerify') }}</div>
            </template>
          </div>
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.state')" prop="status" align="center" width="100">
        <template #default="scope">
          <el-tag type="danger" v-if="scope.row.status == 2">{{
            $t("groupManage.deleted")
            }}</el-tag>
          <el-tag type="danger" v-if="scope.row.status == 1">{{
            $t("common.freeze")
            }}</el-tag>
          <el-tag type="success" v-if="scope.row.status == 0">{{
            $t("channelManage.normal")
            }}</el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="freezeTime" align="center" width="100" :label="$t('userManage.FrozenUntil')">
        <template v-slot="scope">
          <span v-if="scope.row.freezeTime > 3000000000000">{{
            $t("userManage.Forever")
            }}</span>
          <span v-else="scope.row.freezeTime">{{
            parseTime(scope.row.freezeTime)
            }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.operate')" align="center" width="180" class-name="small-padding fixed-width"
        fixed="right">
        <template #default="scope">
          <el-button v-if="scope.row.authStatus === 3" type="text" @click="cancelVerify(scope.row.id)">{{
            $t("channelReview.cancelVerify") }}</el-button>
          <el-button type="text" v-if="scope.row.status == 1" @click="showUnFreeze(scope.row)">{{ $t("common.unfreeze")
            }}</el-button>
          <el-button v-if="scope.row.status == 0 && scope.row.isOfficial == 0" type="text"
            @click="showFreeze(scope.row.id)">{{ $t("common.freeze") }}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog v-model="memberDialogVisivle" width="800px" :title="$t('channelManage.subscribers')">
      <MemberView v-if="memberDialogVisivle" :groupId="selectedId" />
    </el-dialog>
    <el-dialog v-model="postDialogVisivle" width="1200px" :title="$t('channelManage.post')">
      <PostView v-if="postDialogVisivle" :groupId="selectedId" />
    </el-dialog>
    <el-dialog v-if="state.moreDialogVisible" v-model="state.moreDialogVisible" width="400px" append-to-body
      custom-class="el-dialog-no-header">
      <Freeze :params="seleectedRowData" @close="closeMoreDialog" from="group"></Freeze>
    </el-dialog>

    <pagination v-show="state.total > 0" :total="state.total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />
  </div>
</template>

<script setup name="FundWithdrawalReview">
import { reactive, ref } from 'vue'
import { pageChannelList, changeOfficial, cancelAuth } from '@/api/channel/index'
import { ElMessage, ElMessageBox } from 'element-plus'
import Freeze from '@/views/consumer/components/freeze.vue'

import MemberView from '../channel/member.vue'
import PostView from '../channel/post.vue'
import { groupFreeze } from '@/api/consumer/imuser'
import i18n from '../../i18n'

// const host = ref(import.meta.env.VITE_APP_POST_HOST)

const queryFormRef = ref()

// 搜索参数
const queryParams = ref({
  isOpen: '',
  channelName: '',
  channelLink: '',
  ownerId: '',
  ownerNickname: '',
  status: '',
  pageNum: 1,
  pageSize: 10
})

const state = reactive({
  showSearch: true,
  getListLoading: false,
  tableData: [],
  total: 0,
  selectedIds: [],
  dialogVisible: false,
  dialogTitle: '',
  moreDialogVisible: false
})

// 获取列表数据
function getList() {
  state.getListLoading = true
  pageChannelList(queryParams.value)
    .then((response) => {
      state.tableData = response.data.records
      state.total = response.data.total
    })
    .finally(() => {
      state.getListLoading = false
    })
}
// 查询数据
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}
// 重置查询
const resetQuery = (formEl) => {
  if (!formEl) return
  console.log(formEl)
  formEl.resetFields()
  getList()
}

const memberDialogVisivle = ref(false)
const postDialogVisivle = ref(false)
const selectedId = ref(null)
const showDialog = (id, type) => {
  if (type === 1) {
    memberDialogVisivle.value = true
  } else {
    postDialogVisivle.value = true
  }
  selectedId.value = id
}

// 操作弹窗
const seleectedRowData = ref({
  userId: null
})

function copyContent(text) {
  const InputDom = document.createElement('input')
  InputDom.value = text
  document.body.appendChild(InputDom)
  InputDom.select() // 选择对象
  document.execCommand('Copy') // 执行浏览器复制命令
  InputDom.remove()
  ElMessage.success(i18n.global.t('msg.copied'))
}

function closeMoreDialog(type) {
  state.moreDialogVisible = false
  if (type === 'init') {
    // state.tableData.map((i) => {
    //   if (i.id === seleectedRowData.value.userId) {
    //     i.status = 1;
    //   }
    //   return i;
    // });
    getList()
  }
}

function showFreeze(userId) {
  seleectedRowData.value.userId = userId
  state.moreDialogVisible = true
}

function showUnFreeze(data) {
  ElMessageBox.confirm(
    i18n.global.t('common.confirmUnfrozen'),
    i18n.global.t('common.tips'),
    {
      confirmButtonText: i18n.global.t('common.confirm'),
      cancelButtonText: i18n.global.t('common.cancel'),
      type: 'warning'
    }
  )
    .then(() => {
      const params = {
        freezeReason: 0,
        isFreeze: false,
        targetId: data.id,
        time: 0,
        unit: ''
      }
      groupFreeze(params).then((response) => {
        ElMessage({
          type: 'success',
          message: i18n.global.t('common.unfreezeMsg')
        })
        getList()
      })
    })
    .catch(() => {
      // ElMessage.error({
      //   message: '操作失败！'
      // })
    })
}

function cancelVerify(id) {
  ElMessageBox.confirm(
    i18n.global.t('common.confirmCancelChannelVerify'),
    i18n.global.t('common.tips'),
    {
      confirmButtonText: i18n.global.t('common.confirm'),
      cancelButtonText: i18n.global.t('common.cancel'),
      type: 'warning'
    }
  )
    .then(() => {
      const params = {
        channelId: id
      }
      cancelAuth(params).then(() => {
        ElMessage({
          type: 'success',
          message: i18n.global.t('common.cancelVerifyMsg')
        })
        getList()
      })
    })
    .catch(() => {
      // ElMessage.error({
      //   message: '操作失败！'
      // })
    })
}

function changeSwitchStatus(row) {
  return new Promise((resolve, reject) => {
    const params = {
      groupId: row.id
    }
    changeOfficial(params)
      .then((response) => {
        if (response.data !== null) {
          ElMessage.success(i18n.global.t('common.success'))
          resolve(true)
        } else {
          ElMessage.success(i18n.global.t('common.failed'))
          reject(new Error('Error'))
        }
      })
      .catch(() => {
        if (row) row.isOfficial = !row.isOfficial
        reject(new Error('Error'))
      })
  })
}

getList()
</script>
<style scoped>
.img-msg {
  width: 60px;
  height: 60px;
  border-radius: 6px;
  background: #eee;
}
</style>
