<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryFormRef"
      v-show="state.showSearch"
      :inline="true"
    >
      <el-form-item label="">
        <el-select
          style="width: 150px"
          v-model="queryParams.searchKey"
          class="m-2"
          :placeholder="$t('common.select')"
        >
          <el-option :label="$t('groupManage.groupID')" :value="1" />
          <el-option :label="$t('groupManage.groupNickName')" :value="2" />
          <!--          <el-option :label="$t('groupManage.groupOwnerId')" :value="3" />-->
        </el-select>
        <el-input
          v-model.trim="queryParams.input"
          :placeholder="$t('common.pleaseEnter')"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('groupManage.isPaid')">
        <el-select
          style="width: 150px"
          v-model="queryParams.isPayEnabled"
          class="m-2"
          clearable
          :placeholder="$t('common.select')"
        >
          <el-option :label="$t('common.yes')" :value="true" />
          <el-option :label="$t('common.no')" :value="false" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('common.state')">
        <el-select
          style="width: 150px"
          v-model="queryParams.status"
          class="m-2"
          clearable
          :placeholder="$t('common.pleaseSelectState')"
        >
          <el-option :label="$t('groupManage.normal')" value="NORMAL" />
          <el-option :label="$t('common.freeze')" value="FREEZE" />
          <el-option :label="$t('groupManage.deleted')" value="DISMISSED" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">{{
          $t("common.search")
        }}</el-button>
        <el-button icon="Refresh" @click="resetQuery">{{
          $t("common.reset")
        }}</el-button>
      </el-form-item>
    </el-form>

    <!-- 表格数据 -->
    <el-table v-loading="state.getListLoading" :data="state.tableData">
      <el-table-column :label="$t('groupManage.groupID')" prop="groupId" />
      <el-table-column
        :label="$t('groupManage.groupAvatar')"
        prop="headPortrait"
        align="center"
      >
        <template #default="scope">
          <el-image
            v-if="scope.row.headPortrait"
            style="width: 100px; height: 60px"
            :src="scope.row.headPortrait"
            fit="contain"
          />
          <span v-else>无</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('groupManage.groupNickName')"
        align="center"
        prop="name"
      />
      <el-table-column
        :label="$t('groupManage.groupMembers')"
        align="center"
        prop="memberCount"
      >
        <template #default="scope">
          <el-button @click="showDialog(scope.row.id)" type="text">{{
            scope.row.memberCount
          }}</el-button>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('groupManage.isPaid')"
        align="center"
        prop="isPayEnabled"
      >
        <template #default="scope">
          <span v-if="scope.row.isPayEnabled">{{ $t("common.yes") }}</span>
          <span v-else>{{ $t("common.no") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('groupManage.encryptionOrNot')"
        align="center"
        prop="isEncrypt"
      >
        <template #default="scope">
          <span v-if="scope.row.isEncrypt === 1">{{
            $t("groupManage.encrypted")
          }}</span>
          <span v-else>{{ $t("groupManage.ordinary") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('groupManage.groupStyle')"
        align="center"
        prop="groupType"
      >
        <template #default="scope">
          <span v-if="scope.row.groupType === 0">{{
            $t("groupManage.private")
          }}</span>
          <span v-if="scope.row.groupType === 1">{{
            $t("groupManage.public")
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('groupManage.groupOwnerId')"
        align="center"
        prop="groupLeaderNumber"
      />
      <!--      <el-table-column label="群主ID" align="center" prop="groupLeaderId" />-->
      <el-table-column
        :label="$t('common.createdTime')"
        align="center"
        prop="createTime"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column :label="$t('groupManage.reportedTimes')" prop="beReportCount" align="center" width="100" /> -->
      <el-table-column
        :label="$t('common.state')"
        prop="status"
        align="center"
        width="100"
      >
        <template #default="scope">
          <span style="color: red" v-if="scope.row.status === 'DISMISSED'">{{
            $t("groupManage.deleted")
          }}</span>
          <span v-if="scope.row.status === 'FREEZE'">{{
            $t("common.freeze")
          }}</span>
          <span v-if="scope.row.status === 'NORMAL'">{{
            $t("groupManage.normal")
          }}</span>
        </template>
      </el-table-column>

      <el-table-column
        prop="freezeTime"
        align="center"
        width="100"
        :label="$t('userManage.FrozenUntil')"
      >
        <template v-slot="scope">
          <span v-if="scope.row.freezeTime > 3000000000000">{{
            $t("userManage.Forever")
          }}</span>
          <span v-else="scope.row.freezeTime">{{
            parseTime(scope.row.freezeTime)
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('common.operate')"
        align="center"
        width="120"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            type="text"
            @click="showUnFreeze(scope.row)"
            v-if="scope.row.status === 'FREEZE'"
            >{{ $t("common.unfreeze") }}</el-button
          >
          <el-button
            type="text"
            @click="showFreeze(scope.row.id)"
            v-if="scope.row.status === 'NORMAL'"
            >{{ $t("common.freeze") }}</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <el-dialog v-model="memberDialogVisivle">
      <MemberView v-if="memberDialogVisivle" :groupId="selectedId" />
    </el-dialog>
    <el-dialog
      v-if="state.moreDialogVisible"
      v-model="state.moreDialogVisible"
      :width="state.moreDialogWidth"
      append-to-body
      custom-class="el-dialog-no-header"
    >
      <Freeze
        v-if="state.moreDialogType === '冻结'"
        :params="seleectedRowData"
        from="group"
        @close="closeMoreDialog"
      ></Freeze>
    </el-dialog>

    <pagination
      v-show="state.total > 0"
      :total="state.total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script setup name="Group/index">
import { reactive, ref } from "vue";
import { modifyDrawReviewStatus } from "@/api/fund/withdrewreview";
import { pageGroupList, unsealOrBanGroup } from "@/api/group/group";
import { groupFreeze } from "@/api/consumer/imuser";
import { ElMessage, ElMessageBox } from "element-plus";
import Freeze from "@/views/consumer/components/freeze.vue";

import MemberView from "./member.vue";
import i18n from "../../i18n";

// const { proxy } = getCurrentInstance()

const queryFormRef = ref();
// const formRef = ref()

function pass(data) {
  ElMessageBox.confirm(i18n.global.t("common.freeze"), {
    confirmButtonText: i18n.global.t("common.confirm"),
    cancelButtonText: i18n.global.t("common.cancel"),
    type: "warning",
  })
    .then(() => {
      unsealOrBanGroup(data)
        .then((response) => {
          getList();
        })
        .catch((err) => {
          alert(err.message);
        });
    })
    .catch(() => {});
}
function reject(data) {
  ElMessageBox.confirm(i18n.global.t("common.unfreeze"), {
    confirmButtonText: i18n.global.t("common.confirm"),
    cancelButtonText: i18n.global.t("common.cancel"),
    type: "warning",
  })
    .then(() => {
      unsealOrBanGroup(data)
        .then((response) => {
          getList();
        })
        .catch((err) => {
          alert(err.message);
        });
    })
    .catch(() => {});
}

// 搜索参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  searchKey: 1,
  input: null,
  status: null,
  isPayEnabled: null,
});

const state = reactive({
  showSearch: true,
  getListLoading: false,
  tableData: [],
  total: 0,
  selectedIds: [],
  dialogVisible: false,
  dialogTitle: "",
  moreDialogTitle: "",
  moreDialogVisible: false,
  moreDialogType: "",
  moreDialogWidth: "500px",
});

// const formParams = ref({})
// const formRules = reactive({
//   nickName: [
//     { required: true, message: 'Please input Activity name', trigger: 'blur' },
//     { min: 3, max: 5, message: 'Length should be 3 to 5', trigger: 'blur' }
//   ]
// })

// 获取列表数据
function getList() {
  state.getListLoading = true;
  const params = queryParams.value;
  pageGroupList(queryParams.value)
    .then((response) => {
      state.tableData = response.data.records;
      state.total = response.data.total;
    })
    .finally(() => {
      state.getListLoading = false;
    });
}
// 查询数据
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}
// 重置查询
function resetQuery() {
  if (queryFormRef.value) {
    queryFormRef.value.resetFields();
  }
  queryParams.value.input = null;
  queryParams.value.status = null;
  queryParams.value.isPayEnabled = null;
  handleQuery();
}

// 多选框选中数据
// function handleSelectionChange (selection) {
//   state.selectedIds = selection.map((item) => item.roleId)
// }

// function resetForm () {
//   formParams.value = {
//     nickName: ''
//   }
//   if (formRef.value) {
//     formRef.value.resetFields()
//   }
// }
// 修改
// function handleUpdate (row) {
//   resetForm()

//   setTimeout(() => {
//     formParams.value.ref = {}
//     state.dialogTitle = '修改'
//     state.dialogVisible = true
//   }, 1000)
// getJob(jobId).then(response => {
//   form.value = response.data;
//   open.value = true;
//   title.value = "修改任务";
// });
// }

// 取消按钮
// function cancel () {
//   state.dialogVisible = false
// }

// 提交
// async function submitForm () {
//   if (!formRef.value) return
//   formRef.value.validate((valid, fields) => {
//     if (valid) {
//       if (state.dialogTitle === '新增') {
//         console.log('新增')
//       }
//       if (state.dialogTitle === '修改') {
//         console.log('修改')
//       }
//     }
//   })
// }

const memberDialogVisivle = ref(false);
const selectedId = ref("");
function showDialog(id) {
  selectedId.value = id;
  memberDialogVisivle.value = true;
}

// 操作弹窗
const seleectedRowData = ref({
  userId: null,
  nickname: null,
});

function closeMoreDialog(type) {
  state.moreDialogVisible = false;
  state.moreDialogType = null;
  state.getListLoading = true;
  // if (type === 'init') {
  //   state.tableData.map(i => {
  //     if(i.id === seleectedRowData.value.userId){
  //       i.status = 'FREEZE'
  //       }
  //       return i
  //     })
  // }
  getList();
}

function showFreeze(userid) {
  seleectedRowData.value.userId = userid;
  state.moreDialogWidth = "450px";
  state.moreDialogType = "冻结";
  state.moreDialogVisible = true;
}

function showUnFreeze(data) {
  ElMessageBox.confirm(
    i18n.global.t("common.confirmUnfrozen"),
    i18n.global.t("common.tips"),
    {
      confirmButtonText: i18n.global.t("common.confirm"),
      cancelButtonText: i18n.global.t("common.cancel"),
      type: "warning",
    }
  )
    .then(() => {
      const params = {
        freezeReason: 0,
        isFreeze: false,
        targetId: data.id,
        time: 0,
        unit: "",
      };
      groupFreeze(params).then((response) => {
        ElMessage({
          type: "success",
          message: i18n.global.t("common.unfreezeMsg"),
        });
        getList();
      });
    })
    .catch(() => {
      // ElMessage.error({
      //   message: '操作失败！'
      // })
    });
}

getList();
</script>
