<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryFormRef" v-show="state.showSearch" :inline="true">
      <el-form-item label="">
        <el-select style="width: 170px" v-model="queryParams.searchKey" class="m-2" :placeholder="$t('common.select')">
          <el-option :label="$t('common.phoneNumber')" :value="1" />
          <el-option :label="$t('groupManage.memberNickname')" :value="2" />
          <el-option :label="$t('groupManage.memberID')" :value="3" />
        </el-select>
        <el-input v-model.trim="queryParams.input" :placeholder="$t('common.pleaseEnter')" clearable style="width: 240px" @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">{{ $t('common.search') }}</el-button>
        <el-button icon="Refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <!-- 表格数据 -->
    <el-table v-loading="state.getListLoading" :data="state.tableData">
      <el-table-column :label="$t('groupManage.memberID')" prop="id" />
      <el-table-column
        :label="$t('common.phoneNumber')"
        prop="phone"
        :show-overflow-tooltip="true"
        width="150"
      >
        <template #default="scope">
          <span v-if="scope.row.phone && scope.row.phone.includes('_')"
            >{{ `+${scope.row.dialCode} ${scope.row.phone.split('_')[1]}` }}</span>
          <span v-else>{{ `+${scope.row.dialCode} ${scope.row.phone}` }}</span>
        </template>
      </el-table-column>
      <!--      <el-table-column :label="$t('common.userId')" prop="id" :show-overflow-tooltip="true" width="100" />-->
      <el-table-column :label="$t('groupManage.country')" prop="dialCode" width="100">
        <template #default="scope">
          <span>{{
            scope.row.dialCode
              ? countryCodeText(scope.row)
              : ""
          }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('groupManage.memberAvatar')" prop="headImg" align="center">
        <template #default="scope">
          <el-image v-if="scope.row.headImg && scope.row.headImg.length > 0" style="width: 60px; height: 60px" :src="scope.row.headImg" fit="cover" />
          <span v-else>{{ $t('safe.none') }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('groupManage.memberNickname')" align="center" prop="name" />
      <el-table-column :label="$t('groupManage.groupName')" align="center" prop="groupName" />
      <el-table-column :label="$t('groupManage.groupID')" align="center" prop="groupId" />
<!--      <el-table-column label="本群昵称" align="center" prop="groupNickname" />-->
      <!-- <el-table-column :label="$t('common.phoneNumber')" align="center" prop="phone" /> -->
<!--      <el-table-column :label="$t('groupManage.banSpeech')" align="center" prop="status">-->
<!--        <template #default="scope">-->
<!--          <span type="text" v-if="scope.row.muted === 1">{{$t('groupManage.mute')}}</span>-->
<!--          <span type="text" v-if="scope.row.muted === 2">{{$t('groupManage.unmute')}}</span>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column :label="$t('groupManage.joinedTime')" align="center" prop="createTime">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>


<!--      <el-table-column :label="$t('common.state')" align="center">-->
<!--        <template #default="scope">-->
<!--          <span type="text" v-if="scope.row.onLineStatus === 0">离线</span>-->
<!--          <span type="text" v-if="scope.row.onLineStatus === 1">在线</span>-->
<!--        </template>-->
<!--      </el-table-column>-->

      <el-table-column :label="$t('groupManage.role')" align="center">
        <template #default="scope">
          <span type="text" v-if="scope.row.role === 1 ">{{ $t('groupManage.member') }} </span>
          <span type="text" v-if="scope.row.role === 2 ">{{ $t('groupManage.admin') }} </span>
          <span type="text" v-if="scope.row.role === 3 ">{{ $t('groupManage.owner') }} </span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('groupManage.memberType')" align="center">
        <template #default="scope">
          <span type="text" v-if="scope.row.memberType === 'USER' ">{{ $t('groupManage.memberTypeUser') }} </span>
          <span type="text" v-if="scope.row.memberType === 'ROBOT' ">{{ $t('groupManage.memberTypeRobot') }} </span>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="state.total > 0" :total="state.total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
  </div>
</template>

<script setup name="FundWithdrawalReview">
import { reactive, ref } from 'vue'
import { modifyDrawReviewStatus } from '@/api/fund/withdrewreview'
import { pageGroupMember } from '@/api/group/group'
import { countryCode } from '@/utils/enum.js'
import i18n from "../../i18n";

const props = defineProps(['groupId'])

console.log(999, props)

const queryFormRef = ref()

// 搜索参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  searchKey: 1,
  input: null,
  muteStatus: null
})

const state = reactive({
  showSearch: true,
  getListLoading: true,
  tableData: [],
  total: 0,
  selectedIds: [],
  dialogVisible: false,
  dialogTitle: ''
})

// 获取列表数据
function getList () {
  state.getListLoading = true
  const params = queryParams.value
  for (const key in params) {
    if (!params[key]) params[key] = null
  }

  pageGroupMember({ ...queryParams.value, groupId: props.groupId })
    .then((response) => {
      state.tableData = response.data.records
      state.total = response.data.total
    })
    .finally(() => {
      state.getListLoading = false
    })
}
// 查询数据
function handleQuery () {
  queryParams.value.pageNum = 1
  getList()
}
// 重置查询
function resetQuery () {
  if (queryFormRef.value) {
    queryFormRef.value.resetFields()
  }
  queryParams.value.input = null
  queryParams.value.muteStatus = null
  handleQuery()
}

function countryCodeText (row) {
  const list = countryCode.filter((i) => i.tel === row.dialCode)
  let txt = ''
  if (list.length) {
    console.log();
    txt = i18n.global.locale === 'en' ? list[0].en : list[0].name
  }
  return txt
}
getList()
</script>
