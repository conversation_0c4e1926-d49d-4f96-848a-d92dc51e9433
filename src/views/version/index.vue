<template>
  <div class="app-container">
    <QueryForm :queryParams="queryParams" @getList="getList" />

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleDialog">{{ $t('common.add') }}</el-button>
      </el-col>
    </el-row>

    <Table :state="state" @handleDialog="handleDialog" @handleStatusChange="handleStatusChange" />

    <pagination
      v-show="state.total > 0"
      :total="state.total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="({ page, limit, ...rest }) => getList({ ...rest, pageNum: page, pageSize: limit })"
    />

    <el-dialog :title="state.dialogTitle ==='详情'?i18n.global.t('common.details'):(state.dialogTitle ==='修改'?i18n.global.t('common.edit'):i18n.global.t('common.add'))"
               v-model="state.dialogVisible" width="80%" :before-close="handleClose" append-to-body>
      <Form
        @submitForm="submitForm"
        :formObject="formObject"
        :isDetail="state.dialogTitle === '详情'"
      >
        <el-button @click="handleClose">{{state.dialogTitle === '详情' ? i18n.global.t('common.return') : i18n.global.t('common.cancel')}}</el-button>
      </Form>
    </el-dialog>
  </div>
</template>

<script setup>
import { reactive, ref, getCurrentInstance } from 'vue'
import { listVersion, updateVersion, createVersion, updateVersionStatus } from '@/api/version'
import QueryForm from './components/queryForm.vue'
import Table from './components/table.vue'
import Form from './components/form.vue'
import i18n from "../../i18n";

const { proxy } = getCurrentInstance()

const formObject = ref()

const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  versionNumber: null,
  platform: null,
  versionType: null,
  status: null
})

const state = reactive({
  tableData: [],
  getListLoading: true,
  total: 0,
  dialogVisible: false,
  dialogTitle: ''
})

function getList () {
  state.getListLoading = true
  listVersion(queryParams.value)
    .then((res) => {
      state.tableData = res.data.records
      state.total = res.data.total
    })
    .finally(() => (state.getListLoading = false))
}

function handleDialog (row, method) {
  formObject.value = method ? { ...row } : {}
  state.dialogVisible = !state.dialogVisible
  state.dialogTitle = method === 'edit' ? '修改' : method === 'detail' ? '详情' : '新增'
}

function handleClose () {
  state.dialogVisible = false
}

async function handleStatusChange (row) {
  const text = row.status ? i18n.global.t('version.off') : i18n.global.t('version.on')
  proxy.$modal.confirm(text + '?')
    .then(async function () {
      row.status = await updateVersionStatus(row)
      proxy.$modal.msgSuccess(text + i18n.global.t('common.success'))
      getList()
    }).catch(() => {})
}

function submitForm (values) {
  const onSubmitFuntion = values.versionId ? updateVersion : createVersion
  onSubmitFuntion(values).then((res) => {
    const message = values.versionId ? i18n.global.t('common.success') : i18n.global.t('common.success')
    proxy.$modal.msgSuccess(message)
    state.dialogVisible = false
    getList()
  })
}

getList()
</script>

<style scoped lang="scss">
    .title {
      margin-bottom: 20px;
      font-size: 16px;
      font-weight: bold;
    }
</style>
