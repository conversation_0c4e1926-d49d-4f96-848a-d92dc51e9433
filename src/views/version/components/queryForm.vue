<template>
  <el-form :model="formQueryParams" ref="queryFormRef" :inline="true">
    <el-form-item :label="$t('version.versionNumber')">
      <el-input
        v-model="formQueryParams.versionNumber"
        :placeholder="$t('common.pleaseEnter')" clearable style="width: 240px" />
    </el-form-item>

    <el-form-item :label="$t('version.platform')">
      <el-select style="width: 150px" v-model="formQueryParams.platform" class="m-2" :placeholder="$t('version.platform')">
        <el-option
          v-for="(item, index) in platforms"
          :key="index"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>

    <el-form-item :label="$t('version.versionType')">
      <el-select style="width: 150px" v-model="formQueryParams.versionType" class="m-2" :placeholder="$t('version.versionType')">
        <el-option
          v-for="(item, index) in queryUpdates"
          :key="index"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>

    <el-form-item :label="$t('version.enabledState')">
      <el-select style="width: 150px" v-model="formQueryParams.status" class="m-2" :placeholder="$t('version.enabledState')">
        <el-option
          v-for="(item, index) in statuses"
          :key="index"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>

    <el-form-item>
      <el-button type="primary" icon="Search" @click="handleQuery">{{ $t('common.search') }}
      </el-button>
      <el-button icon="Refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup>
import { ref } from 'vue'
import { platforms, queryUpdates, statuses } from '../instances'
import i18n from "../../../i18n";
const { proxy } = getCurrentInstance()

const props = defineProps({ queryParams: Object })
const emit = defineEmits(['getList', 'update:queryParams'])

const formQueryParams = computed({
  get: () => props.queryParams,
  set: val => context.emit('update:queryParams', val)
})

const queryFormRef = ref()

function handleQuery () {
  formQueryParams.value.pageNum = 1
  const validateVersionRegex = /^v\d{1,2}(\.\d{1,2})*$/

  if (!!formQueryParams.value.versionNumber && !validateVersionRegex.test(formQueryParams.value.versionNumber)) {
    proxy.$modal.msgError(i18n.global.t('version.versionFormatError'))
  } else {
    emit('getList')
  }
}

function resetQuery () {
  formQueryParams.value.platform = null
  formQueryParams.value.versionNumber = null
  formQueryParams.value.versionType = null
  formQueryParams.value.status = null
  proxy.resetForm('queryFormRef')
  emit('getList')
}

</script>
