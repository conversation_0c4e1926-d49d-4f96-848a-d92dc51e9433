<template>
  <el-form ref="formRef" :model="formParams" :rules="rules" label-width="100px">
    <el-row :gutter="20">
      <el-col v-for="field in fields" :span="12" :key="field.key">
        <el-form-item :label="field.label" :prop="field.key">
          <el-input v-if="field.type === 'text'" v-model="formParams[field.key]" v-bind="field.props" :disabled="props.isDetail" />

          <el-input v-if="field.type === 'textarea'" v-model="formParams[field.key]" v-bind="field.props" type="textarea" :disabled="props.isDetail" />

          <el-select v-if="field.type === 'select'" v-model="formParams[field.key]" v-bind="field.selectProps" :disabled="props.isDetail">
            <el-option
              v-for="(item, index) in field.options"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20" justify="end">
      <el-col class="right" :span="2">
        <slot />
      </el-col>
      <el-col v-if="!props.isDetail" :span="2">
        <el-button type="primary" @click="handleSubmit">{{ $t('common.submit') }}</el-button>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup>
import { computed, watch, ref, getCurrentInstance } from 'vue'
import { fields as formFields, rules } from '../instances'

const { proxy } = getCurrentInstance()

const props = defineProps({ isDetail: Boolean, formObject: Object })
const emit = defineEmits(['submitForm', 'update:formParams'])

const fields = ref(formFields)
const formRef = ref()
const formParams = computed(() => props.formObject)

function handleSubmit () {
  proxy.$refs.formRef.validate(async valid => {
    if (valid) {
      emit('submitForm', formParams.value)
    }
  })
}

watch(() => props.formObject, () => {
  proxy.$refs.formRef.resetFields()
})

</script>

<style scoped lang="scss">
  .right {
    display: flex;
    justify-content: flex-end;
  }
</style>
