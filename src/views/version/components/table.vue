<template>
  <el-table v-loading="state.getListLoading" :data="state.tableData">
    <el-table-column :label="$t('version.versionNumber')" prop="versionNumber" align="center" />
    <el-table-column :label="$t('version.platform')" prop="platform" align="center">
      <template #default="scope">
        {{ getLabel(platforms, scope.row.platform) }}
      </template>
    </el-table-column>
    <el-table-column :label="$t('version.versionLink')" prop="downloadUrl" align="center">
      <template #default="scope">
        {{  limitWordCounts(scope.row.downloadUrl, 20) }}
      </template>
    </el-table-column>
    <el-table-column :label="$t('version.updateContent')" prop="changelogZh" align="center">
      <template #default="scope">
        {{  limitWordCounts(scope.row.changelogZh) }}
      </template>
    </el-table-column>
    <el-table-column :label="$t('version.updateContentEn')" prop="changelogEn" align="center">
      <template #default="scope">
        {{  limitWordCounts(scope.row.changelogEn) }}
      </template>
    </el-table-column>
    <el-table-column :label="$t('version.versionType')" prop="versionType" align="center">
      <template #default="scope">
        {{ getLabel(updateTypes, scope.row.versionType) }}
      </template>
    </el-table-column>
    <el-table-column :label="$t('version.enabledState')" prop="status" align="center">
      <template #default="scope">
        <el-switch
          v-model="scope.row.status"
          :before-change="() => emit('handleStatusChange', scope.row)"
        />
      </template>
    </el-table-column>
    <el-table-column :label="$t('common.createdTime')" prop="createTime" align="center" >
            <template #default="scope">
             <span>{{  parseTime(scope.row.createTime)}}</span>
        </template>
    </el-table-column>
    <el-table-column :label="$t('common.remark')" prop="remark" align="center">
      <template #default="scope">
        {{  limitWordCounts(scope.row.remark) }}
      </template>
    </el-table-column>

    <el-table-column :label="$t('common.operate')" align="center" class-name="small-padding fixed-width">
      <template #default="scope">
        <el-button type="text" icon="Edit" @click="emit('handleDialog', scope.row, 'edit')">{{ $t('common.edit') }}</el-button>
        <el-button type="text" icon="View" @click="emit('handleDialog', scope.row, 'detail')">{{ $t('common.details') }}</el-button>
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup>
import { platforms, updateTypes } from '../instances'
const props = defineProps({ state: Object })
const emit = defineEmits(['handleDialog', 'handleStatusChange'])
function getLabel (array, value) {
  return array.find(item => item.value === value).label
}

function limitWordCounts (string = '', count = 10) {
  if (!string) return ''
  const zhRegExp = /[\u3040-\u30ff\u3400-\u4dbf\u4e00-\u9fff\uf900-\ufaff\uff66-\uff9f]/g
  if (zhRegExp.test(string)) {
    return string?.slice(0, count) + (string?.length > count ? '...' : '')
  } else {
    const arrayString = string?.split(' ')
    return arrayString?.length > 1
      ? string?.split(' ').slice(0, count).join(' ') + (string?.split(' ')?.length > count ? '...' : '')
      : string?.slice(0, count) + (string?.length > count ? '...' : '')
  }
}
</script>
