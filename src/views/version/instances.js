// Query Options
import i18n from "../../i18n";

export const queryUpdates = [
  { label: i18n.global.t('version.noNeedUpdate'), value: 'NOT_REQUIRE_UPDATE' },
  { label: i18n.global.t('version.optionalUpdate'), value: 'OPTIONAL_UPDATE' },
  { label: i18n.global.t('version.forceUpdate'), value: 'FORCE_UPDATE' }
]

// Options
export const platforms = [
  { label: 'Android', value: 'ANDROID' },
  { label: 'IOS', value: 'IOS' },
  { label: 'Mac', value: 'MAC_OS' },
  { label: 'Windows', value: 'WINDOWS' }
]

export const updateTypes = [
  { label: i18n.global.t('version.noNeedUpdate'), value: 'N' },
  { label: i18n.global.t('version.optionalUpdate'), value: 'O' },
  { label: i18n.global.t('version.forceUpdate'), value: 'F' }
]

export const statuses = [
  { label: i18n.global.t('version.on'), value: true },
  { label: i18n.global.t('version.off'), value: false }
]

// Form Rules
const versionRegex = /^v\d{1,2}\.\d{1,2}\.\d{1,5}$/;
export const rules = {
  versionNumber: [
    { required: true, message: i18n.global.t('common.cannotBeEmpty'), trigger: 'blur' },
    { pattern: versionRegex, message: i18n.global.t('version.versionFormatError'), trigger: 'blur' }
  ],
  downloadUrl: [{ required: true, message: i18n.global.t('common.cannotBeEmpty'), trigger: 'blur' }],
  changelogZh: [{ required: true, message: i18n.global.t('common.cannotBeEmpty'), trigger: 'blur' }],
  changelogEn: [{ required: true, message: i18n.global.t('common.cannotBeEmpty'), trigger: 'blur' }],
  platform: [{ required: true, message: i18n.global.t('common.cannotBeEmpty'), trigger: 'change' }],
  versionType: [{ required: true, message: i18n.global.t('common.cannotBeEmpty'), trigger: 'change' }]
}

// Form Fields
export const fields = [
  {
    label: i18n.global.t('version.versionNumber'),
    key: 'versionNumber',
    type: 'text',
    props: { maxlength: '100', placeholder: i18n.global.t('common.pleaseEnter') }
  },
  {
    label: i18n.global.t('version.versionLink'),
    key: 'downloadUrl',
    type: 'text',
    props: { maxlength: '500', placeholder: i18n.global.t('common.pleaseEnter') }
  },
  {
    label: i18n.global.t('version.updateContent'),
    key: 'changelogZh',
    type: 'textarea',
    props: { maxlength: '500', placeholder: i18n.global.t('version.updateContent'), row: '3', showWordLimit: true }
  },
  {
    label: i18n.global.t('version.updateContentEn'),
    key: 'changelogEn',
    type: 'textarea',
    props: { maxlength: '500', placeholder: i18n.global.t('version.updateContentEn'), row: '3', showWordLimit: true }
  },
  {
    label: i18n.global.t('version.platform'),
    key: 'platform',
    type: 'select',
    options: platforms,
    selectProps: { style: 'width: 100%', placeholder: i18n.global.t('common.select') }
  },
  {
    label: i18n.global.t('version.versionType'),
    key: 'versionType',
    type: 'select',
    options: updateTypes,
    selectProps: { style: 'width: 100%', placeholder: i18n.global.t('common.select') }
  },
  {
    label: i18n.global.t('common.remark'),
    key: 'remark',
    type: 'textarea',
    props: { maxlength: '500', placeholder: i18n.global.t('common.pleaseEnter'), row: '3', showWordLimit: true }
  }
]
