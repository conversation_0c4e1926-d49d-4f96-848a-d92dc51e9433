<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryFormRef" v-show="state.showSearch" :inline="true">
      <el-form-item label="">
<!--        <el-select style="width: 100px" v-model="queryParams.languageType" class="m-2" :placeholder="$t('common.select')>-->
<!--          <el-option label="中文简体" :value="1"/>-->
<!--          <el-option label="中文繁体" :value="2"/>-->
<!--          <el-option label="英文" :value="3"/>-->
<!--          <el-option label="柬文" :value="4"/>-->
<!--        </el-select>-->
        <el-input v-model.trim="queryParams.word" :placeholder="$t('common.pleaseEnter')" clearable style="width: 240px"
                  @keyup.enter="handleQuery"/>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">{{ $t('common.search') }}</el-button>
        <el-button icon="Refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>

<!--    <el-row :gutter="10" class="mb8">-->
<!--      <el-col :span="1.5">-->
<!--        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['system:role:add']">新增-->
<!--        </el-button>-->
<!--      </el-col>-->

<!--    </el-row>-->

    <el-row :gutter="10" class="mb8">
      <el-tabs v-model="activeName" @tab-click="handleQuery">
        <el-tab-pane :label="$t('safe.chineseIP')" name="chinese"></el-tab-pane>
        <el-tab-pane :label="$t('safe.cambodianIP')" name="cambodian"></el-tab-pane>
        <el-tab-pane :label="$t('safe.otherIP')" name="other"></el-tab-pane>
      </el-tabs>

      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['system:role:add']">{{ $t('common.add') }}
        </el-button>
      </el-col>

    </el-row>

    <!-- 表格数据 -->
    <el-table v-loading="state.getListLoading" :data="state.tableData">
      <el-table-column :label="$t('common.text')" prop="word"/>
      <el-table-column :label="$t('safe.addTime')" align="center" prop="createTime">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, "{y}-{m}-{d} {h}:{i}:{s}") }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.operate')" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button type="text" @click="edit(scope.row)">{{ $t('common.edit') }}</el-button>
          <el-button type="text" @click="deleteQuestion(scope.row)">{{ $t('common.delete') }}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog v-model="memberDialogVisivle">
      <MemberView v-if="memberDialogVisivle" :groupId="selectedId"/>
    </el-dialog>

    <pagination v-show="state.total > 0" :total="state.total" v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize" @pagination="getList"/>

    <!-- 添加对话框 -->
    <el-dialog :title="state.dialogTitle" v-model="state.dialogVisible" width="500px" append-to-body>
      <el-form ref="formRef" :model="formParams" :rules="formRules" label-width="100px">
        <el-form-item label="ip" prop='area'>
          <el-input v-model="countryName" :placeholder="$t('common.pleaseEnter')" readonly="readonly"/>
        </el-form-item>
        <el-form-item :label="$t('common.text')" prop='word'>
          <el-input v-model="formParams.word" :placeholder="$t('common.pleaseEnter')"/>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">{{ $t('common.confirm') }}</el-button>
          <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 修改对话框 -->
    <el-dialog :title="state.dialogTitle" v-model="state.editDialogVisible" width="500px" append-to-body>
      <el-form ref="formRef" :model="formParams" :rules="formRules" label-width="100px">
        <el-form-item label="ip" prop='area'>
          <el-input v-model="countryName" :placeholder="$t('common.pleaseEnter')" readonly="readonly"/>
        </el-form-item>
        <el-form-item :label="$t('common.text')" prop='word'>
          <el-input v-model="formParams.word" :placeholder="$t('common.pleaseEnter')"/>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">{{ $t('common.confirm') }}</el-button>
          <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        </div>
      </template>
    </el-dialog>

  </div>
</template>

<script setup name="FundWithdrawalReview">
import {
  pageSensitiveWord,
  saveSensitiveWord,
  deleteSensitiveWord
} from '@/api/sensitiveWord/sensitiveWord'
import { ElMessage, ElMessageBox } from 'element-plus'
import i18n from "../../i18n";

const { proxy } = getCurrentInstance()
const formRules = reactive({
  word: [{ max: 255, message: i18n.global.t('common.charactersExceeds') + '255', trigger: 'blur' }],
})
const queryFormRef = ref()

// 搜索参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  area: 'chinese',
  word: undefined
})

const activeName = ref('chinese')
const countryName = ref()

const state = reactive({
  showSearch: true,
  getListLoading: true,
  tableData: [],
  total: 0,
  selectedIds: [],
  dialogVisible: false,
  dialogTitle: '',
  editDialogVisible: false
})

// 获取列表数据
function getList () {
  state.getListLoading = true
  const params = queryParams.value
  for (const key in params) {
    if (!params[key]) params[key] = null
  }
  pageSensitiveWord(queryParams.value)
    .then((response) => {
      state.tableData = response.data.records
      state.total = response.data.total
    })
    .finally(() => {
      state.getListLoading = false
    })
}

// 查询数据
function handleQuery () {
  queryParams.value.pageNum = 1
  if (activeName.value === undefined) {
    queryParams.value.area = 'chinese'
  } else {
    queryParams.value.area = activeName
  }
  getList()
}

// 重置查询
function resetQuery () {
  if (queryFormRef.value) {
    queryFormRef.value.resetFields()
  }
  queryParams.value.word = null
  queryParams.value.languageType = 1
  queryParams.value.title = null
  handleQuery()
}

// 多选框选中数据
// function handleSelectionChange (selection) {
//   state.selectedIds = selection.map((item) => item.roleId)
// }

// 删除
const deleteQuestion = (data) => {
  ElMessageBox.confirm(i18n.global.t('safe.sureToDelete'), {
    confirmButtonText: i18n.global.t('common.confirm'),
    cancelButtonText: i18n.global.t('common.cancel'),
    type: 'warning'
  })
    .then(() => {
      deleteSensitiveWord(data).then((response) => {
        if (response.code === 200) {
          ElMessage.success(i18n.global.t('common.success'))
          getList()
        } else {
          ElMessage.error(i18n.global.t('common.failed'))
        }
      })
    })
    .catch(() => {})
}

const memberDialogVisivle = ref(false)
const selectedId = ref('')

// 查看详情
function handleView (data) {
  state.dialogTitle = i18n.global.t('common.details')
  formParams.value = data
  state.detailDialog = true
}

// 编辑
function edit (data) {
  reset()
  state.dialogTitle = i18n.global.t('common.edit')
  formParams.value = { ...data }
  if (formParams.value.area === 'chinese') {
    countryName.value = i18n.global.t('safe.chineseIP')
  }
  if (formParams.value.area === 'cambodian') {
    countryName.value = i18n.global.t('safe.cambodianIP')
  }
  if (formParams.value.area === 'other') {
    countryName.value = i18n.global.t('safe.otherIP')
  }
  activeName.value = formParams.value.area
  state.editDialogVisible = true
}

// 新增相关
function handleAdd () {
  reset()
  state.dialogTitle = i18n.global.t('common.add')
  if (activeName.value === undefined || activeName.value === 'chinese') {
    countryName.value = i18n.global.t('safe.chineseIP')
  }
  if (activeName.value === 'cambodian') {
    countryName.value = i18n.global.t('safe.cambodianIP')
  }
  if (activeName.value === 'other') {
    countryName.value = i18n.global.t('safe.otherIP')
  }

  state.dialogVisible = true
}

// 新增问题参数
const formParams = ref({
  id: '',
  area: '',
  word: ''
})

// 取消按钮
function cancel () {
  reset();
  state.dialogVisible = false
  state.editDialogVisible = false
}

// 提交
async function submitForm() {
  proxy.$refs.formRef.validate((valid) => {
    if (valid) {
      formParams.value.area = activeName.value
      saveSensitiveWord(formParams.value).then((response) => {
        if (response.code === 200) {
          state.editDialogVisible = false
          state.dialogVisible = false
          if (formParams.value.id === '') {
            ElMessage.success(i18n.global.t('common.success'))
          } else {
            ElMessage.success(i18n.global.t('common.success'))
          }
          queryParams.value.area = formParams.value.area
          getList()
        }
      })

    }
  })
}

function reset () {
  formParams.value = {
    id: '',
    area: '',
    word: ''
  }
  proxy.resetForm('formRef')
}

getList()
</script>
