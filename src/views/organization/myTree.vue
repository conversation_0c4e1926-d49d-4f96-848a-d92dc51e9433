<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--部门数据-->
      <el-col :span="4" :xs="24">
        <div class="head-container">
          <el-tree
            :data="deptOptions"
            :props="{ label: function(data, node){
              if(data.label.length > 7){
                return data.label.slice(0,7)+'...';
              }else{
                return data.label;
              }
            },
             children: 'children' }"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            ref="deptTreeRef"
            highlight-current
            default-expand-all
            @node-click="handleNodeClick"
            overflow: auto
          />
        </div>
      </el-col>
      <!--用户数据-->
      <el-col :span="20" :xs="24">
        <el-form
          :model="queryParams"
          ref="queryRef"
          :inline="true"
          v-show="showSearch"
          label-width="68px"
        >
          <el-form-item :label="$t('system.userName')" prop="nickname">
            <el-input
              v-model="queryParams.nickname"
              :placeholder="$t('common.pleaseEnter')"
              clearable
              style="width: 240px"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item :label="$t('common.phoneNumber')" prop="phone">
            <el-input
              v-model="queryParams.phone"
              :placeholder="$t('common.pleaseEnter')"
              clearable
              style="width: 240px"
              @keyup.enter="handleQuery"
            />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery"
              >{{ $t('common.search') }}</el-button
            >
            <el-button icon="Refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
          </el-form-item>
        </el-form>

        <el-table
          v-loading="loading"
          :data="organizationUserList"
          @selection-change="handleSelectionChange"
        >
          <el-table-column
            label="用户id"
            align="center"
            key="memberId"
            prop="memberId"
            v-if="columns[1].visible"
          />
          <el-table-column
            :label="$t('common.userNickname')"
            align="center"
            key="nickname"
            prop="nickname"
            v-if="columns[2].visible"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            :label="$t('common.phoneNumber')"
            align="center"
            key="phone"
            prop="phone"
            v-if="columns[3].visible"
            width="120"
          />
          <el-table-column
            label="职位"
            align="center"
            key="positionName"
            prop="positionName"
            v-if="columns[3].visible"
          />
          <el-table-column
            label="部门"
            align="center"
            key="organizationName"
            prop="imOrganizationVo.name"
            v-if="columns[5].visible"
            :show-overflow-tooltip="true"
          />
          <el-table-column
            :label="$t('common.operate')"
            align="center"
            width="150"
            class-name="small-padding fixed-width"
          >
            <template #default="scope">
              <el-button
                type="text"
                icon="Edit"
                @click="handleUpdate(scope.row)"
                >{{ $t('common.edit') }}</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>

    <!-- 添加或修改用户配置对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form :model="form" :rules="rules" ref="userRef" label-width="80px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="归属部门" prop="organizationId">
              <tree-select
                v-model:value="form.organizationId"
                :options="ops"
                :objMap="{ value: 'id', label: 'name', children: 'children' }"
                :placeholder="$t('common.select')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="职位设置" prop="positionId">
                <el-select v-model="form.positionId" :placeholder="$t('common.select')">
                  <el-option
                      v-for="position in positions"
                      :key="position.id"
                      :label="position.name"
                      :value="position.id"
                  ></el-option>
                </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">{{ $t('common.confirm') }}</el-button>
          <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="myTree">
import {
  listOrganizationUser,
  switchDept,
  getOrganizationUser,
} from "@/api/organization/organizationUser";
import { treeSelect as treeSelect1, listDept } from "@/api/organization/organizationDept";
import { positionSelect } from "@/api/organization/organizationPosition";
import i18n from "../../i18n";

const { proxy } = getCurrentInstance();

const organizationUserList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const dateRange = ref([]);
const deptName = ref("");
const deptOptions = ref(undefined);
const positions = ref([]);
const ops = ref([]);

// 列显隐信息
const columns = ref([
  { key: 0, label: "用户编号", visible: true },
  { key: 1, label: "用户名称", visible: true },
  { key: 2, label: "用户昵称", visible: true },
  { key: 3, label: "部门", visible: true },
  { key: 4, label: "手机号码", visible: true },
  { key: 5, label: "状态", visible: true },
  { key: 6, label: "创建时间", visible: true },
]);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    organizationId: undefined,
    nickname: undefined,
    phone: undefined,
  },
  rules: {
    userName: [
      { required: true, message: i18n.global.t('common.cannotBeEmpty'), trigger: "blur" },
      {
        min: 2,
        max: 20,
        message: i18n.global.t('common.strLengthRange') +'2,20',
        trigger: "blur",
      },
    ],
    nickName: [
      { required: true, message: i18n.global.t('common.cannotBeEmpty'), trigger: "blur" },
    ],
    phone: [
      {
        pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
        message: i18n.global.t('common.formatIncorrect'),
        trigger: "blur",
      },
    ],
    organizationId: [
      { required: true, message: i18n.global.t('common.cannotBeEmpty'), trigger: "blur" },
    ],
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 通过条件过滤节点  */
const filterNode = (value, data) => {
  if (!value) return true;
  return data.label.indexOf(value) !== -1;
};
/** 根据名称筛选部门树 */
watch(deptName, (val) => {
  proxy.$refs.deptTreeRef.filter(val);
});
/** 查询部门下拉树结构 */
function getTreeselect() {
  treeSelect1().then((response) => {
    deptOptions.value = response.data;
  });
}
/** 查询用户列表 */
function getList() {
  loading.value = true;
  listOrganizationUser(
    proxy.addDateRange(queryParams.value, dateRange.value)
  ).then((res) => {
    loading.value = false;
    organizationUserList.value = res.rows;
    total.value = res.total;
  });
}
/** 节点单击事件 */
function handleNodeClick(data) {
  queryParams.value.organizationId = data.id;
  handleQuery();
}
/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 选择条数  */
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.userId);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 初始化部门数据 */
function initTreeData() {
  // 判断部门的数据是否存在，存在不获取，不存在则获取
  if (deptOptions.value === undefined) {
    treeSelect1().then((response) => {
      deptOptions.value = response.data;
    });
  }
}
/** 重置操作表单 */
function reset() {
  form.value = {
    nickName: undefined,
    phone: undefined,
    phonenumber: undefined,
    email: undefined,
    sex: undefined,
    status: "0",
    remark: undefined,
    postIds: [],
    organizationId: undefined,
    roleIds: [],
    positionName: undefined,
    positionId: undefined,
  };
  proxy.resetForm("userRef");
}
/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 修改按钮操作 */
async function handleUpdate(row) {
  reset();
  await listDept().then((response) => {
    ops.value = proxy.handleTree(response.data, "id");
  });
  await positionSelect().then((response) => {
    positions.value = response.rows;
  });
  getOrganizationUser(row.id).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "修改部门";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs.userRef.validate((valid) => {
    if (valid) {
      if (form.value.memberId != undefined) {
        switchDept(form.value).then((response) => {
          proxy.$modal.msgSuccess(i18n.global.t('common.success'));
          open.value = false;
          getList();
        });
      } else {
        addUser(form.value).then((response) => {
          proxy.$modal.msgSuccess(i18n.global.t('common.success'));
          open.value = false;
          getList();
        });
      }
    }
  });
}

getTreeselect();
getList();
</script>
