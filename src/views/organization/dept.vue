<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
         <el-form-item :label="$t('system.deptName')" prop="deptName">
            <el-input
               v-model="queryParams.deptName"
               :placeholder="$t('common.pleaseEnter')"
               clearable
               @keyup.enter="handleQuery"
            />
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">{{ $t('common.search') }}</el-button>
            <el-button icon="Refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
         </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
         <el-col :span="1.5">
            <el-button
               type="primary"
               plain
               icon="Plus"
               @click="handleAdd"
            >{{ $t('common.add') }}</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button
               type="info"
               plain
               icon="Sort"
               @click="toggleExpandAll"
            >{{ $t('system.expandCollapse') }}</el-button>
         </el-col>
         <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table
         v-if="refreshTable"
         v-loading="loading"
         :data="deptList"
         row-key="id"
         :default-expand-all="isExpandAll"
         :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      >
         <el-table-column prop="name" label="部门名称"></el-table-column>
         <el-table-column :label="$t('common.createdTime')" align="center" prop="createTime" width="180">
            <template #default="scope">
               <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
         </el-table-column>
         <el-table-column :label="$t('common.operate')" align="center" class-name="small-padding fixed-width">
            <template #default="scope">
               <el-button
                  type="text"
                  icon="Edit"
                  @click="handleUpdate(scope.row)"
               >{{ $t('common.edit') }}</el-button>
               <el-button
                  v-if="scope.row.id != undefined"
                  type="text"
                  icon="Delete"
                  @click="handleDelete(scope.row)"
               >{{ $t('common.delete') }}</el-button>
            </template>
         </el-table-column>
      </el-table>

      <!-- 添加或修改部门对话框 -->
      <el-dialog :title="title" v-model="open" width="600px" append-to-body>
         <el-form v-if="open" ref="deptRef" :model="form" :rules="rules" label-width="80px">
            <el-row>
               <el-col :span="24" v-show="data.isShow">
                  <el-form-item label="上级部门" prop="parentId">
                     <tree-select
                        v-model:value="form.parentId"
                        :options="deptOptions"
                        :objMap="{ value: 'id', label: 'name', children: 'children' }"
                        placeholder="选择上级部门"
                     />
                  </el-form-item>
               </el-col>
               <el-col :span="12">
                  <el-form-item label="部门名称" prop="name">
                     <el-input v-model.trim="form.name" :placeholder="$t('common.pleaseEnter')" />
                  </el-form-item>
               </el-col>
            </el-row>
         </el-form>
         <template #footer>
            <div class="dialog-footer">
               <el-button type="primary" @click="submitForm">{{ $t('common.confirm') }}</el-button>
               <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
            </div>
         </template>
      </el-dialog>
   </div>
</template>

<script setup name="OrganizationDept">
import { listDept, getDept, delDept, addDept, updateDept } from '@/api/organization/organizationDept';
import { ref } from 'vue-demi';
import i18n from "../../i18n";

const { proxy } = getCurrentInstance();
const { sys_normal_disable } = proxy.useDict("sys_normal_disable");

const deptList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const title = ref("");
const deptOptions = ref([]);
const isExpandAll = ref(true);
const refreshTable = ref(true);

const data = reactive({
  isShow: true,
  form: {},
  queryParams: {
    name: undefined,
    parentId: undefined
  },
  rules: {
    parentId: [{ required: true, message: i18n.global.t('common.cannotBeEmpty'), trigger: "blur" }],
    name: [
      { required: true, message: i18n.global.t('common.cannotBeEmpty'), trigger: "blur"},
      {min: 1,max: 15,message: i18n.global.t('common.charactersExceeds') + '15',trigger: "blur"},
    ]
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询部门列表 */
function getList() {
  loading.value = true;
  listDept(queryParams.value).then(response => {
    deptList.value = proxy.handleTree(response.data, "id");
    loading.value = false;
  });
}
/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}
/** 表单重置 */
function reset() {
  form.value = {
    deptId: undefined,
    parentId: undefined,
    name: undefined,
    orderNum: 0,
    leader: undefined,
    phone: undefined,
    email: undefined,
    status: "0"
  };
  proxy.resetForm("deptRef");
}
/** 搜索按钮操作 */
function handleQuery() {
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}
/** 新增按钮操作 */
async function handleAdd(row) {
  reset();
  await listDept().then(response => {
    deptOptions.value = proxy.handleTree(response.data, "id");
  });
  data.isShow = true;
  open.value = true;
  title.value = "添加部门";
}
/** 展开/折叠操作 */
function toggleExpandAll() {
  refreshTable.value = false;
  isExpandAll.value = !isExpandAll.value;
  nextTick(() => {
    refreshTable.value = true;
  });
}
/** 修改按钮操作 */
async function handleUpdate(row) {
  reset();
   await listDept().then(response => {
    deptOptions.value = proxy.handleTree(response.data, 'id');
  });
   getDept(row.id).then(response => {
    form.value = response.data;
    data.isShow = false;
    open.value = true;
    title.value = "修改部门";
  });
}
/** 提交按钮 */
function submitForm() {
  proxy.$refs["deptRef"].validate(valid => {
    if (valid) {
      if (form.value.id != undefined) {
        updateDept(form.value).then(response => {
          proxy.$modal.msgSuccess(i18n.global.t('common.success'));
          open.value = false;
          getList();
        });
      } else {
        addDept(form.value).then(response => {
          proxy.$modal.msgSuccess(i18n.global.t('common.success'));
          open.value = false;
          getList();
        });
      }
    }
  });
}
/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal.confirm(i18n.global.t('system.confirmDelete') + row.name ).then(function() {
    return delDept(row.id);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess(i18n.global.t('common.success'));
  }).catch(() => {});
}

getList();
</script>
