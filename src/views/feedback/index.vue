<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryFormRef" v-show="state.showSearch" :inline="true">
      <el-form-item label="">
        <el-select style="width: 100px" v-model="queryParams.searchKey" class="m-2" :placeholder="$t('common.select')">
          <el-option :label="$t('common.userNickname')" :value="1" />
          <el-option :label="$t('common.phoneNumber')" :value="2" />
        </el-select>
        <el-input v-model="queryParams.input" :placeholder="$t('common.pleaseEnter')" clearable style="width: 240px" @keyup.enter="handleQuery" />
      </el-form-item>

      <el-form-item :label="$t('user.feedbackTime')" style="width: 308px">
        <el-date-picker v-model="queryParams.timeRanges" value-format="YYYY-MM-DD" type="daterange" range-separator="-" :start-placeholder="$t('common.startDate')" :end-placeholder="$t('common.endDate')">
        </el-date-picker>
      </el-form-item>

      <el-form-item :label="$t('common.state')">
        <el-select style="width: 150px" v-model="queryParams.status" class="m-2" :placeholder="$t('common.select')">
          <el-option :label="$t('user.pending')" :value="0" />
          <el-option :label="$t('user.processed')" :value="1" />
        </el-select>
      </el-form-item>

      <el-form-item :label="$t('user.feedbackType')">
        <el-select style="width: 150px" v-model="queryParams.feedbackType" class="m-2" :placeholder="$t('common.select')">
          <el-option :label="$t('user.appeal')" value="APPEAL" />
          <el-option :label="$t('user.advice')" value="ADVICE" />
          <el-option :label="$t('user.websiteCv')" value="WEBSITE_CV" />
          <el-option :label="$t('user.websiteConectus')" value="WEBSITE_CONTACTUS" />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">{{ $t('common.search') }}</el-button>
        <el-button icon="Refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <!-- 表格数据 -->
    <el-table v-loading="state.getListLoading" :data="state.tableData">
      <el-table-column :label="$t('common.userNickname')" prop="nickname" />
      <el-table-column :label="$t('common.phoneNumber')" prop="telephone" />
      <!-- <el-table-column :label="$t('common.userId')" prop="userId" /> -->
      <el-table-column :label="$t('user.feedbackContent')" prop="feedbackContent" show-overflow-tooltip />
      <el-table-column :label="$t('common.state')" prop="status">
        <template #default="scope">
          <span type="text" icon="Edit" v-if="scope.row.status === 0">{{$t('user.pending')}}</span>
          <span type="text" icon="Edit" v-if="scope.row.status === 1">{{$t('user.processed')}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('user.feedbackType')" prop="feedbackType">
        <template #default="scope">
          <span type="text" icon="Edit" v-if="scope.row.feedbackType === 'APPEAL'">{{$t('user.appeal')}}</span>
          <span type="text" icon="Edit" v-if="scope.row.feedbackType === 'ADVICE'">{{$t('user.advice')}}</span>
          <span type="text" icon="Edit" v-if="scope.row.feedbackType === 'WEBSITE_CV'">{{$t('user.websiteCv')}}</span>
          <span type="text" icon="Edit" v-if="scope.row.feedbackType === 'WEBSITE_CONTACTUS'">{{$t('user.websiteConectus')}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('user.feedbackTime')" prop="createTime" width="160px">
        <template #default="scope">
          <span>{{  parseTime(scope.row.createTime)}}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.operate')" align="center" minWidth="120px" fixed="right">
        <template #default="scope">
          <el-button type="text" @click="pass(scope.row)">{{ $t('common.details') }}</el-button>
          <template v-if="scope.row.status === 0">
            <el-button type="text" @click="reject(scope.row)">{{ $t('common.dealWith') }}</el-button>
            <el-button 
              v-if="!['WEBSITE_CV', 'WEBSITE_CONTACTUS'].includes(scope.row.feedbackType)"
              type="text" 
              @click="showReplyDialog(scope.row)"
            >{{ $t('msg.reply') }}</el-button>
          </template>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="state.total > 0" :total="state.total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />

    <el-dialog v-model="state.dialogVisible" title="" width="30%">
      <div class="flex-item">
        <span class="flex-label">{{ $t('user.feedbackContent') }}:</span>
        <span class="flex-value">{{state.content}}</span>
      </div>
      <div v-if="state.details.resultContent" class="flex-item">
        <span class="flex-label">{{ $t('mosAssist.replyContent') }}:</span>
        <span class="flex-value">{{state.details.resultContent}}</span>
      </div>
      <div style="margin-top:10px" v-if="state.picture!=null&&state.picture!=''">
        <a :href="state.picture" target="_blank" style="color: #409EFF; text-decoration: underline; cursor: pointer;">{{ state.picture }}</a>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <template v-if="state.details.status === 0">
            <el-button @click="reject(state.details)">{{ $t('common.dealWith') }}</el-button>
            <el-button 
              v-if="!['WEBSITE_CV', 'WEBSITE_CONTACTUS'].includes(state.details.feedbackType)"
              type="primary" 
              @click="showReplyDialog(state.details)"
            >{{ $t('msg.reply') }}</el-button>
          </template>
          <el-button v-else @click="state.dialogVisible = false">{{ $t('user.close') }}</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 处理对话框 -->
    <el-dialog v-model="state.dialogVisibleResult" :title="$t('user.sureToDeal')" width="30%">
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="state.dialogVisibleResult = false">{{ $t('common.cancel') }}</el-button>
          <el-button type="primary" @click="handleResult">{{ $t('common.dealWith') }}</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 回复对话框 -->
    <el-dialog v-model="state.dialogVisibleReply" :title="$t('user.feedbackContent')" width="420px">
      <div class="feedback-detail">
        <el-avatar :src="state.details.userHeadImg"></el-avatar>
        <div class="feedback-info">
          <div class="user-info">
            <span class="name overflow-ellipsis-1">{{ state.details.nickname }}</span>
            <span class="date">{{ parseTime(state.details.createTime, "{y}-{m}-{d} {h}:{i}:{s}") }}</span>
          </div>
          <div class="feedback-content">{{ state.details.feedbackContent }}</div>
        </div>
      </div>
      <el-form ref="replyFormRef" :model="replyForm" :rules="rules" class="reply-form" @submit.prevent>
        <el-form-item prop="resultContent">
          <el-input v-model="replyForm.resultContent" type="textarea" :placeholder="$t('common.pleaseEnterContent')" :autosize="{ minRows: 2, maxRows: 10 }" maxlength="1000" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="state.dialogVisibleReply = false">{{ $t('user.close') }}</el-button>
          <el-button type="primary" @click="reply">{{ $t('msg.reply') }}</el-button>
        </span>
      </template>
    </el-dialog>
  </div>

</template>

<script setup name="feedback">
import { nextTick, reactive, ref } from 'vue'
import {
  getFeedbackList,
  feedbackHandle,
  replyHandle
} from '@/api/feedback/feedback'
import { ElMessage } from 'element-plus'
import i18n from '../../i18n'

const queryFormRef = ref()
const resultFeedBack = ref({
  feedbackId: null,
  userId: null,
  resultContent: null
})

// 搜索参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  searchKey: 1,
  input: '',
  status: null,
  feedbackType: null,
  timeRanges: []
})

const state = reactive({
  showSearch: true,
  getListLoading: true,
  tableData: [],
  total: 0,
  selectedIds: [],
  dialogVisible: false,
  dialogVisibleResult: false,
  dialogVisibleReply: false,
  resultContent: null,
  dialogTitle: '',
  content: '',
  info: null,
  details: {},
  picture: null
})

function pass (feedback) {
  state.dialogVisible = true
  state.content = feedback.feedbackContent
  state.details = feedback
  state.picture = feedback.picture?.split(',')
  // getFeedbackDetail(feedbackId).then((response) => {
  //   state.content = response.data.feedbackContent
  //   state.details = response.data
  //   state.picture = response.data.picture.split(',')
  // })
}
function reject (feedback) {
  // state.dialogVisible = false
  // feedback.resultContent = '测试'
  // console.log(feedback)
  // feedbackHandle(feedback)
  //   .then((response) => {
  //     ElMessage.success('处理成功')
  //     getList()
  //   })
  //   .catch((err) => {
  //     ElMessage.error(err.message)
  //   })
  state.resultContent = null
  state.info = null
  resultFeedBack.value.id = feedback.id
  resultFeedBack.value.userId = feedback.userId
  state.dialogVisibleResult = true
}
function handleResult () {
  // resultFeedBack.value.resultContent = state.resultContent
  // if (!state.resultContent) {
  //   state.info = '请输入反馈处理结果'
  //   return false
  // } else if (state.resultContent.length > 50) {
  //   state.info = '请输入50字以内内容'
  //   return false
  // }
  feedbackHandle(resultFeedBack.value)
    .then((response) => {
      ElMessage.success(i18n.global.t('common.success'))
      getList()
    })
    .finally(
      () => ((state.dialogVisibleResult = false), (state.dialogVisible = false))
    )
}

// 获取列表数据
function getList () {
  state.getListLoading = true
  const params = {
    ...queryParams.value
  }
  if (params.timeRanges.length === 2) {
    params.timeRanges = [+new Date(queryParams.value.timeRanges[0]+' 00:00:00'),+new Date(queryParams.value.timeRanges[1]+' 23:59:59')]
  }
  getFeedbackList(params)
    .then((response) => {
      state.total = response.data.total
      state.tableData = response.data.records
    })
    .finally(() => (state.getListLoading = false))
}
// 查询数据
function handleQuery () {
  queryParams.value.pageNum = 1
  getList()
}
// 重置查询
function resetQuery () {
  // if (queryFormRef.value) {
  //   queryFormRef.value.resetFields()
  // }
  queryParams.value.input = ''
  queryParams.value.status = null
  queryParams.value.feedbackType = null
  queryParams.value.timeRanges = []
  handleQuery()
}
const validateContent = (rule, value, callback) => {
  if (!value || !value.trim()) {
    return callback(new Error(i18n.global.t('common.pleaseEnterContent')))
  }
  callback()
}
const rules = reactive({
  resultContent: [{ validator: validateContent, trigger: ['blur', 'change'] }]
})

const replyForm = ref({
  resultContent: ''
})
async function showReplyDialog (row) {
  replyForm.value.resultContent = ''
  state.dialogVisibleReply = true
  await nextTick()
  replyFormRef.value.resetFields()
  state.details = row
}
const replyFormRef = ref()

function reply () {
  replyFormRef.value.validate(async (valid) => {
    if (valid) {
      const params = {
        ...replyForm.value,
        feedbackId: state.details.id
      }
      console.log('🚀 ~ file: index.vue:238 ~ handleReply ~ params:', params)
      await replyHandle(params)
      state.dialogVisibleReply = false
      state.dialogVisible = false
      ElMessage.success(i18n.global.t('common.success'))
      getList()
    }
  })
}

getList()
</script>

<style lang="scss" scoped>
.flex-item {
  display: flex;
  & + .flex-item {
    margin-top: 10px;
  }
  .flex-label {
    flex-shrink: 0;
    margin-right: 10px;
    white-space: nowrap;
  }
  .flex-value {
    word-break: break-word;
    white-space: pre-wrap;
  }
}
.feedback-detail {
  display: flex;
  margin-bottom: 20px;
  .feedback-info {
    margin-left: 10px;
    overflow: hidden;
    flex: 1;
    .user-info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .name {
        font-size: 14px;
        font-weight: bold;
        color: #333;
      }
      .date {
        font-size: 12px;
        color: #999;
        white-space: nowrap;
        margin-left: 5px;
      }
    }
    .feedback-content {
      color: #666;
      margin-top: 10px;
      white-space: pre-wrap;
      word-break: break-word;
    }
  }
}
.reply-form {
  .el-form-item {
    margin-bottom: 0;
  }
}
</style>
