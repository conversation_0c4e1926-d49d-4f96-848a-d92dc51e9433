<template>
  <div class="app-container consumer-container">
    <el-form
      :model="queryParams"
      ref="queryFormRef"
      v-show="state.showSearch"
      :inline="true"
    >
      <el-form-item :label="$t('userStatistic.source')">
        <el-select
          style="width: 150px"
          v-model="queryParams.sourceType"
          clearable
          class="m-2"
          :placeholder="$t('userStatistic.selectSource')"
        >
          <el-option :label="$t('userStatistic.official')" :value="0" />
        </el-select>
      </el-form-item>

      <el-form-item :label="$t('userStatistic.period')" style="width: 308px">
        <el-date-picker
          v-model="queryParams.times"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          :start-placeholder="$t('common.startDate')"
          :end-placeholder="$t('common.endDate')"
        >
        </el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          > {{ $t('common.search') }}
        </el-button>
        <el-button icon="Refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <!--      <el-col :span="1.5">-->
      <!--        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['system:role:add']">新增-->
      <!--        </el-button>-->
      <!--      </el-col>-->
      <!-- <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="state.selectedIds.length !== 1" @click="handleDelete" v-hasPermi="['system:role:remove']">删除
        </el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['user:export']"
          > {{ $t('common.export') }}
        </el-button>
      </el-col>
      <right-toolbar
        v-model:showSearch="state.showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 表格数据 -->
    <el-table
      v-loading="state.getListLoading"
      :data="state.tableListMerge"
      :span-method="objectSpanMethod"
      @selection-change="handleSelectionChange"
    >
      <el-table-column :label="$t('userStatistic.date')" align="center" prop="recordTime"  >
        <template #default="scope">
          <span v-if="scope.row.recordTime === 'total'">{{ scope.row.recordTime }}</span>
          <span v-else>{{ parseTime(scope.row.recordTime, '{y}-{m}-{d}') }}</span>

        </template>
      </el-table-column>
      <el-table-column
        :label="$t('userStatistic.source')"
        prop="source"
        align="center"

      >
        <template #default="scope">
          <span v-if="scope.row.source === 1">{{ $t('userStatistic.unknown') }}</span>
          <span v-if="scope.row.source === 0">{{ $t('userStatistic.official') }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('userStatistic.sameDay')"
        prop="registerCount"
        align="center"
      >
      </el-table-column>

      <el-table-column
        :label="$t('userStatistic.nextDay')"
        prop="nextDay"
        align="center"
      >
      </el-table-column>

      <el-table-column
        :label="$t('userStatistic.the7thDay')"
        prop="sevenDay"
        align="center"
      >
      </el-table-column>

      <el-table-column
        :label="$t('userStatistic.the30thDay')"
        prop="thirtyDay"
        align="center"
      >
      </el-table-column>


    </el-table>

    <pagination
      v-show="state.total > 0"
      :total="state.total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改对话框 -->
    <el-dialog
      :title="state.dialogTitle"
      v-model="state.dialogVisible"
      width="500px"
      append-to-body
    >
      <el-form
        ref="formRef"
        :model="formParams"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="名">
          <el-input v-model="formParams.firstName" :placeholder="$t('common.pleaseEnter')" />
        </el-form-item>
        <el-form-item label="姓">
          <el-input v-model="formParams.lastName" :placeholder="$t('common.pleaseEnter')" />
        </el-form-item>
        <el-form-item :label="$t('common.phoneNumber')" prop="phone">
          <el-input v-model="formParams.phone" :placeholder="$t('common.pleaseEnter')" />
        </el-form-item>
        <el-form-item label="区号" prop="dialCode">
          <el-input v-model="formParams.dialCode" :placeholder="$t('common.pleaseEnter')" />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input
            type="password"
            v-model="formParams.password"
            :placeholder="$t('common.pleaseEnter')"
          />
        </el-form-item>
        <!-- <el-form-item label="性别">
          <el-radio-group v-model="formParams.sex">
            <el-radio :label="1">男</el-radio>
            <el-radio :label="2">女</el-radio>
            <el-radio :label="3">未知</el-radio>
          </el-radio-group>
        </el-form-item> -->
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">{{ $t('common.confirm') }}</el-button>
          <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 更多操作 对话框 -->
    <el-dialog
      v-if="state.moreDialogVisible"
      v-model="state.moreDialogVisible"
      :width="state.moreDialogWidth"
      append-to-body
      custom-class="el-dialog-no-header"
    >
      <ResetPassword
        v-if="state.moreDialogType === '修改密码'"
        :params="seleectedRowData"
        @close="closeMoreDialog"
      ></ResetPassword>
      <LoginRecord
        v-else-if="state.moreDialogType === '登录记录'"
        :params="seleectedRowData"
        @close="closeMoreDialog"
      ></LoginRecord>
      <Bill
        v-else-if="state.moreDialogType === '账单'"
        :params="seleectedRowData"
        @close="closeMoreDialog"
      ></Bill>
      <CustomerServer
        v-else-if="state.moreDialogType === '设置客服'"
        :params="seleectedRowData"
        @close="closeMoreDialog"
      ></CustomerServer>
      <SendMsg
        v-else-if="state.moreDialogType === '发送消息'"
        :params="seleectedRowData"
        @close="closeMoreDialog"
      ></SendMsg>
      <Freeze
        v-else-if="state.moreDialogType === '冻结'"
        :params="seleectedRowData"
        @close="closeMoreDialog"
      ></Freeze>
      <FriendList
        v-else-if="state.moreDialogType === '好友列表'"
        :params="seleectedRowData"
        @close="closeMoreDialog"
      ></FriendList>
    </el-dialog>
  </div>
</template>

<script setup name="Consumer">
import { reactive, ref, getCurrentInstance } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import ResetPassword from './components/resetPassword.vue'
import LoginRecord from './components/loginRecord.vue'
import FriendList from './components/friendList.vue'
import Bill from './components/bill.vue'
import SendMsg from './components/sendMsg.vue'
import CustomerServer from './components/setCustomerServer.vue'
import Freeze from './components/freeze.vue'
import { countryCode } from '@/utils/enum.js'

import {
  listUser,
  pageNotice,
  saveUser,
  artificialRecharge,
  userBan,
  userFreeze
} from '@/api/statistics/statistics'
import i18n from "../../i18n";

const { proxy } = getCurrentInstance()

const queryFormRef = ref()
const formRef = ref()

// 搜索参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  searchKey: 'nickName',
  searchValue: '',
  sourceType: null,
  nickname: null,
  phone: null,
  id: null,
  startTime: null,
  endTime: null,
  accountStatus: null,
  onLineState: null,
  isMember: null,
  times: []
})

const state = reactive({
  showSearch: true,
  getListLoading: true,
  tableData: [],
  tableDataLength: 0,
  total: 0,
  selectedIds: [],
  dialogVisible: false,
  dialogTitle: '',
  moreDialogTitle: '',
  moreDialogVisible: false,
  moreDialogType: '',
  moreDialogWidth: '500px'
})

const validateNickName = (rule, value, callback) => {
  if (value === '' || value === undefined) {
    callback(new Error(i18n.global.t('common.pleaseEnterContent')))
  } else {
    if (/^[\u4e00-\u9fa50-9A-Za-z.]{1,64}$/.test(value)) {
      return callback()
    }
    callback(new Error(i18n.global.t('common.charactersExceeds') + '64'))
  }
}

const validatePhone = (rule, value, callback) => {
  if (value === '') {
    callback(new Error(i18n.global.t('common.pleaseEnterContent')))
  } else {
    if (/\d+/.test(value)) {
      return callback()
    }
    callback(new Error(i18n.global.t('common.formatIncorrect')))
  }
}

const validateDialCode = (rule, value, callback) => {
  if (value === '' || value === undefined) {
    callback(new Error(i18n.global.t('common.pleaseEnterContent')))
  }
  return callback()
}

const validatePassword = (rule, value, callback) => {
  if (value === '') {
    callback(new Error(i18n.global.t('common.pleaseEnterContent')))
  } else {
    // if (
    //   /^(?=.*\d)(?=.*[a-zA-Z])(?=.*[~!@#$%^&*])[\da-zA-Z~!@#$%^&*]{8,}$/.test(
    //     value
    //   )
    // ) {
    return callback()
    // }
    // callback(new Error('请输入8位以上密码,包含数字,大小写字母,特殊符号'))
  }
}

// 新增用户参数
const formParams = ref({
  firstName: '',
  lastName: '',
  dialCode: '',
  phone: '',
  password: ''
  // sex: 1
})

// 充值/提现 金额参数/用户id
const rechargeParams = ref({
  userId: null,
  accountCode: null,
  amount: null
})

const formRules = reactive({
  // firstName: {
  //   validator: validateNickName,
  //   trigger: 'blur'
  // },
  // lastName: {
  //   validator: validateNickName,
  //   trigger: 'blur'
  // },

  dialCode: {
    validator: validateDialCode,
    trigger: 'blur'
  },

  phone: {
    validator: validatePhone,
    trigger: 'blur'
  },
  password: {
    validator: validatePassword,
    trigger: 'blur'
  }
})

// 获取列表数据
function getList () {
  state.getListLoading = true

  const [startTime, endTime] = queryParams.value.times || []
  queryParams.value.startTime =
    startTime === undefined ? null : +new Date(startTime + ' 00:00:00')
  queryParams.value.endTime =
    endTime === undefined ? null : +new Date(endTime + ' 23:59:59')
  pageNotice(queryParams.value)
    .then((res) => {
      // state.tableData = [...res.data.pageList.records, ...res.data.totalList]
      // state.tableDataLength = res.data.pageList.records.length
      // state.tableData.push()
      state.totalList = res.data.totalList.map(i => {
        i.recordTime = 'total'
        return i
      })
      state.tableData = res.data.pageList.records
      state.tableListMerge = [...state.tableData, ...state.totalList]
      console.log(state.tableListMerge)
      state.total = res.data.pageList.total
    })
    .finally(() => (state.getListLoading = false))
}

// 查询数据
function handleQuery () {
  queryParams.value.pageNum = 1
  getList()
}

function objectSpanMethod({ row, column, rowIndex, columnIndex }) {
  if (columnIndex === 0) {
    if (rowIndex > state.tableData?.length) {
      return {
        rowspan: state.totalList?.length,
        colspan: 1
      };
    } else {
      return {
        rowspan: 1,
        colspan: 1
      };
    }
  }
}
function nameTextStr (row) {
  const a1 = row.firstName ? row.firstName : ''
  const a2 = row.lastName ? row.lastName : ''
  const a3 = a1 && a2 ? '_' : ''
  return a1 + a3 + a2
}
// 重置查询
function resetQuery () {
  if (queryFormRef.value) {
    queryFormRef.value.resetFields()
  }
  queryParams.value.searchValue = ''
  queryParams.value.times = []
  queryParams.value.accountStatus = null
  queryParams.value.onLineState = null
  queryParams.value.isMember = null
  queryParams.value.searchKey = null
  queryParams.value.sourceType = null
  queryParams.value.appOnLineState = null
  queryParams.value.webOnLineState = null

  handleQuery()
}

// 封禁按钮操作
function handleDelete (row) {
  rechargeParams.value.userId = row
  ElMessageBox.confirm(i18n.global.t('common.confirmBanUser'), {
    confirmButtonText: i18n.global.t('common.confirm'),
    cancelButtonText: i18n.global.t('common.cancel'),
    type: 'warning'
  })
    .then(() => {
      userBan(rechargeParams.value).then((response) => {
        ElMessage({
          type: 'success',
          message: i18n.global.t('common.freezeMsg')
        })
        getList()
      })
    })
    .catch(() => {})
}

// 导出按钮操作
function handleExport () {
  const [startTime, endTime] = queryParams.value.times || []
  queryParams.value.startTime =
    startTime === undefined ? null : +new Date(startTime + ' 00:00:00')
  queryParams.value.endTime =
    endTime === undefined ? null : +new Date(endTime + ' 23:59:59')
  proxy.download(
    '/retention/export',
    {
      ...queryParams.value
    },
    `retention_${new Date().getTime()}.xlsx`
  )
}

// 多选框选中数据
function handleSelectionChange (selection) {
  state.selectedIds = selection.map((item) => item.id)
  console.log(state.selectedIds)
}

function resetForm () {
  formParams.value = {
    firstName: '',
    lastName: '',
    dialCode: '',
    phone: '',
    password: ''
  }
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

function handleAdd () {
  resetForm()
  state.dialogTitle = '新增用户'
  state.dialogVisible = true
}

// 取消按钮
function cancel () {
  state.dialogVisible = false
}

// 提交
async function submitForm () {
  if (!formRef.value) return
  formRef.value.validate((valid, fields) => {
    console.log(valid)
    if (valid) {
      saveUser(formParams.value).then((response) => {
        if (response.code === 200) {
          ElMessage.success(i18n.global.t('common.success'))
          state.dialogVisible = false
          getList()
        }
      })
    }
  })
}

function closeMoreDialog (type) {
  state.moreDialogVisible = false
  state.moreDialogType = null
  if (type === 'init') {
    getList()
  }
}

function countryCodeText (row) {
  const list = countryCode.filter((i) => i.tel === row.dialCode)
  let txt = ''
  if (list.length) {
    txt=i18n.global.locale === 'en' ? list[0].en : list[0].name
  }
  return txt
}

// 重置密码对话框
const seleectedRowData = ref({
  userId: null,
  nickname: null
})

function showResetPswDialog (userId) {
  seleectedRowData.value.userId = userId
  state.moreDialogType = '修改密码'
  state.moreDialogWidth = '500px'
  state.moreDialogVisible = true
}

// 更多操作
function handleMore (command, info) {
  switch (command) {
    case 'loginRecord':
      showLoginRecord(info.id)
      break
    case 'bill':
      showBill(info)
      break
    case 'setAsService':
      setAsService(info.id)
      break
    case 'sendMsg':
      showSendMsg(info.id)
      break
    default:
      break
  }
}

// 登录记录
function showLoginRecord (userId) {
  seleectedRowData.value.userId = userId
  state.moreDialogWidth = '1000px'
  state.moreDialogType = '登录记录'
  state.moreDialogVisible = true
}

// 好友列表
function showFriendsList (userId) {
  seleectedRowData.value.id = userId
  state.moreDialogWidth = '1200px'
  state.moreDialogType = '好友列表'
  state.moreDialogVisible = true
}




function showBill (info) {
  seleectedRowData.value.userId = info.id
  seleectedRowData.value.nickname = info.nickname
  state.moreDialogWidth = '1000px'
  state.moreDialogType = '账单'
  state.moreDialogVisible = true
}

function setAsService (userid) {
  seleectedRowData.value.userId = userid
  console.log(seleectedRowData.value.userId)
  state.moreDialogWidth = '600px'
  state.moreDialogType = '设置客服'
  state.moreDialogVisible = true
}

function showSendMsg (userid) {
  seleectedRowData.value.userId = userid
  state.moreDialogWidth = '600px'
  state.moreDialogType = '发送消息'
  state.moreDialogVisible = true
}

function showFreeze (userid) {
  seleectedRowData.value.userId = userid
  state.moreDialogWidth = '450px'
  state.moreDialogType = '冻结'
  state.moreDialogVisible = true
}

function showUnFreeze (userid) {
  ElMessageBox.confirm(i18n.global.t('common.confirmUnfrozen'), i18n.global.t('common.tips'), {
    confirmButtonText: i18n.global.t('common.confirm'),
    cancelButtonText: i18n.global.t('common.cancel'),
    type: 'warning'
  })
    .then(() => {
      const params = {
        freezeReason: 0,
        isFreeze: false,
        targetId: userid,
        time: 0,
        unit: ''
      }
      userFreeze(params).then((response) => {
        ElMessage({
          type: 'success',
          message: i18n.global.t('common.unfreezeMsg')
        })
        getList()
      })
    })
    .catch(() => {
      // ElMessage.error({
      //   message: '操作失败！'
      // })
    })
}

getList()
</script>
<style>
.consumer-container .el-table .el-table__cell {
  z-index: unset;
}
.el-dialog-no-header .el-table .el-table__cell {
  z-index: unset !important;
}
</style>
