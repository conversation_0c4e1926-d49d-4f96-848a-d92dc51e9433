<template>
  <div class="bill">
    <el-form :model="queryParams" ref="queryFormRef" v-show="state.showSearch" :inline="true">
      <el-form-item label="账单类型">
        <el-select style="width: 150px" v-model="queryParams.tradeType" class="m-2" :placeholder="$t('common.select')">
          <el-option v-for="item in billType" :key="item.label" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>

      <el-form-item :label="$t('wallet.transactionTime')" style="width: 308px">
        <el-date-picker v-model="queryParams.timeRanges" value-format="YYYY-MM-DD" type="daterange" range-separator="-" :start-placeholder="$t('common.startDate')" :end-placeholder="$t('common.endDate')">
        </el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">{{ $t('common.search') }}</el-button>
        <el-button icon="Refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <!-- 表格数据 -->
    <h3 class="title">用户{{state.nickname}}的账单列表：</h3>
    <el-table v-loading="state.getListLoading" :data="state.tableData">
      <el-table-column label="交易流水号" prop="recordSn" width="120" />
      <el-table-column label="金额" prop="changeAmount" :show-overflow-tooltip="true" width="150" />
      <el-table-column label="手续费" :show-overflow-tooltip="true" width="150">
        <template #default="scope">
          <span v-if="scope.row.changeAmount===scope.row.actualAmount">/</span>
          <span v-else>{{(scope.row.changeAmount-scope.row.actualAmount).toFixed(3)}}</span>
        </template>
      </el-table-column>
      <el-table-column label="实际金额" prop="actualAmount" width="100" />
      <el-table-column label="账户余额" align="center" prop="remainingSum" width="100" />
      <el-table-column :label="$t('wallet.transactionTime')" align="center" prop="createTime">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="账单类型" prop="tradeType" align="center" width="100">
        <template #default="scope">
          <span v-if="scope.row.tradeType === 1">转账</span>
          <span v-if="scope.row.tradeType === 2">红包</span>
          <span v-if="scope.row.tradeType === 3">余额提现</span>
          <span v-if="scope.row.tradeType === 4">余额充值</span>
          <span v-if="scope.row.tradeType === 5">钻石充值</span>
          <span v-if="scope.row.tradeType === 6">交易时间</span>
          <span v-if="scope.row.tradeType === 7">手动充值</span>
          <span v-if="scope.row.tradeType === 8">手动提现</span>
          <span v-if="scope.row.tradeType === 9">红包领取</span>
          <span v-if="scope.row.tradeType === 10">红包退回</span>
          <span v-if="scope.row.tradeType === 11">充值会员</span>
          <span v-if="scope.row.tradeType === 12">转账退回</span>
          <span v-if="scope.row.tradeType === 13">提现审核驳回</span>
          <span v-if="scope.row.tradeType === 14">钻石兑换余额</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="state.total > 0" :total="state.total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
  </div>
</template>

<script setup name="Bill">
import { reactive, ref } from 'vue'
import { listBill } from '@/api/consumer/imuser'

const queryFormRef = ref()

const props = defineProps({
  params: Object
})

const state = reactive({
  showSearch: true,
  getListLoading: true,
  tableData: [],
  total: 0,
  selectedIds: [],
  dialogVisible: false,
  dialogTitle: '',
  nickname: props.params.nickname,
  userId: props.params.userId
})

// 搜索参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  tradeType: '',
  timeRanges: [],
  accountCode: state.userId,
  startTime: null,
  endTime: null
})

const billType = ref([
  { label: '转账', value: '1' },
  { label: '红包', value: '2' },
  { label: '余额提现', value: '3' },
  { label: '余额充值', value: '4' },
  { label: '钻石充值', value: '5' },
  { label: '交易', value: '6' },
  { label: '手动充值', value: '7' },
  { label: '手动提现', value: '8' },
  { label: '红包领取', value: '9' },
  { label: '红包退回', value: '10' },
  { label: '充值会员', value: '11' },
  { label: '转账退回', value: '12' },
  { label: '提现审核驳回', value: '13' },
  { label: '钻石兑换余额', value: '14' }
])

// 获取列表数据
function getList () {
  const { timeRanges } = queryParams.value
  const startTime =
    timeRanges[0] === undefined ? null : timeRanges[0] + ' 00:00:00'
  const endTime =
    timeRanges[1] === undefined ? null : timeRanges[1] + ' 23:59:59'
  queryParams.value.startTime = startTime
  queryParams.value.endTime = endTime
  console.log(queryParams.value)
  state.getListLoading = true
  listBill(queryParams.value)
    .then((response) => {
      if (response.code === 200) {
        state.tableData = response.data.records
        state.total = response.data.total
      }
    })
    .finally(() => (state.getListLoading = false))
}
// 查询数据
function handleQuery () {
  queryParams.value.pageNum = 1
  getList()
}
// 重置查询
function resetQuery () {
  if (queryFormRef.value) {
    queryFormRef.value.resetFields()
  }
  queryParams.value.tradeType = null
  queryParams.value.timeRanges = []
  handleQuery()
}

getList()
</script>

<style lang="scss" scoped>
.bill {
  position: relative;
  .title {
    font-size: 18px;
    margin-bottom: 15px;
  }
}
</style>
