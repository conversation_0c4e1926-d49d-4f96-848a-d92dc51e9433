<template>
  <div>
    <h3 class="dialog-title">更多设置</h3>
    <el-form ref="formRef" :rules="rules">
      <el-form-item label="设为客服">
        <el-radio-group v-model="formParams.isService">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <div class="footer-options">
      <el-button :loading="state.submitLoading" type="primary" @click="submitForm">{{ $t('common.confirm') }}</el-button>
      <el-button @click="emit('close')">{{ $t('common.cancel') }}</el-button>
    </div>
  </div>
</template>

<script setup name="resetPassword">
import { reactive, ref, defineProps, defineEmits } from 'vue'
import { ElMessage } from 'element-plus'
import { modifyCusService, selectUserExpand } from '@/api/consumer/imuser'

const props = defineProps({
  params: Object
})

const emit = defineEmits(['close'])

const formRef = ref()

const rules = reactive({})
const formParams = reactive({
  userId: props.params.userId,
  isService: null
})
const state = reactive({
  submitLoading: false
})

function getInfo () {
  selectUserExpand(formParams).then((response) => {
    if (response.isService != null) {
      formParams.isService = response.isService
    } else {
      formParams.isService = 0
    }
  })
}

// 提交表单
function submitForm () {
  state.submitLoading = true
  modifyCusService(formParams)
    .then((response) => {
      ElMessage.success(i18n.global.t('common.success'))
    })
    .finally(() => {
      state.submitLoading = false
      emit('close')
    })
}
getInfo()
</script>

<style lang="scss" scoped>
.footer-options {
  text-align: right;
}
</style>
