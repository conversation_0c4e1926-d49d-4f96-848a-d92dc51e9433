<template>
  <div>
    <h3 class="dialog-title">冻结</h3>
    <el-form ref="formRef" :model="formParams" :rules="rules">
      <el-form-item label="冻结时间" prop="days">
        <el-select v-model="formParams.days" :placeholder="$t('common.select')" @change="changeFreezeTime">
          <el-option
            v-for="item in state.timeOptions"
            :key="item.type"
            :label="item.label"
            :value="item.type">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="冻结原因" prop="freezeReason">
        <el-select v-model="formParams.freezeReason" :placeholder="$t('common.select')">
          <el-option
            v-for="item in state.reasonOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <div class="footer-options">
      <el-button @click="emit('close')">{{ $t('common.cancel') }}</el-button>
      <el-button :loading="state.submitLoading" type="primary" @click="submitHandler">{{ $t('common.confirm') }}</el-button>
    </div>
  </div>
</template>

<script setup name="freeze">
// 修改密码

import { reactive, ref, defineProps, defineEmits } from 'vue'
import { ElMessage } from 'element-plus'
import { userFreeze, groupFreeze } from '@/api/consumer/imuser'
import i18n from "../../../i18n";

const props = defineProps({
  params: Object,
  from: String
})

const emit = defineEmits(['close'])

const formRef = ref()

const rules = reactive({
  days: [{ required: true, message: i18n.global.t('common.cannotBeEmpty'), trigger: 'change' }],
  freezeReason: [{ required: true, message: i18n.global.t('common.cannotBeEmpty'), trigger: 'change' }]
})
const formParams = reactive({
  targetId: props.params.userId,
  isFreeze: true,
  time: 0,
  unit: '',
  freezeReason: '',
  freezeTime: '',
  days: ''
})
const state = reactive({
  submitLoading: false,
  timeOptions: [
    { label: '3小时', type: 1, value: 3, unit: 'hours' },
    { label: '12小时', type: 2, value: 12, unit: 'hours' },
    { label: '1天', type: 3, value: 1, unit: 'day' },
    { label: '3天', type: 4, value: 3, unit: 'day' },
    { label: '1周', type: 5, value: 1, unit: 'week' },
    { label: '永久', type: 6, value: '', unit: 'forever' }
  ],
  reasonOptions: [
    { label: '骚扰信息', value: 1 },
    { label: '不当言论', value: 2 },
    { label: '违反条款', value: 3 },
    { label: '色情暴力', value: 4 },
    { label: '其他', value: 5 }
  ]
})

const changeFreezeTime = (val) => {
  console.log(val)
  const obj = state.timeOptions.filter(i => i.type === val)[0]
  formParams.time = obj.value
  formParams.unit = obj.unit
}
function submitHandler () {
  if (!formRef.value) return
  formRef.value.validate((valid, fields) => {
    if (valid) {
      let requestMethod = userFreeze
      if (props.from === 'group') {
        requestMethod = groupFreeze
      }
      requestMethod(formParams).then((response) => {
        if (response.code === 200) {
          ElMessage.success(i18n.global.t('common.success'))
          emit('close', 'init')
        }
      })
    }
  })
}
</script>

<style lang="scss" scoped>
.footer-options {
  text-align: right;
}
</style>
