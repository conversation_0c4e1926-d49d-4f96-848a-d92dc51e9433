<template>
  <div class="login-record">
    <h3 class="dialog-title">好友列表</h3>
    <el-form :model="queryParams" ref="queryFormRef" :inline="true">
      <el-form-item label="">
        <el-select
          style="width: 100px"
          v-model="queryParams.searchKey"
          clearable
          class="m-2"
          :placeholder="$t('common.select')"
        >
          <el-option label="昵称" value="nickName" />
          <el-option :label="$t('common.phoneNumber')" value="phone" />
        </el-select>
        <el-input
          v-model="queryParams.searchValue"
          :placeholder="$t('common.pleaseEnter')"
          clearable
          style="width: 240px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>

      <el-form-item :label="$t('common.state')">
        <el-select
          style="width: 150px"
          v-model="queryParams.status"
          clearable
          class="m-2"
          :placeholder="$t('common.pleaseSelectState')"
        >
          <el-option label="正常" :value="0" />
          <el-option label="冻结" :value="1" />
          <el-option label="注销" :value="2" />
        </el-select>
      </el-form-item>

      <el-form-item label="APP在线状态">
        <el-select
          style="width: 150px"
          v-model="queryParams.appOnLineState"
          clearable
          class="m-2"
          :placeholder="$t('common.select')"
        >
          <el-option label="在线" :value="1" />
          <el-option label="离线" :value="0" />
        </el-select>
      </el-form-item>
      <el-form-item label="PC在线状态">
        <el-select
          style="width: 150px"
          v-model="queryParams.webOnLineState"
          clearable
          class="m-2"
          :placeholder="$t('common.select')"
        >
          <el-option label="在线" :value="1" />
          <el-option label="离线" :value="0" />
        </el-select>
      </el-form-item>
      <el-form-item label="注册时间" style="width: 308px">
        <el-date-picker
          v-model="queryParams.timeRanges"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          :start-placeholder="$t('common.startDate')"
          :end-placeholder="$t('common.endDate')"
        >
        </el-date-picker>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >{{ $t("common.search") }}
        </el-button>
        <el-button icon="Refresh" @click="resetQuery">{{
          $t("common.reset")
        }}</el-button>
      </el-form-item>
    </el-form>

    <el-table
      :data="state.tableData"
      v-loading="state.getListLoading"
      style="width: 100%"
    >
      <el-table-column
        label="昵称"
        prop="nickname"
        align="center"
        width="100"
      />
      <el-table-column
        :label="$t('common.phoneNumber')"
        prop="phone"
        :show-overflow-tooltip="true"
        width="150"
      >
        <template #default="scope">
          <template v-if="scope.row.dialCode && scope.row.phone">
            <span v-if="scope.row.phone.includes('_')">{{
              `+${scope.row.dialCode} ${scope.row.phone.split("_")[1]}`
            }}</span>
            <span v-else>{{
              `+${scope.row.dialCode} ${scope.row.phone}`
            }}</span>
          </template>
        </template>
      </el-table-column>
      <!--      <el-table-column :label="$t('common.userId')" prop="id" :show-overflow-tooltip="true" width="100" />-->
      <el-table-column label="所属国家" prop="dialCode" width="100">
        <template #default="scope">
          <span>{{
            scope.row.dialCode ? countryCodeText(scope.row) : ""
          }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="姓名" prop="name" width="100">
        <template #default="scope">
          <span>{{ nameTextStr(scope.row) }}</span>
        </template>
      </el-table-column> -->
      <el-table-column label="头像" prop="headPortrait" width="150">
        <template #default="scope">
          <el-image
            style="width: 60px; height: 60px"
            :src="scope.row.headPortrait"
            :zoom-rate="1.2"
            :preview-src-list="[scope.row.headPortrait]"
            :initial-index="4"
            fit="cover"
          />
        </template>
      </el-table-column>
      <el-table-column label="注册时间" align="center" prop="createTime">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="APP在线状态"
        prop="appOnLineState"
        align="center"
        width="100"
      >
        <template #default="scope">
          <span v-if="scope.row.appOnLineState === 1">在线</span>
          <span v-if="scope.row.appOnLineState === 0">离线</span>
        </template>
      </el-table-column>
      <el-table-column
        label="PC在线状态"
        prop="webOnLineState"
        align="center"
        width="100"
      >
        <template #default="scope">
          <span v-if="scope.row.webOnLineState === 1">在线</span>
          <span v-if="scope.row.webOnLineState === 0">离线</span>
        </template>
      </el-table-column>
      <el-table-column
        label="最后上线时间"
        prop="lastUpTime"
        align="center"
        width="100"
      />
      <el-table-column
        label="被举报次数"
        prop="beReportCount"
        align="center"
        width="100"
      />
      <el-table-column
        :label="$t('common.state')"
        prop="status"
        align="center"
        width="100"
      >
        <template #default="scope">
          <span v-if="scope.row.status === 0">正常</span>
          <span v-if="scope.row.status === 1">冻结</span>
          <span style="color: red" v-if="scope.row.status === 2">注销</span>
        </template>
      </el-table-column>
    </el-table>

    <div>
      <pagination
        v-show="state.total > 0"
        :total="state.total"
        v-model:page="state.pageNum"
        v-model:limit="state.pageSize"
        @pagination="getList"
      />
    </div>
  </div>
</template>

<script setup name="loginRecord">
import { reactive, ref } from "vue";
import { getFriend } from "@/api/consumer/imuser";
import { countryCode } from "@/utils/enum.js";

const props = defineProps({
  params: Object,
});

const queryParams = ref({
  searchKey: "nickName",
  searchValue: "",
  nickname: null,
  phone: null,
  startTime: null,
  endTime: null,
  accountStatus: null,
  onLineState: null,
  isMember: null,
  timeRanges: [],
});

// 好友列表
const state = reactive({
  tableData: [],
  getListLoading: false,
  total: 100,
  pageNum: 1,
  pageSize: 10,
  id: props.params.id,
});

// 查询数据
function handleQuery() {
  getList();
}
const queryFormRef = ref();
// 重置查询
function resetQuery() {
  if (queryFormRef.value) {
    queryFormRef.value.resetFields();
  }
  queryParams.value.searchValue = "";
  queryParams.value.timeRanges = [];
  queryParams.value.accountStatus = null;
  queryParams.value.onLineState = null;
  queryParams.value.isMember = null;
  queryParams.value.searchKey = null;
  queryParams.value.status = null;
  queryParams.value.appOnLineState = null;
  queryParams.value.webOnLineState = null;

  handleQuery();
}

function countryCodeText(row) {
  const list = countryCode.filter((i) => i.tel === row.dialCode);
  let txt = "";
  if (list.length) {
    txt = i18n.global.locale === "en" ? list[0].en : list[0].name;
  }
  return txt;
}

function getList() {
  state.getListLoading = true;

  if (queryParams.value.searchKey === "nickName") {
    queryParams.value.nickname = queryParams.value.searchValue;
    queryParams.value.phone = null;
  }
  if (queryParams.value.searchKey === "phone") {
    queryParams.value.nickname = null;
    queryParams.value.phone = queryParams.value.searchValue;
  }
  console.log(queryParams.value.searchValue);
  if (queryParams.value.searchValue === "") {
    queryParams.value.nickname = null;
    queryParams.value.phone = null;
  }
  const [startTime, endTime] = queryParams.value.timeRanges || [];
  queryParams.value.startTime =
    startTime === undefined ? null : startTime + " 00:00:00";
  queryParams.value.endTime =
    endTime === undefined ? null : endTime + " 23:59:59";

  const params = {
    ...state,
    ...queryParams.value,
  };
  if (params.tableData) delete params.tableData;
  getFriend(params).then((response) => {
    state.tableData = response.data.records;
    state.total = response.data.total;
  });
  state.getListLoading = false;
}
function nameTextStr(row) {
  const a1 = row.firstName ? row.firstName : "";
  const a2 = row.lastName ? row.lastName : "";
  const a3 = a1 && a2 ? "_" : "";
  return a1 + a3 + a2;
}
getList();
</script>

<style lang="scss" scoped>
.login-record {
  position: relative;
  min-height: 500px;
  .footer-options {
    text-align: right;
  }
}
</style>
