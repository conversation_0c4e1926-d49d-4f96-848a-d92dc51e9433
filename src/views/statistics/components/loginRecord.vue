<template>
  <div class="login-record">
    <h3 class="dialog-title">登录记录</h3>

    <!-- 功能区 -->
    <!-- <el-row style="height: 30px" :gutter="10" class="mb8"> -->
    <!-- <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="state.selectedIds.length === 0" @click="handleDelete" v-hasPermi="['system:role:remove']">删除</el-button>
      </el-col> -->
    <!-- <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['system:role:export']">{{$t('common.export')}}</el-button>
      </el-col> -->
    <!-- <right-toolbar v-model:showSearch="state.showSearch" @queryTable="getList"></right-toolbar> -->
    <!-- </el-row> -->
    <el-table :data="state.tableData" v-loading="state.getListLoading" style="width: 100%">
      <el-table-column prop="onTime" label="上线时间" align="center" />
      <el-table-column prop="offTime" label="下线时间" align="center" />
      <el-table-column prop="lastActiveIp" label="登录IP" align="center" />
      <el-table-column prop="loginAddress" label="登录IP归属地" align="center" />
      <el-table-column prop="deviceInfo" label="登录设备" align="center" />
      <el-table-column prop="deviceVersion" label="当前系统版本" align="center" />
      <el-table-column prop="mosVersion" label="当前mos版本号" align="center" />
    </el-table>

    <div>
      <pagination v-show="state.total > 0" :total="state.total" v-model:page="state.pageNum" v-model:limit="state.pageSize" @pagination="getList" />
    </div>
  </div>
</template>

<script setup name="loginRecord">
import { reactive } from 'vue'
import { loginHistory } from '@/api/consumer/imuser'

const props = defineProps({
  params: Object
})

// 登录记录
const state = reactive({
  tableData: [],
  getListLoading: false,
  total: 100,
  pageNum: 1,
  pageSize: 10,
  userId: props.params.userId
})

function getList () {
  state.getListLoading = true
  loginHistory(state).then((response) => {
    state.tableData = response.data.records
    state.total = response.data.total
  })
  state.getListLoading = false
}
getList()
</script>

<style lang="scss" scoped>
.login-record {
  position: relative;
  min-height: 500px;
  .footer-options {
    text-align: right;
  }
}
</style>
