<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="通知标题" prop="title">
        <el-input
          v-model="queryParams.title"
          :placeholder="$t('common.pleaseEnter')"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >{{ $t('common.search') }}</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">{{ $t('common.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd"
          >{{ $t('common.add') }}</el-button
        >
      </el-col>

      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      class="table"
      v-loading="loading"
      :data="postList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column :label="$t('common.caption')" align="center" prop="title" />
      <el-table-column label="封面图" align="center" prop="cover">
        <template #default="scope">
          <el-image
            style="width: 100px; height: 60px"
            :src="scope.row.cover"
            fit="contain"
            :preview-src-list="[scope.row.cover]"
            :preview-teleported="true"
          />
        </template>
      </el-table-column>
      <el-table-column label="发布状态" align="center" prop="publishStatus">
        <template #default="scope">
          <span style="color: red" v-if="scope.row.publishStatus == 0"
            >未发布</span
          >
          <span v-if="scope.row.publishStatus == 1">已发布</span>
        </template>
      </el-table-column>
      <el-table-column
        label="发布时间"
        align="center"
        prop="publishDate"
        width="180"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.publishDate) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        :label="$t('common.operate')"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            type="text"
            icon="el-icon-edit"
            @click="publish(scope.row)"
            v-if="scope.row.publishStatus == 0"
            >{{ $t('common.post') }}
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            >{{ $t('common.edit') }}
          </el-button>
          <el-button type="text" icon="Delete" @click="handleDelete(scope.row)"
            >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改对话框 -->
    <el-dialog
      :title="title"
      v-model="open"
      width="1000px"
      append-to-body
      @opened="createEditor"
    >
      <el-form
        ref="postRef"
        :model="form"
        :rules="rules"
        label-width="80px"
        v-if="open"
      >
        <el-form-item :label="$t('common.caption')" prop="title">
          <el-input v-model="form.title" :placeholder="$t('common.pleaseEnter')" />
        </el-form-item>
        <el-form-item label="封面图" prop="cover">
          <el-upload class="image-uploader" :headers="headers" :action="uploadUrl" :before-upload="beforeAvatarUpload" :show-file-list="false" :on-success="handleUpload"
                      :on-remove="handleRemove">
            <el-image style="width: 160px; height: 160px" class="image" v-if="form.cover!=null" :src="form.cover"></el-image>
          <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
          </el-upload>
          <span style="color:red"> {{data.info}}</span>
        </el-form-item>
        <el-form-item label="内容" prop="content">
          <div id="editor" style="width: 100%; height: 300px"></div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">{{ $t('common.confirm') }}</el-button>
          <el-button @click="cancel">{{ $t('common.cancel') }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="serviceNumber">
import { ref, reactive, toRefs, getCurrentInstance } from 'vue'
import {
  listNumber,
  addNumber,
  updateNumber,
  getNumber,
  editStatus,
  delNumber
} from '@/api/serviceNumber/number'
import { ElMessage, ElMessageBox } from 'element-plus'

import Quill from 'quill'
import 'quill/dist/quill.snow.css'
import { getToken } from '@/utils/auth'
import i18n from '../../i18n'

let cEditor

const createEditor = () => {
  const toolbarOptions = [
    ['bold', 'italic', 'underline', 'strike'], // toggled buttons
    ['blockquote', 'code-block'],
    [{ header: 1 }, { header: 2 }], // custom button values
    [{ list: 'ordered' }, { list: 'bullet' }],
    [{ script: 'sub' }, { script: 'super' }], // superscript/subscript
    [{ indent: '-1' }, { indent: '+1' }], // outdent/indent
    [{ direction: 'rtl' }], // text direction
    [{ size: ['small', false, 'large', 'huge'] }], // custom dropdown
    [{ header: [1, 2, 3, 4, 5, 6, false] }],
    [{ color: [] }, { background: [] }], // dropdown with defaults from theme
    [{ align: [] }],
    ['clean'] // remove formatting button
  ]
  cEditor = new Quill('#editor', {
    theme: 'snow',
    modules: {
      toolbar: toolbarOptions
    },
    placeholder: i18n.global.t('common.pleaseEnter')
  })
}

const { proxy } = getCurrentInstance()

const postList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref('')
const addBannerFormRef = ref()
const uploadUrl = import.meta.env.VITE_APP_UPLOAD_BASEURL
const headers = {
  Authorization: 'Bearer ' + getToken()
}

const data = reactive({
  info: null,
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    title: undefined,
    name: undefined,
    status: undefined
  },
  rules: {
    title: [
      { required: true, message: i18n.global.t('common.cannotBeEmpty'), trigger: 'blur' },
      { min: 1, max: 30, message: i18n.global.t('common.charactersExceeds') + '30', trigger: 'blur' }
    ],
    cover: [{ required: true, message: i18n.global.t('common.cannotBeEmpty'), trigger: 'blur' }]
  }
})

const { queryParams, form, rules } = toRefs(data)

// 文件上传
function handleUpload (url) {
  if (uploadUrl.indexOf('/dev-api')) {
    form.value.cover = 'http://' + url
  } else if (uploadUrl.indexOf('https')) {
    form.value.cover = 'https://' + url
  }
  data.info = null
  addBannerFormRef.value.validate(() => {})
}

function handleRemove () {
  form.value.headImg = null
}

function beforeAvatarUpload (rawFile) {
  if (['image/jpeg', 'image/png', 'image/bmp'].includes(rawFile.type)) {
    if (rawFile.size / 1024 / 1024 > 5) {
      data.info = '图片大小不能超过5M'
      return false
    }
    return true
  } else {
    data.info = i18n.global.t('common.formatIncorrect')
    return false
  }
}

/** 查询职位列表 */
function getList () {
  loading.value = true
  listNumber(queryParams.value).then((response) => {
    postList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}
/** 取消按钮 */
function cancel () {
  open.value = false
  reset()
}
/** 表单重置 */
function reset () {
  form.value = {
    title: undefined,
    cover: undefined,
    content: undefined
  }
  proxy.resetForm('postRef')
}
/** 搜索按钮操作 */
function handleQuery () {
  queryParams.value.pageNum = 1
  getList()
}
/** 重置按钮操作 */
function resetQuery () {
  proxy.resetForm('queryRef')
  handleQuery()
}
/** 多选框选中数据 */
function handleSelectionChange (selection) {
  ids.value = selection.map((item) => item.id)
  single.value = selection.length !== 1
  multiple.value = !selection.length
}
/** 新增按钮操作 */
function handleAdd () {
  reset()
  data.info = null
  addBannerFormRef.value && addBannerFormRef.value.resetFields()
  open.value = true
  title.value = '添加服务号消息'
}
/** 修改按钮操作 */
function handleUpdate (row) {
  reset()
  data.info = null
  addBannerFormRef.value && addBannerFormRef.value.resetFields()
  open.value = true
  title.value = '修改服务号消息'
  setTimeout(() => {
    getNumber(row.id).then((response) => {
      form.value = response.data
      cEditor.root.innerHTML = form.value.content
    })
  }, 500)
}

// 发布公告
function publish (row) {
  ElMessageBox.confirm(i18n.global.t('common.post'), {
    confirmButtonText: i18n.global.t('common.confirm'),
    cancelButtonText: i18n.global.t('common.cancel'),
    type: 'warning'
  }).then(() => {
    editStatus(row.id).then((response) => {
      proxy.$modal.msgSuccess(i18n.global.t('common.success'))
      getList()
    })
  })
}

/** 提交按钮 */
function submitForm () {
  proxy.$refs.postRef.validate((valid) => {
    console.log(cEditor.getLength())
    if (cEditor.getLength() === 1) {
      ElMessage({
        type: 'warning',
        message: i18n.global.t('common.pleaseEnterContent')
      })
      return
    }
    if (valid) {
      form.value.content = cEditor.root.innerHTML
      if (form.value.id !== undefined) {
        updateNumber(form.value).then((response) => {
          proxy.$modal.msgSuccess(i18n.global.t('common.success'))
          open.value = false
          getList()
        })
      } else {
        addNumber(form.value).then((response) => {
          proxy.$modal.msgSuccess(i18n.global.t('common.success'))
          open.value = false
          getList()
        })
      }
    }
  })
}
/** 删除按钮操作 */
function handleDelete (row) {
  console.log(row)
  proxy.$modal
    .confirm(i18n.global.t('system.confirmDelete') + row.title)
    .then(function () {
      return delNumber(row.id)
    })
    .then(() => {
      getList()
      proxy.$modal.msgSuccess(i18n.global.t('common.success'))
    })
    .catch(() => {})
}
getList()
</script>

<style scoped lang="scss">
.table {
  :deep(.el-table__cell) {
    //解决层级穿透 使得 子元素不与其产生新的层叠关系
    position: static;
  }
}
.image-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);

  &:hover {
    border-color: var(--el-color-primary);
  }
  :deep(.el-upload) {
    display: block;
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 100px;
    height: 100px;
    text-align: center;
  }

  .image {
    width: 150px;
    height: 100px;
    display: block;
  }
}
</style>
