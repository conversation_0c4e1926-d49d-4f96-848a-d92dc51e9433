<template>
  <div class="app-container consumer-container">
    <el-form :model="queryParams" ref="queryFormRef" v-show="state.showSearch" :inline="true">
      <el-form-item :label="$t('marking.activityName')">
        <el-input v-model="queryParams.activityName" :placeholder="$t('userManage.pleaseEnter')" clearable
          style="width: 240px" @keyup.enter="handleQuery" />
      </el-form-item>

      <el-form-item :label="$t('marking.activityType')">
        <el-select style="width: 150px" v-model="queryParams.activityType" clearable class="m-2"
          :placeholder="$t('common.select')">
          <el-option v-for="item in activityTypeEnum" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('marking.activityStatus')">
        <el-select style="width: 150px" v-model="queryParams.activityStatus" clearable class="m-2"
          :placeholder="$t('common.select')">
          <el-option v-for="item in activityStatusEnum" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">{{ $t("common.search") }}
        </el-button>
        <el-button icon="Refresh" @click="resetQuery">{{
          $t("common.reset")
          }}</el-button>
        <el-button type="warning" plain icon="Download" @click="handleExport">{{ $t("common.export") }}
        </el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="3" style="display: flex; gap: 10px;">
        <el-button type="primary" plain icon="Setting" @click="handleWithdrawSetting" style="flex: 1;">{{
          $t("marking.withdrawalLevelManagement") }}</el-button>
        <el-button type="primary" plain icon="Plus" @click="handleShowModal('add')" style="flex: 1;">{{
          $t("marking.addMarketingActivity") }}</el-button>

      </el-col>
      <right-toolbar v-model:showSearch="state.showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 表格数据 -->
    <el-table v-loading="state.getListLoading" :data="state.tableData">
      <el-table-column :label="$t('marking.activityName')" prop="activityName" align="center" />
      <el-table-column :label="$t('marking.activityTime')" prop="timeRange" align="center" width="160">
        <template #default="scope">
          <div v-if="scope.row.startTime && scope.row.endTime">
            <div>{{ parseTime(scope.row.startTime, '{y}/{m}/{d} {h}:{i}:{s}') }}</div>
            <div>-</div>
            <div>{{ parseTime(scope.row.endTime, '{y}/{m}/{d} {h}:{i}:{s}') }}</div>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('marking.activityType')" prop="type" align="center">
        <template #default="scope">
          {{ codeToText(activityTypeEnum, scope.row.type) }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('marking.activityStatus')" prop="status" align="center">
        <template #default="scope">
          {{ codeToText(activityStatusEnum, scope.row.status) }}
        </template>
      </el-table-column>
      <el-table-column :label="$t('marking.isEnabled')" prop="enabled" align="center" width="100">
        <template #default="scope">
          <el-switch v-model="scope.row.enabled" @change="handleAction('enabled', scope.row.id)" />
        </template>
      </el-table-column>
      <el-table-column :label="$t('common.createdTime')" prop="createdTime" align="center" width="160">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}/{m}/{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('userManage.operate')" align="center" width="220" fixed="right"
        class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button type="text" @click="handleShowModal('edit', scope.row)">{{ $t("common.edit") }}</el-button>
          <el-button v-if="['REJECTED','WAITING_START','IN_PROGRESS', 'FINISHED'].includes(scope.row.status)"
            type="text" @click="handleShowModal('view', scope.row)">{{ $t("common.view") }}</el-button>
          <el-button v-if="['IN_PROGRESS', 'FINISHED'].includes(scope.row.status)" type="text"
            @click="handleShowModal('data', scope.row)">{{ $t("marking.data") }}</el-button>
          <el-button v-if="['DRAFT', 'REJECTED'].includes(scope.row.status)" type="text"
            @click="handleAction('submitAudit', scope.row.id)">{{ $t("marking.submitAudit") }}</el-button>
          <el-button v-if="['REVIEWING'].includes(scope.row.status)" type="text"
            @click="handleShowModal('audit', scope.row)" v-hasPermi="['marking:examine']">{{ $t("marking.audit")
            }}</el-button>
          <el-button v-if="['DRAFT', 'REVIEWING', 'REJECTED'].includes(scope.row.status)" type="text"
            @click="handleAction('delete', scope.row.id)">{{ $t("common.delete") }}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="state.total > 0" :total="state.total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改对话框 -->
    <el-dialog :title="state.dialogTitle" v-model="state.dialogVisible" width="600px" append-to-body
      :before-close="handleDialogClose">
      <el-form ref="formRef" :model="formParams" :rules="formRules" label-width="120px"
        :disabled="['view'].includes(state.dialogType) || (formParams.status && !['DRAFT', 'REJECTED'].includes(formParams.status))"
        :validate-on-rule-change="false">
        <el-form-item :label="$t('marking.activityType')" prop="activityType">
          <el-select v-model="formParams.activityType" :placeholder="$t('common.select')" class="full-width"
            :disabled="['audit'].includes(state.dialogType)">
            <el-option v-for="item in activityTypeEnum" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('marking.activityName')" required class="direction-column">
          <div v-for="(item, index) in formParams.activityNameList" :key="index"
            class="flex flex-y-center flex-1 full-width row-item">
            <div class="flex flex-y-center flex-1">
              <el-form-item :prop="`activityNameList.${index}.content`" :rules="[
                  {
                    required: true,
                    message: $t('common.pleaseEnterContent'),
                    trigger: 'blur',
                  },
                  { min: 1, max: 30, message: i18n.global.t('marking.activityNameLimit'), trigger: 'blur' }
                ]" class="full-width">
                <el-input v-model="item.content" :placeholder="$t('common.pleaseEnter')" maxlength="30" class="flex-1"
                  :disabled="['audit'].includes(state.dialogType)" />
              </el-form-item>
              <el-form-item :prop="`activityNameList.${index}.lang`" :rules="[
                  {
                    required: true,
                    message: $t('common.select'),
                    trigger: 'blur',
                  },
                ]">
                <el-select v-model="item.lang" :placeholder="$t('common.select')"
                  :disabled="['audit'].includes(state.dialogType)">
                  <el-option v-for="item in getLangOptions(formParams.activityNameList)" :key="item.code"
                    :label="item.name" :value="item.code" :disabled="item.disabled" />
                </el-select>
              </el-form-item>
            </div>
            <template
              v-if="['add'].includes(state.dialogType) || (state.dialogType !== 'view' && ['DRAFT', 'REJECTED'].includes(formParams.status))">
              <el-icon v-if="index === 0" :size="20" color="#067EFF" class="ml10 pointer" :class="{
                  disabled:
                    formParams.activityNameList.length ===
                    getLangOptions(formParams.activityNameList).length,
                }" @click="addItem('activityName')">
                <Plus />
              </el-icon>
              <el-icon v-else :size="20" color="#F53F3F" class="ml10 pointer"
                @click="deleteItem('activityName', index, item.id)">
                <Close />
              </el-icon>
            </template>
          </div>
        </el-form-item>
        <el-form-item :label="$t('marking.activityTime')" prop="timeRange">
          <el-date-picker v-model="formParams.timeRange" type="datetimerange" format="YYYY-MM-DD HH:00:00"
            time-format="HH" class="full-width" popper-class="activity-time-range-popper"
            :disabled="['audit'].includes(state.dialogType)" />
        </el-form-item>
        <el-form-item v-show="formParams.activityType === 'INVITE_NEW_USERS'" :label="$t('marking.taskDeadline')"
          prop="duration">
          <el-input v-model="formParams.duration" :placeholder="$t('common.pleaseEnter')"
            :disabled="['audit'].includes(state.dialogType)" @input="handleInput('int', 'duration')"
            @blur="handleBlur('int', 'duration')">
            <template #append>{{ $t("marking.hour") }}</template>
          </el-input>
        </el-form-item>
        <el-form-item :label="$t('marking.rewardCoin')" prop="currency">
          <el-select v-model="formParams.currency" :placeholder="$t('common.select')" class="full-width"
            @change="toValidate" :disabled="['audit'].includes(state.dialogType)">
            <el-option v-for="item in currencyEnum" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('marking.rewardCondition')" required class="direction-column">
          <el-radio-group v-model="formParams.isWalletOpen" :disabled="['audit'].includes(state.dialogType)">
            <el-radio :label="true">{{ $t("marking.needWallet") }}</el-radio>
            <!-- <el-radio :label="false">{{ $t("marking.noNeedWallet") }}</el-radio> -->
          </el-radio-group>
          <el-radio-group v-model="formParams.isKycAuth" :disabled="['audit'].includes(state.dialogType)">
            <el-radio :label="true">{{ $t("marking.needKyc") }}</el-radio>
            <el-radio :label="false">{{ $t("marking.noNeedKyc") }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('marking.newUserReward')" required>
          <div class="flex">
            <el-form-item prop="userRewardMin">
              <el-input v-model="formParams.userRewardMin" :placeholder="$t('marking.lowerLimit')"
                :disabled="['audit'].includes(state.dialogType)"
                @input="handleInput(formParams.currency === 'KHR' ? 'int' : 'float', 'userRewardMin')"
                @blur="handleBlur(formParams.currency === 'KHR' ? 'int' : 'float', 'userRewardMin')" />
            </el-form-item>
            <span class="ml10 mr10">——</span>
            <el-form-item prop="userRewardMax">
              <el-input v-model="formParams.userRewardMax" :placeholder="$t('marking.limit')"
                :disabled="['audit'].includes(state.dialogType)"
                @input="handleInput(formParams.currency === 'KHR' ? 'int' : 'float', 'userRewardMax')"
                @blur="handleBlur(formParams.currency === 'KHR' ? 'int' : 'float', 'userRewardMax')" />
            </el-form-item>
          </div>
        </el-form-item>
        <el-form-item :label="$t('marking.newUserRewardTotalAmountUpperLimit')" prop="userRewardTotal">
          <el-input v-model="formParams.userRewardTotal" :placeholder="$t('common.pleaseEnter')"
            :disabled="['audit'].includes(state.dialogType)"
            @input="handleInput(formParams.currency === 'KHR' ? 'int' : 'float', 'userRewardTotal')"
            @blur="handleBlur(formParams.currency === 'KHR' ? 'int' : 'float', 'userRewardTotal')" />
        </el-form-item>
        <div v-show="formParams.activityType === 'INVITE_NEW_USERS'">
          <el-form-item :label="$t('marking.needInvitation')" prop="inviteNum">
            <el-input v-model="formParams.inviteNum" :placeholder="$t('common.pleaseEnter')"
              :disabled="['audit'].includes(state.dialogType)" @input="handleInput('int', 'inviteNum')"
              @blur="handleBlur('int', 'inviteNum')" />
          </el-form-item>
          <el-form-item :label="$t('marking.rewardAmount')" prop="rewardAmount">
            <el-input v-model="formParams.rewardAmount" :placeholder="$t('common.pleaseEnter')"
              :disabled="['audit'].includes(state.dialogType)"
              @input="handleInput(formParams.currency === 'KHR' ? 'int' : 'float', 'rewardAmount')"
              @blur="handleBlur(formParams.currency === 'KHR' ? 'int' : 'float', 'rewardAmount')" />
          </el-form-item>
          <el-form-item :label="$t('marking.reserveReward')" required prop="rewardCalculateType">
            <el-radio-group v-model="formParams.rewardCalculateType">
              <el-radio v-for="item in rewardCalculateTypeEnum" :key="item.value" :value="item.value"
                :label="item.value" :disabled="['audit'].includes(state.dialogType)">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <div v-show="formParams.activityType === 'INVITE_SPLIT_USERS'">
          <el-form-item :label="$t('marking.fissionGrade')" required prop="splitLevel">
            <el-select v-model="formParams.splitLevel" :placeholder="$t('common.select')" class="full-width"
              :disabled="['audit'].includes(state.dialogType)" @change="splitLevelChange">
              <el-option v-for="item in 5" :key="item" :label="item" :value="item" />
            </el-select>
          </el-form-item>
          <el-form-item v-for="(item, index) in formParams.splitLevelList" :key="index"
            :label="$t('marking.levelReward', [item.label])" :prop="`splitLevelList.${index}.splitLevelReward`" :rules="[
              {
                required: formParams.activityType === 'INVITE_SPLIT_USERS',
                message: $t('common.pleaseEnterContent'),
                trigger: 'blur',
              },
              {
                validator: validateSplitLevel
              }
            ]">
            <el-input v-model="item.splitLevelReward" :placeholder="$t('common.pleaseEnter')"
              :disabled="['audit'].includes(state.dialogType)"
              @input="handleInput(formParams.currency === 'KHR' ? 'int' : 'float', 'splitLevelReward', item)"
              @blur="handleBlur(formParams.currency === 'KHR' ? 'int' : 'float', 'splitLevelReward', item)" />
          </el-form-item>
          <el-form-item :label="$t('marking.singleInviteeRewardUpperLimit')" prop="inviterRewardLimit">
            <el-input v-model="formParams.inviterRewardLimit" :placeholder="$t('common.pleaseEnter')"
              :disabled="['audit'].includes(state.dialogType)"
              @input="handleInput(formParams.currency === 'KHR' ? 'int' : 'float', 'inviterRewardLimit')"
              @blur="handleBlur(formParams.currency === 'KHR' ? 'int' : 'float', 'inviterRewardLimit')" />
          </el-form-item>
          <!-- <el-form-item :label="$t('marking.fissionRewardUpperLimit')" prop="totalRewardLimit">
            <el-input
              v-model="formParams.totalRewardLimit"
              :placeholder="$t('common.pleaseEnter')"
              :disabled="['audit'].includes(state.dialogType)"
              @input="handleInput(formParams.currency === 'KHR' ? 'int' : 'float', 'totalRewardLimit')"
              @blur="handleBlur(formParams.currency === 'KHR' ? 'int' : 'float', 'totalRewardLimit')"
            />
          </el-form-item> -->
        </div>
        <el-form-item :label="$t('marking.activityRules')" class="direction-column">
          <div v-for="(item, index) in formParams.activityRuleDescList" :key="index"
            class="flex flex-y-center flex-1 full-width row-item">
            <div class="flex flex-1">
              <el-form-item :prop="`activityRuleDescList.${index}.content`" class="full-width">
                <el-input v-model="item.content"
                  :placeholder="['add', 'edit'].includes(state.dialogType)? $t('common.pleaseEnter'): ''" class="flex-1"
                  show-word-limit type="textarea" :autosize="{ minRows: 4, maxRows: 10 }"
                  :disabled="['audit'].includes(state.dialogType)" maxlength="1000" />
              </el-form-item>
              <el-form-item :prop="`activityRuleDescList.${index}.lang`" :rules="[
                {
                  required: true,
                  message: $t('common.select'),
                  trigger: 'blur',
                },
              ]">
                <el-select v-model="item.lang" :placeholder="$t('common.select')"
                  :disabled="['audit'].includes(state.dialogType)">
                  <el-option v-for="item in getLangOptions(formParams.activityRuleDescList)" :key="item.code"
                    :label="item.name" :value="item.code" :disabled="item.disabled" />
                </el-select>
              </el-form-item>
            </div>
            <template
              v-if="['add'].includes(state.dialogType) || (state.dialogType !== 'view' && ['DRAFT', 'REJECTED'].includes(formParams.status))">
              <el-icon v-if="index === 0" :size="20" color="#067EFF" class="ml10 pointer" :class="{
                  disabled:
                    formParams.activityRuleDescList.length ===
                    getLangOptions(formParams.activityRuleDescList).length,
                }" @click="addItem('activityRuleDesc')">
                <Plus />
              </el-icon>
              <el-icon v-else :size="20" color="#F53F3F" class="ml10 pointer"
                @click="deleteItem('activityRuleDesc', index, item.id)">
                <Close />
              </el-icon>
            </template>
          </div>
        </el-form-item>
      </el-form>
      <!-- 适用人群表单 -->
      <el-form ref="targetFormRef" :model="formParams" label-width="120px"
        :disabled="['view', 'audit'].includes(state.dialogType)">
        <el-form-item :label="$t('marking.targetAudience')" prop="suitableType" required>
          <el-radio-group v-model="formParams.suitableType">
            <el-radio v-for="item in targetAudienceEnum" :key="item.value" :value="item.value" :label="item.value">{{
              item.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-show="formParams.suitableType === 'SPECIFY'" prop="suitablePhone" :rules="[
          {
            required: formParams.suitableType === 'SPECIFY',
            message: $t('common.pleaseEnterContent'),
            trigger: 'blur',
          },
        ]">
          <el-input v-model="formParams.suitablePhone" :placeholder="$t('marking.targetAudienceTip')" type="textarea"
            :autosize="{ minRows: 4, maxRows: 8 }" />
        </el-form-item>
      </el-form>
      <!-- 审核表单 -->
      <el-form v-if="['view', 'audit'].includes(state.dialogType) && formParams.status !='DRAFT'" ref="auditFormRef"
        :model="formParams" label-width="120px" :rules="formRules" :disabled="['view'].includes(state.dialogType)">
        <el-form-item :label="$t('marking.auditResult')" prop="auditResult">
          <el-radio-group v-model="formParams.auditResult">
            <el-radio v-for="item in auditResultEnum" :key="item.value" :value="item.value" :label="item.value">{{
              item.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-show="!formParams.auditResult" :label="$t('marking.rejectReason')" prop="rejectReason">
          <el-input v-model="formParams.rejectReason"
            :placeholder="['add', 'edit'].includes(state.dialogType)? $t('common.pleaseEnter'): ''" show-word-limit
            type="textarea" :autosize="{ minRows: 1, maxRows: 4 }" maxlength="200" />
        </el-form-item>
        <el-form-item v-if="['audit'].includes(state.dialogType) && formParams.auditResult"
          :label="$t('marking.securityKey')" prop="secretKey">
          <el-input v-model="formParams.secretKey" :placeholder="$t('common.pleaseEnter')" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <template v-if="state.dialogType === 'view'">
            <el-button @click="handleDialogClose">{{ $t("common.close") }}</el-button>
          </template>
          <template v-else>
            <el-button type="primary" @click="submitForm">{{
              $t("common.confirm")
              }}</el-button>
            <el-button @click="handleDialogClose">{{ $t("common.cancel") }}</el-button>
          </template>
        </div>
      </template>
    </el-dialog>
    <!-- 数据对话框 -->
    <el-dialog :title="state.dataDialogTitle" v-model="state.dataDialogVisible" width="980px" append-to-body
      :before-close="handleDataDialogClose">
      <div class="flex flex-y-center">
        <div class="radio-group-container">
          <el-radio-group fill="#0bb976" @change="changeQueryType" v-model="dataQueryParams.queryType">
            <el-radio-button :label="1">{{
              $t("homePage.today")
              }}</el-radio-button>
            <el-radio-button :label="2">{{
              $t("homePage.yesterday")
              }}</el-radio-button>
            <el-radio-button :label="3">{{
              $t("homePage.lastSevenDays")
              }}</el-radio-button>
            <el-radio-button :label="4">{{
              $t("homePage.lastThirtyDays")
              }}</el-radio-button>
            <el-radio-button :label="5">{{
              $t("marking.historyCumulative")
              }}</el-radio-button>
          </el-radio-group>
        </div>
        <el-date-picker @change="changeDate" v-model="dataQueryParams.dateRange" type="daterange"
          :disabled-date="disabledDate" :placeholder="$t('common.selectDate')" class="ml10 mr10 date-range" />
        <el-button icon="Refresh" @click="resetDataQuery">{{
          $t("common.reset")
          }}</el-button>
      </div>
      <div class="data-card-list mt20" :style="{
        'grid-template-columns': `repeat(${state.dataDialogType === 'INVITE_SPLIT_USERS' ? 3 : 4}, 1fr)`
      }">
        <div v-for="item in dataCardList" :key="item.value" class="data-card-item"
          :class="{'active': item.type === dataQueryParams.lineChartType, 'pointer': item.value !== 'totalAmount'}"
          @click="handleDataTypeClick(item.type)">
          <el-tooltip v-if="item.tooltip" effect="dark" :content="item.tooltip" placement="top">
            <el-icon class="tooltip-icon">
              <InfoFilled />
            </el-icon>
          </el-tooltip>
          <div class="title">{{ item.label }}</div>
          <div class="num">{{ item.num }}</div>
          <div v-if="item.value !== 'totalAmount' && [2, 3, 4].includes(dataQueryParams.queryType)" class="diffSr">
            <span>{{ diffStr }}：</span>
            <span class="no-wrap">{{ item.diffNum }}</span>
          </div>
        </div>
      </div>
      <div id="lineChart" class="chart-container mt20"></div>
    </el-dialog>

    <!-- 提现档位管理对话框 -->
    <el-dialog :title="state.withdrawDialogTitle" v-model="state.withdrawDialogVisible" width="600px" append-to-body
      :before-close="handleWithdrawDialogClose">
      <el-form ref="withdrawFormRef" :model="withdrawFormData" label-width="0px">
        <div class="withdraw-setting-container">
          <div class="withdraw-tip">
            提现金额≥对应档位金额才可提现
          </div>

          <!-- USD 档位设置 -->
          <div class="currency-section">
            <h3 class="currency-title">USD (最多3个档位，金额范围：0.01-999.99)</h3>
            <div v-for="(item, index) in withdrawFormData.USD" :key="index" class="withdraw-level-item">
              <el-form-item :prop="`USD.${index}.amount`" :rules="getWithdrawRules('USD', index)"
                class="amount-form-item">
                <el-input v-model="item.amount" placeholder="请输入金额" class="amount-input"
                  @input="(val) => handleAmountInput(val, 'USD', index)" @blur="validateSingleField('USD', index)" />
              </el-form-item>
              <el-button v-if="index === 0" type="primary" icon="Plus" circle size="small"
                @click="addWithdrawLevel('USD')" class="action-btn" :disabled="withdrawFormData.USD.length >= 3" />
              <el-button v-else type="danger" icon="Close" circle size="small"
                @click="removeWithdrawLevel('USD', index)" class="action-btn" />
            </div>
          </div>

          <!-- KHR 档位设置 -->
          <div class="currency-section">
            <h3 class="currency-title">KHR (最多3个档位，金额范围：100-9999999)</h3>
            <div v-for="(item, index) in withdrawFormData.KHR" :key="index" class="withdraw-level-item">
              <el-form-item :prop="`KHR.${index}.amount`" :rules="getWithdrawRules('KHR', index)"
                class="amount-form-item">
                <el-input v-model="item.amount" placeholder="请输入金额" class="amount-input"
                  @input="(val) => handleAmountInput(val, 'KHR', index)" @blur="validateSingleField('KHR', index)" />
              </el-form-item>
              <el-button v-if="index === 0" type="primary" icon="Plus" circle size="small"
                @click="addWithdrawLevel('KHR')" class="action-btn" :disabled="withdrawFormData.KHR.length >= 3" />
              <el-button v-else type="danger" icon="Close" circle size="small"
                @click="removeWithdrawLevel('KHR', index)" class="action-btn" />
            </div>
          </div>
        </div>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleWithdrawDialogClose">取消</el-button>
          <el-button type="primary" @click="confirmWithdrawSetting">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Marketing">
import { reactive, ref, getCurrentInstance, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useStore } from 'vuex'
import i18n from '@/i18n'
import echarts from '@/plugins/echarts'

import {
  pageMarketList,
  addMarket,
  audit,
  deleteActivity,
  editActivity,
  enableActivity,
  getInfoActivity,
  submitAudit,
  getActivityStatics,
  getActivityStaticsLineChart
} from '@/api/market/market'
import { codeToText } from '@/utils'
import { getTodayTimestampRange, getNDaysTimestampRange } from '@/utils/dateUtil'
import {
  activityTypeEnum,
  currencyEnum,
  rewardCalculateTypeEnum,
  activityStatusEnum,
  auditResultEnum,
  targetAudienceEnum
} from '@/utils/enum'

const { proxy } = getCurrentInstance()

const queryFormRef = ref()
const formRef = ref()
const auditFormRef = ref()
const targetFormRef = ref()
const withdrawFormRef = ref()

// 搜索参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  activityName: '',
  activityType: null,
  activityStatus: null
})

const state = reactive({
  showSearch: true,
  getListLoading: true,
  tableData: [],
  total: 0,
  dialogVisible: false,
  dialogTitle: '',
  dialogType: '',
  dataDialogVisible: false,
  dataDialogType: '',
  dataDialogTitle: i18n.global.t('marking.activityData'),
  withdrawDialogVisible: false,
  withdrawDialogTitle: '提现档位管理'
})

// 校验用户奖励上、下限
const validateUserReward = (rule, value, callback) => {
  console.log(value)
  const minLimit = 0
  const maxLimit = formParams.value.currency === 'USD' ? 99.99 : 9999
  const regex = /^[+-]?(\d+(\.\d*)?|\.\d+)([eE][+-]?\d+)$/
  if (formParams.value.currency === 'KHR' && !regex.test(value) && String(value).includes('.')) {
    return callback(new Error(i18n.global.t('marking.canNotContainDecimals')))
  }
  if (rule.field === 'userRewardMin') {
    if (formParams.value.userRewardMin <= minLimit || formParams.value.userRewardMin > maxLimit) {
      return callback(new Error(i18n.global.t('marking.betweenLimit', [i18n.global.t('marking.lowerLimit'), 0, maxLimit])))
    }
    if (formParams.value.userRewardMax && formParams.value.userRewardMax < formParams.value.userRewardMin) {
      return callback(new Error(i18n.global.t('marking.cannotGreaterThan', [i18n.global.t('marking.lowerLimit'), i18n.global.t('marking.limit')])))
    }
  } else if (rule.field === 'userRewardMax') {
    if (formParams.value.userRewardMax <= minLimit || formParams.value.userRewardMax > maxLimit) {
      return callback(new Error(i18n.global.t('marking.betweenLimit', [i18n.global.t('marking.limit'), 0, maxLimit])))
    }
    if (formParams.value.userRewardMin && formParams.value.userRewardMax < formParams.value.userRewardMin) {
      return callback(new Error(i18n.global.t('marking.mustGreaterThan', [i18n.global.t('marking.limit'), i18n.global.t('marking.lowerLimit')])))
    }
  }
  return callback()
}
// 校验x级拉新奖励
const validateSplitLevel = (rule, value, callback) => {
  if (formParams.value.activityType === 'INVITE_SPLIT_USERS') {
    const min = formParams.value.currency === 'KHR' ? 1 : 0.01
    const max = formParams.value.currency === 'KHR' ? 9999 : 99.99
    const fileName = i18n.global.t('marking.levelReward', [+rule.field.split('.')[1] + 1])
    const regex = /^[+-]?(\d+(\.\d*)?|\.\d+)([eE][+-]?\d+)$/
    if (formParams.value.currency === 'KHR' && !regex.test(value) && String(value).includes('.')) {
      return callback(new Error(i18n.global.t('marking.canNotContainDecimals')))
    }
    if (value < min || value > max) {
      callback(new Error(i18n.global.t('marking.betweenLimit1', [fileName, min, max])))
    } else {
      callback()
    }
  } else {
    callback()
  }
}

// 校验新用户奖励总金额上限
const validateReward = (rule, value, callback) => {
  const field = rule.field
  if (!(field === 'userRewardTotal' || (formParams.value.activityType === 'INVITE_SPLIT_USERS' && ['inviterRewardLimit'].includes(field)) || (formParams.value.activityType === 'INVITE_NEW_USERS' && ['rewardAmount'].includes(field)))) {
    return callback()
  }
  let minLimit = 0
  let maxLimitUSD = 9999.99
  let maxLimitKHR = 999999
  let fieldName = i18n.global.t('marking.newUserRewardTotalAmountUpperLimit')
  if (rule.field === 'rewardAmount') {
    fieldName = i18n.global.t('marking.rewardAmount')
  } else if (rule.field === 'inviterRewardLimit') {
    fieldName = i18n.global.t('marking.singleInviteeRewardUpperLimit')
    minLimit = formParams.value.currency === 'KHR' ? 1 : 0.01
    maxLimitKHR = 999999
  } else if (rule.field === 'totalRewardLimit') {
    maxLimitUSD = 9999999.99
    maxLimitKHR = 999999999
    fieldName = i18n.global.t('marking.fissionRewardUpperLimit')
  }
  const maxLimit = formParams.value.currency === 'USD' ? maxLimitUSD : maxLimitKHR
  const regex = /^[+-]?(\d+(\.\d*)?|\.\d+)([eE][+-]?\d+)$/
  if (formParams.value.currency === 'KHR' && !regex.test(value) && String(value).includes('.')) {
    return callback(new Error(i18n.global.t('marking.canNotContainDecimals')))
  }
  if (value || value === 0) {
    if (rule.field === 'inviterRewardLimit') {
      if (value < minLimit || value > maxLimit) {
        return callback(new Error(i18n.global.t('marking.betweenLimit1', [fieldName, minLimit, maxLimit])))
      }
    } else {
      if (value <= minLimit || value > maxLimit) {
        return callback(new Error(i18n.global.t('marking.betweenLimit', [fieldName, minLimit, maxLimit])))
      }
    }
    if (rule.field === 'userRewardTotal') {
      if ((formParams.value.userRewardMax || formParams.value.userRewardMax === 0) && value < formParams.value.userRewardMax) {
        return callback(new Error(i18n.global.t('marking.mustGreaterThan', [fieldName, i18n.global.t('marking.limit')])))
      }
    }
  }
  return callback()
}

// 校验邀请人数
const validateInviteNum = (rule, value, callback) => {
  if (formParams.value.activityType !== 'INVITE_NEW_USERS') {
    return callback()
  }
  const minLimit = 0
  const maxLimit = 999
  if (value || value === 0) {
    if (value <= minLimit) {
      return callback(new Error(i18n.global.t('marking.mustGreaterThan', [i18n.global.t('marking.needInvitation'), minLimit, maxLimit])))
    }
    if (value > maxLimit) {
      return callback(new Error(i18n.global.t('marking.cannotGreaterThan', [i18n.global.t('marking.needInvitation'), maxLimit, maxLimit])))
    }
  }
  return callback()
}

// 新增参数
const formParams = ref({
  activityName: '',
  activityRuleDesc: '',
  timeRange: '',
  activityType: 'INVITE_NEW_USERS',
  currency: '',
  duration: null,
  startTime: '',
  endTime: '',
  inviteNum: null,
  isWalletOpen: true,
  isKycAuth: true,
  rewardAmount: null,
  rewardCalculateType: 'NO_MINIMUM',
  userRewardMax: null,
  userRewardMin: null,
  userRewardTotal: null,
  auditResult: null,
  rejectReason: '',
  delI18nDataIds: [],
  activityNameList: [],
  activityRuleDescList: [],
  suitableType: 'ALL',
  suitablePhone: '',
  splitLevel: 3,
  splitLevelList: [],
  inviterRewardLimit: '',
  totalRewardLimit: ''
})

const splitLevelChange = () => {
  if (formParams.value.splitLevelList.length < formParams.value.splitLevel) {
    for (let i = formParams.value.splitLevelList.length; i < formParams.value.splitLevel; i++) {
      formParams.value.splitLevelList.push({
        label: i + 1,
        splitLevelReward: ''
      })
    }
  } else if (formParams.value.splitLevelList.length > formParams.value.splitLevel) {
    formParams.value.splitLevelList.splice(formParams.value.splitLevel)
  }
  toValidate()
}
const formRules = computed(() => {
  return {
    activityType: [
      {
        required: true,
        message: i18n.global.t('common.select'),
        trigger: 'blur'
      }
    ],
    timeRange: [
      {
        required: true,
        message: i18n.global.t('common.select'),
        trigger: 'change'
      }
    ],
    duration: [
      {
        required: formParams.value.activityType === 'INVITE_NEW_USERS',
        message: i18n.global.t('common.pleaseEnterContent'),
        trigger: 'blur'
      },
      {
        validator: (rule, value, callback) => {
          if (formParams.value.activityType === 'INVITE_NEW_USERS' && (value < 1 || value > 9999)) {
            callback(new Error((i18n.global.t('marking.durationLimit'))))
          } else {
            callback()
          }
        },
        trigger: 'blur'
      }
    ],
    currency: [
      {
        required: true,
        message: i18n.global.t('common.select'),
        trigger: 'change'
      }
    ],
    userRewardMin: [
      {
        required: true,
        message: i18n.global.t('common.pleaseEnterContent'),
        trigger: 'blur'
      },
      {
        validator: validateUserReward,
        trigger: 'blur'
      }
    ],
    userRewardMax: [
      {
        required: true,
        message: i18n.global.t('common.pleaseEnterContent'),
        trigger: 'blur'
      },
      {
        validator: validateUserReward,
        trigger: 'blur'
      }
    ],
    inviteNum: [
      {
        required: formParams.value.activityType === 'INVITE_NEW_USERS',
        message: i18n.global.t('common.pleaseEnterContent'),
        trigger: 'blur'
      },
      {
        validator: validateInviteNum,
        trigger: 'blur'
      }
    ],
    rewardAmount: [
      {
        required: formParams.value.activityType === 'INVITE_NEW_USERS',
        message: i18n.global.t('common.pleaseEnterContent'),
        trigger: 'blur'
      },
      {
        validator: validateReward,
        trigger: 'blur'
      }
    ],
    userRewardTotal: [
      {
        required: true,
        message: i18n.global.t('common.pleaseEnterContent'),
        trigger: 'blur'
      },
      {
        validator: validateReward,
        trigger: 'blur'
      }
    ],
    auditResult: [
      {
        required: false,
        message: i18n.global.t('common.select'),
        trigger: 'change'
      }
    ],
    secretKey: [
      {
        required: false,
        message: i18n.global.t('common.pleaseEnterContent'),
        trigger: 'blur'
      }
    ],
    inviterRewardLimit: [
      {
        required: formParams.value.activityType === 'INVITE_SPLIT_USERS',
        message: i18n.global.t('common.pleaseEnterContent'),
        trigger: 'blur'
      },
      {
        validator: validateReward,
        trigger: 'blur'
      }
    ]
    // totalRewardLimit: [
    //   {
    //     required: true,
    //     message: i18n.global.t('common.pleaseEnterContent'),
    //     trigger: 'blur'
    //   },
    //   {
    //     validator: validateReward,
    //     trigger: 'blur'
    //   }
    // ]
  }
})

function toValidate() {
  const allFieldList = ['userRewardMin', 'userRewardMax', 'userRewardTotal']
  if (formParams.value.activityType === 'INVITE_NEW_USERS') {
    allFieldList.push('rewardAmount')
  } else if (formParams.value.activityType === 'INVITE_SPLIT_USERS') {
    allFieldList.push('inviterRewardLimit', 'splitLevelList')
  }
  const needFieldList = []
  for (const item of allFieldList) {
    if (item === 'splitLevelList') {
      for (let i = 0; i < formParams.value.splitLevelList.length; i++) {
        const element = formParams.value.splitLevelList[i]
        if (element.splitLevelReward || element.splitLevelReward === 0) {
          needFieldList.push(`${item}.${i}.splitLevelReward`)
        }
      }
    } else {
      if (formParams.value[item] || formParams.value[item] === 0) {
        needFieldList.push(item)
      }
    }
  }
  formRef.value.validateField(needFieldList, (isValid) => {
    console.log('🚀 ~ file: index.vue:839 ~ formRef.value.validateField ~ isValid:', isValid)
  })
}

function handleInput(type, field, item) {
  const form = item || formParams.value
  const value = form[field]
  let regex = ''
  if (type === 'int') {
    regex = /^\+?[1-9]\d*$/
  } else if (type === 'float') {
    // 允许最后一位为小数点或者小数点后两位
    regex = /^(?:[1-9]\d*|0)(?:\.\d{0,2}|)$/
  }
  if (!regex.test(value)) {
    // 如果输入不符合规则，则将值设置回上一个有效值
    form[field] = value.substring(0, value.length - 1)
  } else {
    // 如果值最后一位为小数点，则为原值，否则为格式化值
    if (value.endsWith('.') || value.endsWith('.0')) {
      form[field] = value
    } else {
      form[field] = Number(value)
    }
  }
}

function handleBlur(type, field, item) {
  const form = item || formParams.value
  const value = form[field]
  if (isNaN(value) || (type === 'int' && String(value).includes('.'))) {
    form[field] = ''
  }
}

const store = useStore()
const languageList = computed(() => store.state.app.languageList)
// 多语言下拉框选项
function getLangOptions(list) {
  return languageList.value.map((item) => {
    const idx = list.findIndex((v) => v.lang === item.code)
    return {
      ...item,
      disabled: idx !== -1
    }
  })
}

// 获取列表数据
function getList() {
  state.getListLoading = true
  if (!queryParams.value.activityType) queryParams.value.activityType = null
  if (!queryParams.value.activityStatus) { queryParams.value.activityStatus = null }
  pageMarketList(queryParams.value)
    .then((res) => {
      state.tableData = res.data.records
      state.total = res.data.total
    })
    .finally(() => (state.getListLoading = false))
}

// 查询数据
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

// 重置查询
function resetQuery() {
  if (queryFormRef.value) {
    queryFormRef.value.resetFields()
  }
  queryParams.value.activityName = ''
  queryParams.value.activityType = null
  queryParams.value.activityStatus = null
  handleQuery()
}

// 导出按钮操作
function handleExport() {
  proxy.download(
    'market/activity/export',
    {
      ...queryParams.value
    },
    `Marketing_Activities_${proxy.parseTime(new Date(), '{y}/{m}/{d}')}.xlsx`
  )
}

function resetForm() {
  formParams.value = {
    activityName: '',
    activityRuleDesc: '',
    timeRange: '',
    activityType: 'INVITE_NEW_USERS',
    currency: '',
    duration: null,
    startTime: '',
    endTime: '',
    inviteNum: null,
    isWalletOpen: true,
    isKycAuth: true,
    rewardAmount: null,
    rewardCalculateType: 'NO_MINIMUM',
    userRewardMax: null,
    userRewardMin: null,
    userRewardTotal: null,
    auditResult: null,
    rejectReason: '',
    delI18nDataIds: [],
    suitableType: 'ALL',
    suitablePhone: '',
    activityNameList: [
      {
        bizFiled: 'MARKET_ACTIVITY_NAME',
        content: '',
        lang: 'zh-CN'
      }
    ],
    activityRuleDescList: [
      {
        bizFiled: 'MARKET_ACTIVITY_RULE_DESC',
        content: '',
        lang: 'zh-CN'
      }
    ],
    splitLevel: 3,
    splitLevelList: [],
    inviterRewardLimit: '',
    totalRewardLimit: ''
  }
  if (formParams.value.splitLevelList.length < formParams.value.splitLevel) {
    for (let i = formParams.value.splitLevelList.length; i < formParams.value.splitLevel; i++) {
      formParams.value.splitLevelList.push({
        label: i + 1,
        splitLevelReward: ''
      })
    }
  }

  if (languageList.value.length) {
    const lang = languageList.value[0].code
    formParams.value.activityNameList[0].lang = lang
    formParams.value.activityRuleDescList[0].lang = lang
  }
  if (formRef.value) {
    formRef.value.resetFields()
  }
  if (targetFormRef.value) {
    targetFormRef.value.resetFields()
  }
}

function handleShowModal(type, item) {
  // 查看数据的弹窗
  if (type === 'data') {
    const { id } = item
    dataQueryParams.value.activityId = id
    state.dataDialogVisible = true
    state.dataDialogType = item.type
    handleDataQuery()
    return
  }
  state.dialogType = type
  resetForm()
  toggleAuditValidation(false)
  state.dialogTitle = i18n.global.t('marking.configureMarketingActivity')
  if (['view', 'edit', 'audit'].includes(type)) {
    getInfo(item)
    if (type === 'view') {
      state.dialogTitle = i18n.global.t('marking.viewMarketingActivity')
    } else if (type === 'audit') {
      state.dialogTitle = i18n.global.t('marking.auditMarketingActivity')
      toggleAuditValidation(true)
    }
  }
  state.dialogVisible = true
}

const toggleAuditValidation = (enable) => {
  formRules.value.auditResult[0].required = enable
  formRules.value.secretKey[0].required = enable
  auditFormRef.value && auditFormRef.value.resetFields()
}

async function getInfo(item) {
  formParams.value.id = item.id
  formParams.value.status = item.status
  const params = { id: item.id }
  const res = await getInfoActivity(params)
  if (res.code === 200) {
    const data = res.data
    formParams.value = {
      ...formParams.value,
      ...data
    }
    if (data.startTime && data.endTime) {
      formParams.value.timeRange = [data.startTime, data.endTime]
    }
    const activityNameList = data.i18nDataList.filter(
      (item) => item.bizFiled === 'MARKET_ACTIVITY_NAME'
    )
    const activityRuleDescList = data.i18nDataList.filter(
      (item) => item.bizFiled === 'MARKET_ACTIVITY_RULE_DESC'
    )
    formParams.value.activityNameList = activityNameList
    formParams.value.activityRuleDescList = activityRuleDescList
    const splitLevelList = []
    if (formParams.value.splitLevel) {
      for (let i = 0; i < formParams.value.splitLevel; i++) {
        splitLevelList.push({
          label: i + 1,
          splitLevelReward: formParams.value[`splitLevel${i + 1}Reward`]
        })
      }
    }
    formParams.value.splitLevelList = splitLevelList
  }
}

function addItem(name) {
  switch (name) {
    case 'activityName':
      if (
        formParams.value.activityNameList.length ===
        getLangOptions(formParams.value.activityNameList).length
      ) { return }
      formParams.value.activityNameList.push({
        bizFiled: 'MARKET_ACTIVITY_NAME',
        content: '',
        lang: ''
      })
      break
    case 'activityRuleDesc':
      if (
        formParams.value.activityRuleDescList.length ===
        getLangOptions(formParams.value.activityRuleDescList).length
      ) { return }
      formParams.value.activityRuleDescList.push({
        bizFiled: 'MARKET_ACTIVITY_RULE_DESC',
        content: '',
        lang: ''
      })
      break
    default:
      break
  }
}

function deleteItem(name, index, id) {
  if (id) {
    formParams.value.delI18nDataIds.push(id)
  }
  switch (name) {
    case 'activityName':
      formParams.value.activityNameList.splice(index, 1)
      break
    case 'activityRuleDesc':
      formParams.value.activityRuleDescList.splice(index, 1)
      break
    default:
      break
  }
}

// 关闭弹窗
function handleDialogClose() {
  resetForm()
  state.dialogVisible = false
}

// 数据查询参数
const dataQueryParams = ref({
  activityId: '',
  dateRange: null,
  isHistory: false,
  queryType: 3,
  lineChartType: 1
})

// 数据
const dataCardList = ref([])
watch(
  () => state.dataDialogType,
  (val) => {
    if (val === 'INVITE_SPLIT_USERS') {
      dataCardList.value = [
        {
          label: i18n.global.t('marking.issuedAmount'),
          value: 'grantAmount',
          type: 1,
          num: 0,
          diffNum: '-',
          tooltip: i18n.global.t('marking.issuedAmountTip')
        },
        {
          label: i18n.global.t('marking.inviteUserRewardAmount'),
          value: 'inviterAmount',
          type: 3,
          num: 0,
          diffNum: '-',
          tooltip: i18n.global.t('marking.inviteUserRewardAmountTip')
        },
        {
          label: i18n.global.t('marking.newUserRewardAmount'),
          value: 'newUserAmount',
          type: 2,
          num: 0,
          diffNum: '-',
          tooltip: i18n.global.t('marking.newUserRewardAmountTip')
        },
        {
          label: i18n.global.t('marking.activityPageViews'),
          value: 'viewCount',
          type: 7,
          num: 0,
          diffNum: '-',
          tooltip: i18n.global.t('marking.activityPageViewsTip')
        },
        {
          label: i18n.global.t('marking.inviteUserNumber'),
          value: 'sendInviteCount',
          type: 8,
          num: 0,
          diffNum: '-',
          tooltip: i18n.global.t('marking.inviteUserNumberTip')
        },
        {
          label: i18n.global.t('marking.inviteNewUser'),
          value: 'newUserCount',
          type: 5,
          num: 0,
          diffNum: '-',
          tooltip: i18n.global.t('marking.inviteNewUserTip')
        }
      ]
    } else {
      dataCardList.value = [
        {
          label: i18n.global.t('marking.issuedAmount'),
          value: 'grantAmount',
          type: 1,
          num: 0,
          diffNum: '-',
          tooltip: i18n.global.t('marking.issuedAmountTip')
        },
        {
          label: i18n.global.t('marking.newUserRewardAmount'),
          value: 'newUserAmount',
          type: 2,
          num: 0,
          diffNum: '-',
          tooltip: i18n.global.t('marking.newUserRewardAmountTip')
        },
        {
          label: i18n.global.t('marking.inviteUserRewardAmount'),
          value: 'inviterAmount',
          type: 3,
          num: 0,
          diffNum: '-',
          tooltip: i18n.global.t('marking.inviteUserRewardAmountTip')
        },
        {
          label: i18n.global.t('marking.expectedAmount'),
          value: 'totalAmount',
          num: 0,
          diffNum: '-',
          tooltip: i18n.global.t('marking.expectedAmountTip')
        },
        {
          label: i18n.global.t('marking.participationNumber'),
          value: 'joinCount',
          type: 4,
          num: 0,
          diffNum: '-',
          tooltip: i18n.global.t('marking.participationNumberTip')
        },
        {
          label: i18n.global.t('marking.inviteNewUser'),
          value: 'newUserCount',
          type: 5,
          num: 0,
          diffNum: '-',
          tooltip: i18n.global.t('marking.inviteNewUserTip')
        },
        {
          label: i18n.global.t('marking.completedTaskNumber'),
          value: 'finishTaskCount',
          type: 6,
          num: 0,
          diffNum: '-',
          tooltip: i18n.global.t('marking.completedTaskNumberTip')
        },
        {
          label: i18n.global.t('marking.activityPageViews'),
          value: 'viewCount',
          type: 7,
          num: 0,
          diffNum: '-',
          tooltip: i18n.global.t('marking.activityPageViewsTip')
        }
      ]
    }
  },
  {
    immediate: true
  }
)
const diffStr = computed(() => {
  let text = ''
  switch (dataQueryParams.value.queryType) {
    case 2:
      text = i18n.global.t('marking.comparedWith1DayAgo')
      break
    case 3:
      text = i18n.global.t('marking.comparedWith7DaysAgo')
      break
    case 4:
      text = i18n.global.t('marking.comparedWith30DaysAgo')
      break
    default:
      break
  }
  return text
})

const disabledDate = (time) => {
  return time.getTime() > Date.now()
}

// 修改查询类型
function changeQueryType() {
  dataQueryParams.value.dateRange = null
  handleDataQuery()
}

// 修改时间
function changeDate() {
  if (!dataQueryParams.value.dateRange) {
    dataQueryParams.value.queryType = 3
  } else if (dataQueryParams.value.dateRange.length === 2) {
    dataQueryParams.value.queryType = 6
  }
  dataQueryParams.value.queryType = 6
  handleDataQuery()
}

// 修改时间
async function handleDataTypeClick(type) {
  if (!type) return
  dataQueryParams.value.lineChartType = type
  const params = handleDataQueryParams('chart')
  const res = await getActivityStaticsLineChart(params)
  const lineChart = res.data || []
  updateLineChart(lineChart)
}

// 关闭数据的弹窗
function handleDataDialogClose() {
  state.dataDialogVisible = false
  dataQueryParams.value.activityId = ''
  dataQueryParams.value.dateRange = []
  dataQueryParams.value.isHistory = false
  dataQueryParams.value.queryType = 3
  dataQueryParams.value.lineChartType = 1
}

function handleDataQueryParams(type) {
  let dateRange = null
  if (dataQueryParams.value.queryType === 1) {
    dateRange = getTodayTimestampRange()
  } else if (dataQueryParams.value.queryType === 2) {
    dateRange = getNDaysTimestampRange(1)
  } else if (dataQueryParams.value.queryType === 3) {
    dateRange = getNDaysTimestampRange(7)
  } else if (dataQueryParams.value.queryType === 4) {
    dateRange = getNDaysTimestampRange(30)
  } else if (dataQueryParams.value.queryType === 6) {
    dateRange = {
      startTime: new Date(dataQueryParams.value.dateRange[0]).getTime(),
      endTime: new Date(dataQueryParams.value.dateRange[1]).getTime() + (1 * 24 * 60 * 60 * 1000)
    }
  }
  dataQueryParams.value.isHistory = dataQueryParams.value.queryType === 5
  const params = {
    ...dataQueryParams.value
  }
  if (dateRange) {
    params.startTime = dateRange.startTime
    params.endTime = dateRange.endTime
  }
  if (type === 'chart') {
    params.queryType = params.lineChartType
  }
  delete params.dateRange
  delete params.lineChartType
  return params
}

// 查询数据
async function handleDataQuery() {
  if (!dataQueryParams.value.activityId) return
  const params = handleDataQueryParams('statics')
  const chartParams = handleDataQueryParams('chart')
  const res = await Promise.allSettled([getActivityStatics(params), getActivityStaticsLineChart(chartParams)])
  if (res[0].status === 'fulfilled') {
    const statics = res[0].value.data
    dataCardList.value.forEach((item) => {
      item.num = statics[item.value] || statics[item.value] === 0 ? statics[item.value] : '-'
      item.diffNum = statics[`${item.value}Diff`] || statics[`${item.value}Diff`] === 0 ? statics[`${item.value}Diff`] : '-'
    })
  }
  if (res[1].status === 'fulfilled') {
    const lineChart = res[1].value.data || []
    updateLineChart(lineChart)
  }
}
let lineChart

function updateLineChart(data) {
  if (!lineChart) {
    lineChart = echarts.init(document.getElementById('lineChart'))
  }
  const xA = []
  const yA = []
  const currentCard = dataCardList.value.find((item) => item.type === dataQueryParams.value.lineChartType)
  let timeFormatStr = '{y}/{m}/{d}'
  if ([1, 2].includes(dataQueryParams.value.queryType)) {
    timeFormatStr = '{h}:{i}'
  }
  if (dataQueryParams.value.dateRange?.length === 2) {
    // 两个日期是否大于1天
    const diffDays = Math.ceil((dataQueryParams.value.dateRange[1] - dataQueryParams.value.dateRange[0]) / (24 * 60 * 60 * 1000))
    if (diffDays >= 1) {
      timeFormatStr = '{y}/{m}/{d}'
    } else {
      timeFormatStr = '{h}:{i}'
    }
  }
  data.forEach((v) => {
    xA.push(proxy.parseTime(v.time, timeFormatStr))
    yA.push(v.value)
  })
  const option = {
    legend: {
      show: false
    },
    xAxis: {
      type: 'category',
      data: xA
    },
    yAxis: {
      type: 'value'
    },
    tooltip: {
      trigger: 'axis'
    },
    series: [
      {
        name: currentCard?.label,
        data: yA,
        type: 'line',
        itemStyle: {
          color: '#0bb976'
        }
      }
    ]
  }
  if ([4, 5, 6, 7, 8].includes(dataQueryParams.value.lineChartType)) {
    option.yAxis.minInterval = 1
  } else {
    option.yAxis.minInterval = null
  }
  lineChart.setOption(option)
}
// 重置数据弹窗的查询
function resetDataQuery() {
  dataQueryParams.value.isHistory = false
  dataQueryParams.value.queryType = 3
  dataQueryParams.value.lineChartType = 1
  dataQueryParams.value.dateRange = null
  handleDataQuery()
}

function validateForm(formNameRef) {
  return new Promise((resolve, reject) => {
    if (!formNameRef.value) return Promise.resolve(true)
    formNameRef.value.validate((valid, fields) => {
      if (valid) {
        return resolve()
      } else {
        console.log('🚀 ~ formNameRef.value.validate ~ fields:', fields)
        reject(new Error(fields))
      }
    })
  })
}

// 提交
async function submitForm() {
  const validFormList = [validateForm(formRef), validateForm(targetFormRef)]
  if (state.dialogType === 'audit') {
    validFormList.push(validateForm(auditFormRef))
  }
  try {
    await Promise.all(validFormList)
    let params = {}
    if (['add', 'edit'].includes(state.dialogType)) {
      params = {
        ...formParams.value
      }
      if (state.dialogType === 'add') {
        params.i18nDataAddParams = [
          ...formParams.value.activityNameList,
          ...formParams.value.activityRuleDescList
        ]
        delete params.id
      } else if (state.dialogType === 'edit') {
        params.i18nDataList = [
          ...formParams.value.activityNameList,
          ...formParams.value.activityRuleDescList
        ]
      }
      if (params.timeRange?.length === 2) {
        const startTime = new Date(params.timeRange[0]).setMinutes(0, 0, 0)
        const endTime = new Date(params.timeRange[1]).setMinutes(0, 0, 0)
        params.startTime = startTime
        params.endTime = endTime
        delete params.timeRange
      }
      if (params.activityNameList.length) {
        params.activityName = params.activityNameList[0].content
      }
      if (params.activityRuleDescList.length) {
        params.activityRuleDesc = params.activityRuleDescList[0].content
      }
      delete params.activityNameList
      delete params.activityRuleDescList
    } else if (state.dialogType === 'audit') {
      params = {
        id: formParams.value.id,
        auditResult: formParams.value.auditResult,
        rejectReason: formParams.value.rejectReason,
        secretKey: formParams.value.secretKey
      }
    }
    params.suitableType = formParams.value.suitableType
    params.suitablePhone = formParams.value.suitablePhone
    console.log(
      '🚀 ~ file: index.vue:476 ~ formRef.value.validate ~ params:',
      params
    )
    const methodList = {
      add: addMarket,
      edit: editActivity,
      audit: audit
    }
    // 裂变活动
    if (params.activityType === 'INVITE_SPLIT_USERS') {
      params.splitLevelList.forEach((item, index) => {
        params[`splitLevel${index + 1}Reward`] = +item.splitLevelReward
      })
      delete params.splitLevelReward
      delete params.splitLevelList
      params.duration = -1
    }
    if (!methodList[state.dialogType]) return
    console.log('🚀 ~ methodList[state.dialogType] ~ params:', params)
    methodList[state.dialogType](params).then((response) => {
      if (response.code === 200) {
        ElMessage.success(i18n.global.t('common.success'))
        state.dialogVisible = false
        getList()
      }
    })
  } catch (error) {
    console.log('🚀 检验不通过', error)
  }
}

async function handleAction(type, id) {
  if (type === 'data') {
    console.log('跳转到数据')
    return
  }
  const params = {
    id
  }
  if (type === 'submitAudit') {
    await submitAudit(params)
  } else if (type === 'audit') {
    await audit(params)
  } else if (type === 'delete') {
    await ElMessageBox.confirm(
      i18n.global.t('common.confirmDelete'),
      i18n.global.t('common.tips'),
      {
        confirmButtonText: i18n.global.t('common.confirm'),
        cancelButtonText: i18n.global.t('common.cancel'),
        type: 'warning'
      }
    )
    await deleteActivity(params)
  } else if (type === 'enabled') {
    try {
      await enableActivity(params)
    } catch (error) {
      const current = state.tableData.find((item) => item.id === id)
      if (current) {
        current.enabled = !current.enabled
      }
      return
    }
  }
  ElMessage.success(i18n.global.t('common.success'))
  getList()
}

// 提现档位管理相关数据
const withdrawFormData = ref({
  USD: [
    { amount: '', id: null }
  ],
  KHR: [
    { amount: '', id: null }
  ]
})

// 处理提现档位管理按钮点击
function handleWithdrawSetting() {
  state.withdrawDialogVisible = true
  // 这里可以添加获取现有档位数据的逻辑
}

// 关闭提现档位管理对话框
function handleWithdrawDialogClose() {
  state.withdrawDialogVisible = false
  // 重置表单数据
  withdrawFormData.value = {
    USD: [{ amount: '', id: null }],
    KHR: [{ amount: '', id: null }]
  }
}

// 添加档位输入框
function addWithdrawLevel(currency) {
  if (withdrawFormData.value[currency].length >= 3) {
    ElMessage.warning(`${currency}币种最多只能设置3个提现档位`)
    return
  }
  withdrawFormData.value[currency].push({ amount: '', id: null })
}

// 删除档位输入框
function removeWithdrawLevel(currency, index) {
  if (withdrawFormData.value[currency].length > 1) {
    withdrawFormData.value[currency].splice(index, 1)
    // 删除后重新验证剩余字段
    setTimeout(() => {
      validateCurrencyFields(currency)
    }, 100)
  }
}

// 处理金额输入
function handleAmountInput(value, currency, index) {
  // 根据币种限制输入格式
  let regex = ''
  if (currency === 'KHR') {
    // KHR只允许整数
    regex = /^\d*$/
  } else {
    // USD允许小数点后两位
    regex = /^\d*\.?\d{0,2}$/
  }

  if (!regex.test(value)) {
    // 如果输入不符合规则，则恢复到上一个有效值
    withdrawFormData.value[currency][index].amount = value.substring(0, value.length - 1)
  }
}

// 获取提现档位校验规则
function getWithdrawRules(currency, index) {
  return [
    {
      required: true,
      message: '请输入档位金额',
      trigger: 'blur'
    },
    {
      validator: (rule, value, callback) => {
        if (!value || value === '') {
          return callback()
        }

        const numValue = parseFloat(value)

        // 金额范围校验
        if (currency === 'USD') {
          if (numValue < 0.01 || numValue > 999.99) {
            return callback(new Error('USD档位金额必须在0.01到999.99之间'))
          }
        } else if (currency === 'KHR') {
          if (numValue < 100 || numValue > 9999999) {
            return callback(new Error('KHR档位金额必须在100到9999999之间'))
          }
        }

        // 档位递增校验
        const currentList = withdrawFormData.value[currency]
        for (let i = 0; i < currentList.length; i++) {
          if (i === index) continue

          const otherAmount = parseFloat(currentList[i].amount)
          if (isNaN(otherAmount)) continue

          if (i < index && otherAmount >= numValue) {
            return callback(new Error(`档位金额必须大于上方档位金额(${otherAmount})`))
          }
          if (i > index && otherAmount <= numValue) {
            return callback(new Error(`档位金额必须小于下方档位金额(${otherAmount})`))
          }
        }

        callback()
      },
      trigger: 'blur'
    }
  ]
}

// 验证单个字段
function validateSingleField(currency, index) {
  const fieldName = `${currency}.${index}.amount`
  withdrawFormRef.value?.validateField(fieldName)

  // 同时验证相关的其他字段（因为递增关系）
  setTimeout(() => {
    validateCurrencyFields(currency)
  }, 100)
}

// 验证某个币种的所有字段
function validateCurrencyFields(currency) {
  const currentList = withdrawFormData.value[currency]
  currentList.forEach((item, index) => {
    const fieldName = `${currency}.${index}.amount`
    withdrawFormRef.value?.validateField(fieldName)
  })
}

// 确认提现档位设置
async function confirmWithdrawSetting() {
  try {
    // 验证表单
    await withdrawFormRef.value?.validate()

    // 检查是否有空的档位
    const usdAmounts = withdrawFormData.value.USD.filter(item => item.amount && item.amount.trim() !== '')
    const khrAmounts = withdrawFormData.value.KHR.filter(item => item.amount && item.amount.trim() !== '')

    if (usdAmounts.length === 0 && khrAmounts.length === 0) {
      ElMessage.warning('请至少设置一个档位')
      return
    }

    // 这里可以添加保存档位数据的逻辑
    console.log('保存提现档位数据:', withdrawFormData.value)
    ElMessage.success('设置成功')
    handleWithdrawDialogClose()
  } catch (error) {
    console.log('表单验证失败:', error)
    ElMessage.error('请检查输入的档位信息')
  }
}

getList()
</script>
<style lang="scss">
.activity-time-range-popper {
  border: 1px solid red;

  .el-time-spinner {
    .el-time-spinner__wrapper {
      display: none;

      &:nth-child(1) {
        display: inline-block;
        width: 100%;
      }
    }
  }
}
</style>
<style lang="scss" scoped>
.direction-column {
  :deep(.el-form-item__content) {
    flex-direction: column;
    align-items: flex-start;

    .row-item+.row-item {
      margin-top: 15px;
    }
  }
}

:deep(.date-range) {
  width: 300px;
}

.data-card-list {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 20px;

  .data-card-item {
    border: 1px solid #dddddd;
    padding: 25px;
    border-radius: 20px;
    text-align: center;
    position: relative;

    &.active {
      border-color: #3478f5;
    }

    .title {
      font-size: 14px;
      word-break: break-word;
    }

    .num {
      font-size: 20px;
      margin: 10px 0;
    }

    .tooltip-icon {
      position: absolute;
      right: 7px;
      top: 25px;
    }
  }
}

.chart-container {
  width: 100%;
  height: 400px;
}

.withdraw-setting-container {
  .withdraw-tip {
    background-color: #f5f7fa;
    padding: 12px 16px;
    border-radius: 8px;
    color: #606266;
    font-size: 14px;
    margin-bottom: 24px;
    border-left: 4px solid #409eff;
  }

  .currency-section {
    margin-bottom: 32px;

    .currency-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      margin: 0 0 16px 0;
    }

    .withdraw-level-item {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      .amount-input {
        flex: 1;
        margin-right: 12px;
      }

      .action-btn {
        flex-shrink: 0;
      }
    }
  }
}
</style>
