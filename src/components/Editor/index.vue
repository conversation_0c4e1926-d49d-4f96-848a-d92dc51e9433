<template>
  <div class="my-wangeditor">
    <Toolbar
      style="border-bottom: 1px solid #ccc"
      :editor="editorRef"
      :defaultConfig="toolbarConfig"
      :mode="mode"
    />
    <Editor
      style="height: 400px; overflow-y: hidden"
      v-model="valueHtml"
      :defaultConfig="editorConfig"
      :mode="mode"
      @onCreated="handleCreated"
      @onChange="handleChange"
      @onDestroyed="handleDestroyed"
      @onFocus="handleFocus"
      @onBlur="handleBlur"
      @customAlert="customAlert"
      @customPaste="customPaste"
    />
  </div>
</template>

<script setup>
import '@wangeditor/editor/dist/css/style.css' // 引入 css

import {
  onBeforeUnmount,
  ref,
  shallowRef,
  defineProps,
  defineEmits,
  watch,
  nextTick
} from 'vue'
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import { i18nChangeLanguage } from '@wangeditor/editor'
import { getToken } from '@/utils/auth'
import axios from 'axios'
import { ElMessage } from 'element-plus'
import i18n from "../../i18n";

const props = defineProps({
  modelValue: String
})
watch(
  () => props.modelValue,
  (val) => {
    nextTick(() => {
      valueHtml.value = val
      i18nChangeLanguage(i18n.global.locale === 'en' ? 'en' : 'zh-CN' )
    })
  },
  {
    immediate: true
  }
)
const emit = defineEmits(['change', 'blur'])
// 编辑器实例，必须用 shallowRef
const editorRef = shallowRef()

// 内容 HTML
const valueHtml = ref()
const mode = ref('default')

const uploadImgUrl = ref(import.meta.env.VITE_APP_BASE_API + '/file/upload') // 上传的图片服务器地址
const toolbarConfig = {}
const editorConfig = {
  MENU_CONF: {}
}
editorConfig.placeholder = i18n.global.t('common.pleaseEnterContent')
// 修改 uploadImage 菜单配置
editorConfig.MENU_CONF.uploadImage = {
  async customUpload (file, insertFn) {
    const res = await uploadImageHandler(file)
    insertFn(res.data)
  }
}

editorConfig.MENU_CONF.uploadVideo = {
  // 自定义上传
  async customUpload (file, insertFn) {
    // file 即选中的文件
    const res = await uploadImageHandler(file)
    const baseImg = await indvideocover(res.data)
    const post = await uploadImageHandler(baseImg)
    insertFn(res.data, post.data)
  }
}

function dataURLtoFile (dataBase64, filename) {
  const arr = dataBase64.split(',')
  const mime = arr[0].match(/:(.*?);/)[1]
  const bstr = atob(arr[1])
  let n = bstr.length
  const u8arr = new Uint8Array(n)
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n)
  }
  return new File([u8arr], filename, { type: mime })
}

function indvideocover (url) {
  // 创建video元素
  const video = document.createElement('video')
  video.src = url
  video.setAttribute('crossOrigin', 'Anonymous') // 处理跨域
  video.setAttribute('preload', 'auto') // auto|metadata|none
  // 等待视频加载完成
  return new Promise((resolve, reject) => {
    video.addEventListener('loadeddata', () => {
      // 创建canvas元素
      const canvas = document.createElement('canvas')
      canvas.width = video.videoWidth
      canvas.height = video.videoHeight

      // 将视频第一帧绘制到canvas上
      const ctx = canvas.getContext('2d')

      ctx.drawImage(video, 0, 0, canvas.width, canvas.height)

      // 将canvas图像转换为base64格式的数据URI
      const dataBase64 = canvas.toDataURL()
      if (dataBase64) {
        const imgFile = dataURLtoFile(dataBase64, `${new Date().getTime()}.png`)
        resolve(imgFile)
      }
    })

    // 如果视频加载出错，则返回错误信息
    video.addEventListener('error', (err) => {
      reject(err, `Failed to load video: ${url}`)
    })
  })
}

function uploadImageHandler (file, insertFn) {
  return new Promise((resolve, reject) => {
    const formData = new FormData()
    formData.append('file', file) // 后端接收文件的API接口
    axios
      .post(uploadImgUrl.value, formData, {
        headers: {
          'Content-type': 'multipart/form-data',
          Authorization: 'Bearer ' + getToken()
        }
      })
      .then((res) => {
        resolve(res)
      })
      .catch(() => {
        // 出现错误时的处理
        ElMessage.error('上传失败')
      })
  })
}

// 组件销毁时，也及时销毁编辑器
onBeforeUnmount(() => {
  const editor = editorRef.value
  if (editor == null) return
  editor.destroy()
})

const handleCreated = (editor) => {
  editorRef.value = editor // 记录 editor 实例，重要！
}
const handleChange = (editor) => {
  // console.log('change:', editor.children)
  emit('change', valueHtml.value)
  emit('update:modelValue', valueHtml.value)
}
const handleDestroyed = (editor) => {
  // console.log('destroyed', editor)
}
const handleFocus = (editor) => {
  // console.log('focus', editor)
}
const handleBlur = (editor) => {
  // console.log('blur', editor)
  emit('blur', valueHtml.value)
  emit('update:modelValue', valueHtml.value)
}
const customAlert = (info, type) => {
  // alert(`【自定义提示】${type} - ${info}`)
}

const customPaste = async (editor, event, callback) => {
  console.log('ClipboardEvent 粘贴事件对象', event.clipboardData)
  const clipboardData = event.clipboardData
  // const html = clipboardData.getData('text/html') // 获取粘贴的 html
  // const text = clipboardData.getData('text/plain') // 获取粘贴的纯文本
  // const rtf = clipboardData.getData('text/rtf') // 获取 rtf 数据（如从 word wsp 复制粘贴）

  const items = clipboardData.items
  const typeList = Array.from(items).map((i) => i.type)
  for (let i = 0; i < items.length; i++) {
    const item = items[i]
    console.log(item, item.type)
    // html类型
    if (item.type.indexOf('html') > -1) {
      // if (item.type.indexOf('text/plain') > -1 && item.type.indexOf('html') > -1) {
      const html = clipboardData.getData('text/html')
      editor.dangerouslyInsertHtml(html)
    }
    // word文档
    // if (item.type.indexOf('rtf') > -1 && typeList.length === 4) {
    //   const text = clipboardData.getData('text/rtf')
    //   editor.dangerouslyInsertHtml(text)
    // }

    // 图片类型，则上传图片
    if (item.type.indexOf('image') > -1 && typeList.length === 2) {
      const file = item.getAsFile()

      console.log(file)
      // 调用自定义的上传图片方法，上传图片并插入到编辑器中
      const res = await uploadImageHandler(file)
      console.log('resresresresres', res)
    }
  }
  // 返回 false ，阻止默认粘贴行为
  event.preventDefault()
  callback(true) // 返回值（注意，vue 事件的返回值，不能用 return）

  // 返回 true ，继续默认的粘贴行为
  // callback(true)
}
</script>

<style scoped lang="scss">
.my-wangeditor {
  border: 1px solid #ccc;
  :deep(.w-e-text-container) {
    a {
      color: #409eff;
    }
    strong,
    b {
      font-weight: bold;
    }
    h1 {
      font-size: 1.7em;
    }
    h2 {
      font-size: 1.5em;
    }
    h3 {
      font-size: 1.3em;
    }
    h4 {
      font-size: 1.2em;
    }
    h5 {
      font-size: 1.05em;
    }
    .italic {
      font-style: italic;
    }
    video, img {
      width: 100%;
    }
  }
}
</style>
