import cache from '@/plugins/cache'
import store from '@/store'
import { getToken } from '@/utils/auth'
import errorCode from '@/utils/errorCode'
import { blobValidate, tansParams } from '@/utils/ruoyi'
import axios from 'axios'
import { ElLoading, ElMessage, ElMessageBox } from 'element-plus'
import { saveAs } from 'file-saver'
import JSONBIG from 'json-bigint'
import i18n from '../i18n'
import Cookies from 'js-cookie'

let downloadLoadingInstance
// 是否显示重新登录
export const isRelogin = { show: false }

axios.defaults.headers['Content-Type'] = 'application/json;charset=utf-8'
// 创建axios实例
const service = axios.create({
  // axios中请求配置有baseURL选项，表示请求URL公共部分
  baseURL: import.meta.env.VITE_APP_BASE_API,
  // 超时
  timeout: 150000
})
// 精度问题
axios.defaults.transformResponse = [
  function (data) {
    const json = JSONBIG({
      storeAsString: true
    })
    return json.parse(data)
  }
]

// request拦截器
service.interceptors.request.use(config => {
  // 是否需要设置 token
  const isToken = (config.headers || {}).isToken === false
  // 是否需要防止数据重复提交
  const isRepeatSubmit = (config.headers || {}).repeatSubmit === false
  if (getToken() && !isToken) {
    config.headers.Authorization = 'Bearer ' + getToken() // 让每个请求携带自定义token 请根据实际情况自行修改
  }
  // get请求映射params参数
  if (config.method === 'get' && config.params) {
    let url = config.url + '?' + tansParams(config.params)
    url = url.slice(0, -1)
    config.params = {}
    config.url = url
  }
  if (!isRepeatSubmit && (config.method === 'post' || config.method === 'put')) {
    const requestObj = {
      url: config.url,
      data: typeof config.data === 'object' ? JSON.stringify(config.data) : config.data,
      time: new Date().getTime()
    }
    const sessionObj = cache.session.getJSON('sessionObj')
    if (sessionObj === undefined || sessionObj === null || sessionObj === '') {
      cache.session.setJSON('sessionObj', requestObj)
    } else {
      const s_url = sessionObj.url // 请求地址
      const s_data = sessionObj.data // 请求数据
      const s_time = sessionObj.time // 请求时间
      const interval = 1000 // 间隔时间(ms)，小于此时间视为重复提交
      if (s_data === requestObj.data && requestObj.time - s_time < interval && s_url === requestObj.url) {
        const message = i18n.global.t('common.reSubmit')
        console.warn(`[${s_url}]: ` + message)
        return Promise.reject(new Error(message))
      } else {
        cache.session.setJSON('sessionObj', requestObj)
      }
    }
  }
  let lang = Cookies.get('language') || 'en-US'
  if (lang.includes('zh')) {
    lang = 'zh-CN'
  } else {
    lang = 'en-US'
  }
  config.headers.lang = lang
  config.headers.timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone
  return config
}, error => {
  console.log(error)
  Promise.reject(error)
})

// 响应拦截器
service.interceptors.response.use(res => {
  // 未设置状态码则默认成功状态
  const code = res.data.code || 200
  // 获取错误信息
  const msg = res.data.msg || res.data.message || errorCode.default || errorCode[code]
  // 二进制数据则直接返回
  if (res.request.responseType === 'blob' || res.request.responseType === 'arraybuffer') {
    return res.data
  }
  if (code === 401) {
    if (!isRelogin.show) {
      isRelogin.show = true
      ElMessageBox.confirm(i18n.global.t('login.loginExpired'), i18n.global.t('login.systemPrompt'), {
        confirmButtonText: i18n.global.t('login.reLogin'),
        cancelButtonText: i18n.global.t('common.cancel'),
        type: 'warning'
      }
      ).then(() => {
        isRelogin.show = false
        store.dispatch('LogOut').then(() => {
          location.href = '/'
        })
      }).catch(() => {
        isRelogin.show = false
      })
    }
    return Promise.reject(i18n.global.t('login.invalidLogin'))
  } else if (code === 500) {
    ElMessage({
      message: msg,
      type: 'error'
    })
    return Promise.reject(new Error(msg))
  } else if (code !== 200) {
    ElMessage({
      message: msg,
      type: 'error'
    })
    return Promise.reject('error')
  } else {
    return Promise.resolve(res.data)
  }
},
error => {
  console.log('err' + error)
  let { message } = error
  if (message === 'Network Error') {
    message = i18n.global.t('common.interfaceNetworkError')
  } else if (message.includes('timeout')) {
    message = i18n.global.t('common.interfaceTimeout')
  } else if (message.includes('Request failed with status code')) {
    message = i18n.global.t('common.interfaceException') + message.substr(message.length - 3)
  }
  ElMessage({
    message: message,
    type: 'error',
    duration: 5 * 1000
  })
  return Promise.reject(error)
}
)

// 通用下载方法
export function download (url, params, filename) {
  downloadLoadingInstance = ElLoading.service({ text: i18n.global.t('common.downloadWait'), background: 'rgba(0, 0, 0, 0.7)' })
  return service.post(url, params, {
    transformRequest: [(params) => { return tansParams(params) }],
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    responseType: 'blob'
  }).then(async (data) => {
    const isLogin = await blobValidate(data)
    if (isLogin) {
      const blob = new Blob([data])
      saveAs(blob, filename)
    } else {
      const resText = await data.text()
      const rspObj = JSON.parse(resText)
      const errMsg = errorCode[rspObj.code] || rspObj.msg || errorCode.default
      ElMessage.error(errMsg)
    }
    downloadLoadingInstance.close()
  }).catch((r) => {
    console.error(r)
    ElMessage.error(i18n.global.t('common.downloadError'))
    downloadLoadingInstance.close()
  })
}

export default service
