export const limitWordCounts = (string = '', type, count = 10)=> {
    if (!string) return ''
    if (type === 'arrayString') {
      const ids = string?.split(',')
      const idsString = ids.slice(0, 1).join(' , ')
      return idsString.slice(0, count) + (idsString?.length > count ? '...' : '')
    } else {
      const zhRegExp = /[\u3040-\u30ff\u3400-\u4dbf\u4e00-\u9fff\uf900-\ufaff\uff66-\uff9f]/g
      if (zhRegExp.test(string)) {
        return string?.slice(0, count) + (string?.length > count ? '...' : '')
      } else {
        const arrayString = string?.split(' ')
        return arrayString?.length > 1
          ? string?.split(' ').slice(0, count).join(' ') + (string?.split(' ')?.length > count ? '...' : '')
          : string?.slice(0, count) + (string?.length > count ? '...' : '')
      }
    }
  }