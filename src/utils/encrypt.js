import CryptoJS from 'crypto-js'
import axios from 'axios'

/**
 * 登录密码 用的加密方法
 * @return {string}
 */

// 十六位十六进制数作为密钥
export const localAesKey = 'weeKeejjLL123.VB'
export const localAesIv = 'weeKeejjLL123.VB'
export const aesKey = CryptoJS.enc.Utf8.parse('weeKeejjLL123.VB')
// 十六位十六进制数作为密钥偏移量
export const aesIv = CryptoJS.enc.Utf8.parse('weeKeejjLL123.VB')

export function AesEncrypt (word) {
  const srcs = CryptoJS.enc.Utf8.parse(word)
  const encrypted = CryptoJS.AES.encrypt(srcs, aesKey, {
    iv: aesIv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7
  })
  return encrypted.toString()
}

export const localAesEncode = function (word) {
  const srcs = CryptoJS.enc.Utf8.parse(word)
  let encrypted = ''

  encrypted = CryptoJS.AES.encrypt(srcs, aesKey, {
    iv: aesIv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7
  })
  return encrypted.ciphertext.toString(CryptoJS.enc.Base64)
}

// 解密方法
export const localAesDecode = function (word) {
  try {
    const encryptedHexStr = CryptoJS.enc.Base64.parse(word)
    const srcs = CryptoJS.enc.Base64.stringify(encryptedHexStr)
    let decrypt = ''
    decrypt = CryptoJS.AES.decrypt(srcs, aesKey, {
      iv: aesIv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    })
    const result = decrypt.toString(CryptoJS.enc.Utf8).toString()
    if (!result) {
      return this.$t('msg.messageInvalid')
    }
    return result
  } catch (e) {
    return this.$t('msg.messageInvalid')
  }
}

function ArrayBufferToWordArray (arrayBuffer) {
  const u8 = new Uint8Array(arrayBuffer, 0, arrayBuffer.byteLength)
  const len = u8.length
  const words = []
  for (let i = 0; i < len; i += 1) {
    words[i >>> 2] |= (u8[i] & 0xff) << (24 - (i % 4) * 8)
  }
  return CryptoJS.lib.WordArray.create(words, len)
}

/**
 * AES加密文件
 * @param {*} file
 * @param {*} key
 * @returns
 */

export function AesEncryptFile (file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onerror = (err) => {
      reject(err)
    }
    reader.onload = () => {
      const wordArray = ArrayBufferToWordArray(reader.result)
      const encrypted = CryptoJS.AES.encrypt(wordArray, aesKey, {
        iv: aesIv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
      })
      const uInt8Array = convertWordArrayToUint8Array(encrypted.ciphertext)
      const blob = new Blob([uInt8Array])
      const encryptFile = new File([blob], file.name)
      resolve(encryptFile)
    }
    reader.readAsArrayBuffer(file)
  })
}
/**
 * 解密文件方法
 * @param data
 * @returns {string}
 */
export function AesDecryptFile ({
  file,
  aesKey,
  aesIv,
  fileName
}) {
  return new Promise(resolve => {
    const reader = new FileReader()
    reader.readAsArrayBuffer(file)
    reader.onload = function (evt) {
      const fileString = evt.target.result
      const key = CryptoJS.enc.Utf8.parse(aesKey)
      const iv = CryptoJS.enc.Utf8.parse(aesIv)

      const wordArray = CryptoJS.lib.WordArray.create(fileString)
      const dcBase64String = wordArray.toString(CryptoJS.enc.Base64)

      const decrypted = CryptoJS.AES.decrypt(dcBase64String, key, { iv: iv, mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7 })

      const uInt8Array = convertWordArrayToUint8Array(decrypted)

      const fileDec = new Blob([uInt8Array])
      resolve(fileDec)
    }
    reader.onerror = function () {
    }
  })
}

function convertWordArrayToUint8Array (wordArray) {
  // eslint-disable-next-line no-prototype-builtins
  const arrayOfWords = wordArray.hasOwnProperty('words')
    ? wordArray.words
    : []
  // eslint-disable-next-line no-prototype-builtins
  const length = wordArray.hasOwnProperty('sigBytes')
    ? wordArray.sigBytes
    : arrayOfWords.length * 4
  const uInt8Array = new Uint8Array(length)
  let index = 0
  let word
  let i
  for (i = 0; i < length; i++) {
    word = arrayOfWords[i]
    uInt8Array[index++] = word >> 24
    uInt8Array[index++] = (word >> 16) & 0xff
    uInt8Array[index++] = (word >> 8) & 0xff
    uInt8Array[index++] = word & 0xff
  }
  return uInt8Array
}

// url进行aes文件解密
export function handleUrl (list) {
  return new Promise((resolve, reject) => {
    if (!list.length) {
      resolve(list)
    }
    list.forEach((fileItem, index) => {
      if (fileItem.fileKey) {
        const x = new window.XMLHttpRequest()
        x.open('GET', fileItem.url, true)
        x.responseType = 'blob'
        x.onload = (blob) => {
          const blobFile = blob.target.response
          const file = new window.File([blobFile], `${fileItem.name}`)
          const aesKey = fileItem.fileKey.split(',')[0]
          const aesIv = fileItem.fileKey.split(',')[1]
          const params = {
            file,
            aesKey,
            aesIv,
            fileName: fileItem.name
          }
          AesDecryptFile(params).then(aesDecryptFile => {
            if (Object.prototype.toString.call(aesDecryptFile) === '[object File]' || Object.prototype.toString.call(aesDecryptFile) === '[object Blob]') {
              fileItem.sourceUrl = fileItem.url
              fileItem.url = window.URL.createObjectURL(aesDecryptFile)
            }
            // fileItem.url = new File([aesDecryptFile], fileItem.name)
            if (index === list.length - 1) {
              resolve(list)
            }
          })
        }
        x.send()
      } else {
        if (index === list.length - 1) {
          resolve(list)
        }
      }
    })
  })
}
