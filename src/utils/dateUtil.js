/**
 *  今天
 * @returns
 */
export function getTodayTimestampRange () {
  const today = new Date()
  const start = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 0, 0, 0)
  const end = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1, 0, 0, 0)
  return {
    startTime: start.getTime(),
    endTime: end.getTime()
  }
}
/**
 *  N天
 * @returns
 */
export function getNDaysTimestampRange (n) {
  const today = new Date()
  const endTimestamp = new Date(
    today.getFullYear(),
    today.getMonth(),
    today.getDate(),
    0,
    0,
    0
  ).getTime() // 获取今天零点的时间戳
  const startTimestamp = endTimestamp - n * 24 * 60 * 60 * 1000 // 计算N天前零点的时间戳
  return {
    startTime: startTimestamp,
    endTime: endTimestamp
  }
}
