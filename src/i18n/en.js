export default {
  auth: {
    title: "Scan Code Login",
    authTitle: "Please complete real name authentication first",
    country: "country",
    idType: "ID type",
    IDCard: "ID card",
    passport: "passport",
    certNumber: "ID NO",
    certFrontPhoto: "Front photo of ID",
    certBackPhoto: "Back photo of ID",
    holdingCertPhoto: "Handheld ID photo",
    goAuth: "Go for authentication",
    fullName: "full name",
    authenticationType: "Certification type",

    pAuth: "Personal authentication",
    cAuth: "Enterprise certification",

    enterpriseRegCountry: "Country of Enterprise Registration",
    enterpriseRegLocation: "Enterprise registration location",
    enterpriseRegCertPhoto: "Enterprise registration certificate",
    enterpriseRegCode: "Enterprise unique registration code",
    submit: "Submit",
    reSubmit: "Resubmit",

    check: "check",

    reviewResult: "Review result",

    reasonRejection: "Reason for rejection",

    approved: "Approved",
    reject: "Reject",
  },
  channelReview: {
    channelId: "channel ID",
    avatar: "Channel avatar",
    Name: "Channel Name",
    Type: "Channel Type",
    Link: "Channel Link",
    time: "Creation time",
    certType: "Certification type",
    status: "Certification status",

    inReview: "In Review",
    notPassed: "Declined",
    passed: "Completed",
    expired: "Expired",

    personal: "Personal Verified",
    enterprise: "Enterprise Verified",
    weiVerify: "Unverified",
    cancelVerify: "Cancel Verification",
    auditing: "Auditing",

    application: "application",

    personalApply: "Personal authentication application",
    enterpriseApply: "Enterprise certification application",
    submitSuccess: "Submitted successfully",
    enterpriseInfo: "Enterprise Information",
    contactInfo: "Contact Information",
  },
  login: {
    title: "Management System",
    systemPrompt: "System Prompt",
    logIn: "Log in",
    logIng: "Log in...",
    reLogin: "Re-login",
    loginExpired:
      "Your login status has expired. You can stay on this page or log in again.",
    invalidLogin:
      "Invalid session or session has expired, please log in again。",
    username: "Username",
    pleaseInputUsername: "please input username",
    rememberPassword: "remember password",
    inputPassword: "input password",
    pleaseInputPassword: "please input password",
    verifyCode: "verify code",
    pleaseInputVerifyCode: "please input verify code",
    register: "register",
    inputNewPassword: "Input New Password",
    confirmNewPassword: "Confirm New Password",
    passwordNotSame: "The two passwords you input are not the same",
  },
  common: {
    internalError: "Internal server error, please wait.",
    authError: "Authentication failed, unable to access system resources.",
    noPermit: "Current operation has no permission.",
    resourceNotExist: "Access to the resource does not exist.",
    systemError: "System unknown error, please feedback to the administrator.",
    reSubmit: "Data is being processed, please do not resubmit.",
    select: "Please select",
    pleaseSelectState: "Please Select",
    pleaseSelectType: "Please select the type",
    pleaseSelectDeleteItem: "Please Select Delete Item",
    selectDate: "Select Date",
    startDate: "Start Date",
    startTime: "Start time",
    endTime: "End time",
    endDate: "End Date",
    to: "to",
    all: "All",
    search: "Search",
    save: "Save",
    reset: "Reset",
    export: "Export",
    cancel: "Cancel",
    confirm: "Confirm",
    add: "Add",
    tips: "Tips",
    confirmUnfrozen: "confirm unfrozen?",
    confirmCancelChannelVerify: "Sure to cancel the channel's verification?",
    confirmCancelUserVerify: "Sure to cancel the user's verification?",
    confirmLogoutUser: "Confirm to let this user log out?",
    pleaseEnter: "Please Enter",
    state: "State",
    operate: "Operate",
    phoneNumber: "Phone Number",
    createdTime: "Created Time",
    delete: "Delete",
    confirmDelete: "Confirm Delete",
    edit: "Edit",
    clear: "Clear",
    success: "Success!",
    failed: "Failed!",
    post: "Post",
    details: "Details",
    monday: "Monday",
    tuesday: "Tuesday",
    wednesday: "Wednesday",
    thursday: "Thursday",
    friday: "Friday ",
    saturday: "Saturday",
    sunday: "Sunday",
    userId: "User ID",
    userNickname: "User nickname",
    caption: "Caption",
    hideSearch: "Hide search",
    showSearch: "Show search",
    refresh: "Refresh",
    text: "Text",
    image: "Image",
    voice: "Voice",
    video: "Video",
    file: "File",
    redEnvelope: "red envelope",
    other: "Other",
    remark: "Remark",
    handleTime: "Handle Time",
    handleClientId: "Handle Client",
    days: "Handle Result",
    submit: "Submit",
    cannotBeEmpty: "Cannot be empty",
    formatError: "Format error",
    dealWith: "Deal With",
    return: "Return",
    on: "On",
    off: "Off",
    downloadWait: "Downloading data, please wait.",
    yes: "Yes",
    no: "No",
    close: "Close",
    viewMore: "View More",
    view: "View",
    viewDetails: "View Details",
    freeze: "Freeze",
    unfreeze: "Unfreeze",
    freezeMsg: "Frozen",
    unfreezeMsg: "Already unfrozen",
    cancelVerifyMsg: "Verification Cancelled",
    interfaceNetworkError: "Backend interface connection exception",
    interfaceTimeout: "System interface request timeout",
    interfaceException: "System interface exception：",
    pleaseEnterContent: "Please enter content",
    downloadError: "Download Error!",
    pleaseEnterNumber: "Please Enter Number",
    keywordExceed: "The keyword must not exceed 300 characters.",
    replyExceed: "The reply content must not exceed 1000 characters.",
    doNotRepost: "Please do not repost it!",
    charactersExceeds: "The number of characters exceeds the limit:",
    numberExceeds: "The entered number is out of range:",
    userCanNotEmpty: "User can not be empty",
    pleaseSelectChannel: "Please Select Channel",
    formatIncorrect: "The format is incorrect",
    pleaseSelectUser: "Please select the user to assign",
    strLengthRange: "String length range: ",
    confirmBanUser: "Confirm to ban this user?",
    confirmPost: "Confirm post?",
    enable: "Enable",
    disable: "Disable",
    content: "Content",
  },
  tool: {
    fileFormatError: "File format is incorrect, please upload.",
    fileSizeLimit: "The file size cannot exceed",
    fileCountLimit: "The file count cannot exceed",
    uploadWait: "The file is being uploaded, please wait...",
    uploadFail: "File upload failed",
    waitSetLayOut: "Setting layout size, please wait...",
    waitSaveLocal: "Saving to local, please wait...",
    waitClearCache: "Clearing cache and refreshing settings, please wait...",
    waitLoadCacheMonitor: "Loading cache monitoring data, please wait!",
    waitLoadServiceMonitor: "Loading service monitoring data, please wait!",
    upload: "upload",
    sizeLimit: "size limit:",
    formats: " formats:",
    files: "files",
  },
  homePage: {
    title: "Management System",
    homePage: "Homepage",
    personalCenter: "personal center",
    layoutSettings: "Layout Settings",
    logout: "Logout",
    confirmLogout: "Are you sure you want to logout and exit the system？",
    personalInformation: "Personal Information",
    userName: "User Name",
    mobilePhoneNumber: "Mobile Phone Number",
    userEmail: "User Email",
    department: "Department",
    role: "Role",
    creationDate: "Creation Date",
    basicInformation: "Basic Information",
    email: "Email",
    sex: "Sex",
    select: "Select",
    man: "Man",
    woman: "Woman",
    save: "Save",
    close: "Close",
    modifyPwd: "Modify Password",
    oldPassword: "Old Password",
    newPassword: "New Password",
    confirmPassword: "Confirm Password",
    pleaseConfirmYourPassword: "Please confirm your password",
    pwdNotSame: "The two passwords entered are inconsistent",
    changeAvatar: "Change Avatar",
    uploadAvatar: "Click to upload avatar",
    authRole: "Auth role",
    authUser: "Auth User",
    dicData: "Dic Data",
    jobLog: "Job Log",
    genEdit: "Gen Edit",
    totalUsers: "Total Users",
    newUsersToday: "New Users(Today)",
    newUsersYesterday: "New Users(Yesterday)",
    activeUsersToday: "Active Users(Today)",
    activeUsersYesterday: "Active Users(Yesterday)",
    registeredUsers: "Registered Users",
    registeredUsersToday: "Registered Users(Today)",
    yesterday: "Yesterday",
    today: "Today",
    sevenDays: "7 Days",
    threeMonths: "3 Months",
    oneYear: "1 Year",
    lastSevenDays: "7th Day",
    lastThirtyDays: "30th Day",
    activeUsersEcharts: "Active Users",
    activeUsers: "Active Users",
    retentionRate: "Retention Rate",
    nextDayRetentionRate: "Next-day Retention Rate",
    sevenDayRetentionRate: "7-day Retention Rate",
    thirtyDayRetentionRate: "30-day Retention Rate",
    thirtyDays: "Thirty days",
    ninetyDays: "Ninety days",
    unknown: "Unknown",
    themeStyleSettings: "Theme Style Settings",
    themeColor: "Theme Color",
    systemLayoutConfiguration: "System Layout Configuration",
    turnOn: "Turn on",
    fix: "Fix",
    display: "Display",
    dynamicTitle: "Dynamic Title",
    layoutSize: "Layout Size",
    large: "Large",
    default: "Default",
    small: "Small",
    moreMenu: "More",
    pwdLengthLimit: "The user password length must be between 5 and 20",
    nameLengthLimit: "The user name length must be between 5 and 20",
    notFoundErr: "404 ERROR!",
    notFoundPage: "Page not found!",
    returnHome: "Return to homepage",
    errDesc:
      "Sorry, the page you are looking for does not exist. Try checking the URL for errors, then press the refresh button on your browser or try finding other content within our application.",
  },
  userStatistic: {
    dataStatistics: "Data Statistics",
    retentionUsers: "Retention Users",
    source: "Source",
    selectSource: "Select source",
    official: "Official",
    unknown: "Unknown",
    period: "Period",
    date: "Date",
    sameDay: "Same Day",
    nextDay: "Next Day",
    the7thDay: "7th Day",
    the30thDay: "30th Day",
  },
  userManage: {
    contacts: "Contacts",
    usersManagement: "Users Management",
    nickname: "Nickname",
    phoneNumber: "Phone Number",
    pleaseEnter: "Please Enter",
    state: "State",
    pleaseSelectStatus: "Please Select",
    normal: "Normal",
    deactivated: "Deactivated",
    appOnlineState: "APP Online State",
    online: "Online",
    offline: "Offline",
    pcOnlineState: "PC Online State",
    pleaseSelectOnlineStatus: "Please Select",
    registrationTime: "Registration Time",
    startDate: "Start Date",
    endDate: "End Date",
    country: "Country",
    avatar: "Avatar",
    friendCount: "Contacts",
    lastOnlineTime: "Last Online Time",
    FrozenUntil: "Frozen Until",
    Forever: "Permanent",
    reportedTimes: "Reported Times",
    operate: "Operate",
    registrationRecord: "Registration Record",
    logoutUser: "Logout",
    friendList: "Contacts",
    yes: "Yes",
    no: "No",
    onlineTime: "Online Time",
    offlineTime: "Offline Time",
    loginIp: "Login IP",
    loginIpAddress: "Login IP Address",
    loginDevice: "Login Device",
    systemVersion: "System Version",
    mosappVersion: "MosApp Version",
    frozenTime: "Frozen Time",
    selectFrozenTime: "Select frozen time",
    threeHours: "3 Hours",
    twelveHours: "12 Hours",
    oneDay: "1 Day",
    threeDays: "3 Days",
    oneWeek: "1 Week",
    permanent: " Permanent",
    oneMonth: "1 Month",
    threeMonths: "3 Months",
    reason: "Reason",
    harassmentAndBullying: "Harassment and Bullying",
    inappropriateRemarks: "Inappropriate Remarks",
    violationOfRegulation: "Violation of Regulation",
    pornographicViolence: "Pornographic Violence",
    other: "Other",
    firstName: "FirstName",
    lastName: "LastName",
    dialCode: "DialCode",
    password: "Password",
    cancelAuth: "Uncheck user authorized data items?",
    userType: "User Type",
    normalUser: "Registered",
    guest: "Unregistered",
  },
  groupManage: {
    groupManagement: "Group Management",
    isPaid: "Paid",
    groupID: "Group ID",
    groupNickName: "Group Name",
    groupOwnerId: "Group Owner ID",
    normal: "Normal",
    deleted: "Deleted",
    groupAvatar: "Group Avatar",
    groupName: "Group Name",
    groupMembers: "Group Members",
    encryptionOrNot: "Encryption or not",
    ordinary: "Ordinary",
    encrypted: "Encrypted",
    private: "Private",
    public: "Public",
    groupStyle: "Group Style",
    memberNickname: "Member Nickname",
    memberID: "Member ID",
    country: "Country",
    mutedState: "Muted State",
    mute: "Mute",
    unmute: "Unmute",
    memberAvatar: "Member Avatar",
    joinedTime: "Joined Time",
    role: "Role",
    memberType: "Member Type",
    memberTypeUser: "User",
    memberTypeRobot: "Bot",
    owner: "Owner",
    member: "Member",
    admin: "Admin",
    reportedTimes: "Reported Times",
    banSpeech: "Ban Speech",
    pleaseSelectAuditState: "Please Select Audit State",
  },
  channelManage: {
    channelManagement: "Channel Management",
    channelType: "Channel Type",
    pleaseSelect: "Please Select",
    public: "Public",
    private: "Private",
    channelName: "Channel Name",
    channelId: "Channel ID",
    channelLink: "Channel Link",
    ownerID: "Owner ID",
    ownerName: "Owner Name",
    channelState: "Channel State",
    normal: "Normal",
    deleted: "Deleted",
    channelAvatar: "Channel Avatar",
    subscriber: "Subscriber",
    postsNumber: "Posts Number",
    officialChannelOrNot: "Official channel or not",
    state: "State",
    operate: "Operate",
    subscribers: "Subscribers",
    userID: "User ID",
    userName: "User Name",
    phoneNumber: "Phone Number",
    avatar: "Avatar",
    noAvatar: "No avatar available",
    country: "Country",
    subscriberTime: "Subscriber Time",
    applyTime: "Application Time",
    post: "Post",
    sendTime: "Post Time",
    postType: "Post Type",
    text: "Text",
    image: "Image",
    voice: "Voice",
    video: "Video",
    file: "File",
    postLink: "Post Link",
    postID: "Post ID",
    postTime: "Post Time",
    views: "Views",
    likes: "Likes",
    comments: "Comments",
    collections: "Collections",
    shares: "Shares",
    clickToCopy: "Click to copy",
    rewardCount: "reward count",
    rewardAmoutn: "rewad amount",
    fee: "fee",
    actualAmount: "actual amount",
  },
  crawlerManage: {
    crawlerManagement: "Crawler Management",
    targetManagement: "Target Management",
    platform: "Platform",
    channelName: "Channel Name",
    channelLink: "Channel Link",
    enterChannelName: "Please enter channel name",
    enterChannelLink: "Please enter channel link",
    switchState: "Switch State",
    enabledState: "Enabled State",
    contents: "Contents",
    crawlOnce: "Crawl Once",
    contentDetails: "Content Details",
    postTimeOriginalPlatform: "Post Time(Original Platform)",
    crawlTime: "Crawl Time",
    crawlContent: "Crawl Content",
    viewDetails: "View Details",
    video: "[Video]",
    file: "File",
    sureToDeleteIt: "Sure to delete it",
    contentLibrary: "Content Library",
    belongChannel: "Channel",
    sureToDeleteThisMessage: "Sure to delete this message",
    postedTimes: "Posted Times",
    messageContent: "Message Content",
    attachment: "Attachment",
    postRecord: "Post Record",
    subscribersCount: "Subscribers",
    selectChannel: "Select Channel",
    subscribers: "Subscribers",
    sureToPostThisMessage: "Sure to post this message",
    botManagement: "Bot Management",
    mosappChannelName: "MosApp Channel Name",
    postingRule: "Posting Rule",
    inTime: "In-time",
    regularly: "Regularly",
    addTime: "Add Time",
    enalbedState: "Enalbed State",
    posts: "Posts",
    channel: "Channel",
    source: "Source",
    isEnabled: "Enabled State",
    postDate: "Post Date",
    postTime: "Post Time",
    postState: "Post State",
    successfully: "Successfully",
    posting: "Posting",
    failed: "Failed",
    addTarget: "Add Target",
    selectPlatform: "Select Platform",
    confirmGetChannelData: "Confirm Get Channel Data?",
    successAndWait: "Success, Please wait",
    addPostTime: "Add Post Time",
  },
  mosAssist: {
    mosAssistant: "Mos Assistant",
    messagesOfMosAssistant: "Messages of Mos Assistant",
    messageDeleted: "[Message Deleted]",
    userName: "User Name",
    userID: "User ID",
    state: "State",
    pending: "Pending",
    processed: "Processed",
    feedbackTime: "Feedback Time",
    feedbackMessage: "Feedback Message",
    viewMore: "View More",
    dealWith: "Deal With",
    hint: "Hint",
    sureToMarkAsProcessed: "Sure to mark as processed?",
    notificationManagement: "Notification Management",
    notificationType: "Type",
    text: "Text",
    imageMessage: "Image Message",
    createdTimeRange: "Time Range",
    notificationCaption: "Caption",
    language: "Language",
    receiver: "Receiver",
    viewDetails: "View Details",
    likes: "Likes",
    views: "Views",
    publishedState: "Published State",
    progress: "Progress",
    exception: "Exception",
    continue: "Continue",
    author: "Author",
    publish: "Publish",
    allUsers: "All Users",
    partOfUsers: "Part of Users",
    selectUsers: "Select Users",
    selected: "Selected",
    users: "Users",
    device: "Device",
    mosappVersion: "MosApp Version",
    useLanguage: "Language",
    countryCode: "Country Code",
    registeredTime: "Registered Time",
    lastOnlineTime: "Last Online Time",
    selectAll: "Select All",
    nickname: "Nickname",
    country: "Country",
    coverPhoto: "Cover Photo",
    sureToPublishThisAnnouncement: "Sure to publish this announcement",
    welcomeManagement: "Welcome Management",
    welcomeType: "Type",
    image: "Image",
    all: "All",
    chinese: "Chinese",
    english: "English",
    creator: "Creator",
    keywordReply: "Keyword Reply",
    keyword: "Keyword",
    replyContent: "Reply Content",
    off: "Off",
    on: "On",
    addTime: "Add Time",
    unpublished: "Unpublished",
    published: "Published",
    abstract: "Abstract",
    content: "Content",
    close: "Close",
    done: "Done",
    persons: "Persons",
    toReceiverTip: "Users who meet the following conditions are recipients",
    version: "version",
    registerTimeRange: "Register time range",
    lastLoginTimeRange: "Last login time range",
  },
  version: {
    versionManagement: "Version Management",
    versionNumber: "Version Number",
    versionFormatError: "Format is incorrect. The correct is: vxx.xx.xx",
    platform: "Platform",
    versionType: "Version Type",
    noNeedUpdate: "No need Update",
    optionalUpdate: "Optional Update",
    forceUpdate: "Force Update",
    enabledState: "Enabled State",
    on: "On",
    off: "Off",
    versionLink: "Version Link",
    updateContent: "Update Content",
    updateContentEn: "Update Content(English)",
  },
  msg: {
    rtfMessage: "RTF Message",
    rtf: "RTF",
    forwardRtfMessage: "Forward RTF Message",
    linkPictureMessage: "Link Picture Message",
    linkPicture: "Link Picture",
    linkVideoMessage: "Link Video Message",
    linkVideo: "Link Video",
    linkForwardPictureMessage: "Link Forward Picture Message",
    linkForwardVideoMessage: "Link Forward Video Message",
    chatMessages: "Chat Messages",
    singleMessageManagement: "",
    groupMessageManagement: "",
    channelMessageManagement: "",
    messageManagement: "Message Management",
    chatID_GroupName: "Chat ID/Group Name",
    messageType: "Message Type",
    namecard: "Namecard",
    position: "Position",
    reply: "Reply",
    merge_forwardedMessage: "Merge-forwarded Message",
    customizedEmoticon: "Customized Emoticon",
    externalCustomizedEmoticon: "External Customized Emoticon",
    channelTextForward: "Channel Text Forward",
    channelVoiceForward: "Channel Voice Forward",
    channelPictureForward: "Channel Picture Forward",
    channelVideoForward: "Channel Video Forward",
    channelFileForward: "Channel File Forward",
    channelRedEnvelopeForward: "Channel RedEnvelop Forward",
    channelLink: "Channel Link",
    mosAssistant: "Mos Assistant",
    multiPersonCall: "Multi-person Call",
    AImessage: "AI Customer Message",
    encryptedType: "Encrypted Type",
    encrypted: "Encrypted",
    unencrypted: "Unencrypted",
    sendTime: "Post Time",
    sendTimeRange: "Send Time Range",
    senderNickname: "Sender Nickname",
    receiverNickname: "Receiver Nickname",
    groupNickName: "Group Name",
    encryptionGroup: "Encryption Group",
    messageContent: "Message Content",
    notShow: "Encrypted content cannot be displayed",
    view: "View",
    copy: "Copy",
    chatType: "Chat Type",
    privateChat: "Private Chat",
    ordinaryGroup: "Ordinary Group",
    thousandGroup: "Thousand group",
    temporaryConversation: "Temporary conversation",
    systemChat: "System Chat",
    channel: "Channel",
    AdvancedGroupedConversation: "Advanced grouped conversation",
    readState: "Read State",
    deleteState: "Delete State",
    recallState: "Recall State",
    callRecord: "Call Record",
    sender: "Sender",
    receiver: "Receiver",
    roomID: "Room ID",
    folderId: "Folder Id",
    folderName: "Folder Name",
    type: "Type",
    video: "Video",
    audio: "Audio",
    conversationId: "Conversation ID",
    senderID: "Sender ID",
    senderName: "Sender Name",
    receiverID: "Receiver ID",
    receiverName: "Receiver Name",
    joinTime: "Join Time",
    endTime: "End Time",
    startTime: "Start Time",
    duration: "Duration",
    unread: "Unread",
    read: "Read",
    notDeleted: "Not deleted",
    deleted: "Deleted",
    notWithdrawn: "Not withdrawn",
    Withdrawn: "Withdrawn",
    copied: "Copied",
    audioVideo: "[Audio and video call",
    chatHistory: "Chat history",
  },
  user: {
    userMaintenance: "User maintenance",
    feedbackManagement: "Feedback Management",
    feedbackTime: "Feedback Time",
    feedbackType: "Feedback Type",
    appeal: "appeal",
    advice: "advice",
    websiteCv: "website-curriculum-vitae",
    websiteConectus: "website-contact-us",
    pending: "Pending",
    processed: "Processed",
    feedbackContent: "Feedback Content",
    close: "Close",
    reportManagement: "Report Management",
    date: "Date",
    sameDay: "Same Day",
    yesterday: "Yesterday",
    servenDays: "7 Days",
    oneMonth: "1 Month",
    reportFrom: "Report From",
    user: "User",
    group: "Group",
    channel: "Channel",
    customer: "Customer",
    moment: "Moment",
    reportType: "Report Type",
    processingState: "Processing State",
    ignored: "Ignored",
    reportUser: "Report User",
    reportedTarget: "Reported Target",
    reportDescription: "Report Description",
    reportDetails: "Report Details",
    remark: "Remark",
    frozenTime: "Frozen Time",
    ignore: "Ignore",
    faq: "FAQ",
    simplifiedChinese: "Simplified Chinese",
    traditionalChinese: "Traditional Chinese",
    english: "English",
    cambodian: "Cambodian",
    addTime: "Add Time",
    description: "Description",
    download: "Download",
    sureToDeal: "Sure to deal with this feedback?",
    sureToIgnore: "Sure to Ignore this report?",
    sureToDelete: "Sure to delete this question?",
  },
  safe: {
    securitySettings: "Security Settings",
    sensitiveWords: "Sensitive Words",
    chineseIP: "Chinese  IP",
    cambodianIP: "Cambodian IP",
    otherIP: "Other IP",
    addTime: "Add Time",
    blacklistForCountryCode: "Blacklist for country code",
    restrictedCountry: "Restricted Country",
    countryCode: "Country Code",
    restrictedByCountryCode: "Restricted by country code",
    ipRule: "IP rule",
    none: "No",
    have: "Yes",
    restryctedByIP: "Restrycted By IP",
    no: "No",
    yes: "Yes",
    enabledState: "Enabled State",
    sureToDelete: "Sure to delete this rule?",
    bothRestrictionsCanNotBeEmpty:
      "Area code restrictions and IP restrictions, both cannot be empty.",
    selectCountryFirst: "Please select a country first.",
  },
  wallet: {
    walletManagement: "Wallet Management",
    dashboard: "Dashboard",
    selectWallet: "Select Wallet",
    mosappWallet: "MosApp Wallet",
    uPayWallet: "U-Pay Wallet",
    selectCurrency: "Select Currency",
    selectDate: "Select Date",
    yesterday: "Yesterday",
    today: "Today",
    sevenDays: "7 Days",
    fifteenDays: "15 Days",
    oneMonth: "1 Month",
    threeMonths: "3 Months",
    oneYear: "1 Year",
    accountBalance: "Account Balance",
    userTopUpAmount: "User Top-up Amount",
    userTransferAmount: "User Transfer Amount",
    redPacketAmount: "Red Packet Amount",
    transactionTimes: "Transaction Times",
    userTopUpTimes: "User Top-up Times",
    userTransferTimes: "User Transfer Times",
    redPacketTimes: "Red Packet Times",
    rewardAmount: "User Reward Amount",
    rewardCount: "User Reward Times",
    users: "Users",
    basicUsers: "Basic Users",
    kycUsers: "KYC Users",
    fullKYCUsers: "Full-KYC Users",
    addedYesterday: "Added Yesterday",
    orders: "Orders",
    orderID: "Order ID",
    orderType: "Order Type",
    topUp: "Top-up",
    transfer: "Transfer",
    redPacket: "Group Red Packet",
    channelRedPacket: "Channel Red Packet",
    referralBonus: "Referral Bonus",
    usersInformation: "Users Information",
    transactionTime: "Transaction Time",
    orderState: "Order State",
    succesfully: "Successfully",
    inProgress: "In Progress",
    failed: "Failed",
    details: "Details",
    orderDetails: "Order Details",
    country: "Country",
    currency: "Currency",
    transactionAmount: "Transaction Amount",
    totalAmount: "Total",
    paymentMethod: "Payment Method",
    paymentAccount: "Payment Account",
    paymentAccountName: "Payment Account Name",
    dailyTransactionData: "Daily Transaction Data",
    date: "Date",
    totalAccountAmount: "Total Account Amount",
    addedAccountAmount: "Added Account Amount",
    addedTopUpAmount: "Added Top-up Amount",
    addedTopUpTimes: "Added Top-up Times",
    addedRedPacketAmount: "Added red packet amount",
    addedRedPacketTimes: "Added red packet times",
    addedTransferAmount: "Added Transfer Amount",
    addedTransferTimes: "Added Transfer Times",
    totalUsers: "Total Users",
    addedUsers: "Added Users",
    usersAccountID: "Users Account ID",
    usersState: "Users State",
    usersLevel: "Users Level",
    accountAmount: "Account Amount",
    totalReceiveAmount: "Total Receive Amount",
    totalTransferAmount: "Total Transfer Amount",
    totalsendRedPacketAmount: "Total send red packet amount",
    exportTimeLimit:
      "The interval of exporting data cannot be greater than 35 days.",
    selectTimeLimit:
      "Please select the trading time with time interval less than 35 days.",
    userAttr: "Nickname/ID/phone number",
    wallet: "Wallet",
    targetUserNickname: "Target User Nickname",
    targetUserId: "Target User Id",
    targetUserPhoneNumber: "Target User Phone Number",
    envelopeType: "Envelope Type",
    envelopeCount: "Envelope Count",
    envelopeTotal: "Total Amount",
    grabTime: "Grab Time",
    grabAmount: "Grab Amount",
    unknown: "Unknown",
    ordinary: "Ordinary",
    lucky: "Lucky",
    normalUser: "Normal User",
    payAccount: "Pay Account",
    payAccountName: "Pay Account Name",
    KHQR收款: "KHQR Collect",
    Upay账户充值: "Upay Recharge",
    selectTotal: "Select Total",
    pageTotal: "Page Total",
    VIP_SUBSCRIBE_PAY: "VIP Subscription Payment",
    PAID_GROUP_PAY: "Paid Group Payment",
    CARD_APPLY: "Card Applicatio",
    MINI_APP_PAY: "Mini App Payment",
    KHQR_WITHDRAW: "KHQR Withdrawal",
    KHQR_PAY: "KHQR Payment",
    KHQR_PAY_CENTRAL: "KHQR Central Payment",
    rewardOrder: "Reward",
    userAddRewardAmount: 'Added Reward Amount',
    userAddRewardCount: 'Added Reward Times'
  },
  monitor: {
    attribute: "Attribute",
    value: "Value",
    coreCount: "Core Count",
    userUsageRate: "User Usage Rate",
    systemUsageRate: "System Usage Rate",
    currentIdleRate: "Current Idle Rate",
    property: "Property",
    memory: "Memory",
    totalMemory: "Total Memory",
    usedMemory: "Used Memory",
    remainingMemory: "Remaining Memory",
    usageRate: "Usage Rate",
    serverInformation: "Server Information",
    serverName: "Server Name",
    operatingSystem: "Operating System",
    serverIP: "Server IP",
    systemArchitecture: "System Architecture",
    javaVirtualMachineInformation: "Java Virtual Machine Information",
    javaName: "Java Name",
    javaVersion: "Java Version",
    startupTime: "Startup Time",
    uptime: "Uptime",
    installationPath: "Installation Path",
    projectPath: "Project Path",
    runtimeParameters: "Runtime Parameters",
    diskStatus: "Disk Status",
    diskDrivePath: "Disk Drive Path",
    fileSystem: "File System",
    driveType: "Drive Type",
    totalSize: "Total Size",
    availableSize: "Available Size",
    usedSize: "Used Size",
    usedPercentage: "Used Percentage",
    basicInformation: "Basic Information",
    redisVersion: "Redis Version",
    runningMode: "Running Mode",
    standalone: "Standalone",
    cluster: "Cluster",
    port: "Port",
    clientCount: "Client Count",
    uptimeDays: "Uptime (Days)",
    usedCPU: "Used CPU",
    memoryConfiguration: "Memory Configuration",
    aofEnabled: "AOF Enabled",
    rdbSuccessful: "RDB Successful",
    keyCount: "Key Count",
    networkInOut: "Network In/Out",
    commandStatistics: "Command Statistics",
    memoryInformation: "Memory Information",
    memoryConsumption: "Memory Consumption",
    peak: "Peak",
    command: "Command",
  },
  system: {
    userAgreement: "User Agreement",
    privacyPolicy: "Privacy Policy",
    roleName: "Role Name",
    permissionCharacter: "Permission Character",
    roleStatus: "Role Status",
    roleCode: "Role Code",
    displayOrder: "Display Order",
    dataPermissions: "Data permissions",
    assignUsers: "Assign users",
    roleOrder: "Role Order",
    menuPermissions: "Menu Permissions",
    permissionScope: "Permission Scope",
    loading: "Loading",
    expandCollapse: "Expand/Collapse",
    selectAllDeselectAll: "Select All/Deselect All",
    parentChildLinkage: "Parent-Child Linkage",
    confirmDelete: "Confirm delete this item:",
    allocateDataPermissions: "Allocate Data Permissions",
    allDataPermissions: "All Data Permissions",
    customDataPermissions: "Custom Data Permissions",
    departmentDataPermissions: "Department Data Permissions",
    departmentandBelowDataPermissions: "Department and Below Data Permissions",
    onlyMyDataPermissions: "Only My Data Permissions",
    batchRevokeAuthorization: "Batch Revoke Authorization",
    revokeAuthorization: "Revoke Authorization",
    confirmRevokeAuthorization:
      "Confirm to revoke authorization for this user：",
    userName: "User Name",
    userID: "User ID",
    role: "Role",
    lastLoginTime: "Last Login Time",
    resetPassword: "Reset Password",
    newPassword: '" new password',
    dictionaryName: "Dictionary Name",
    dictionaryType: "Dictionary Type",
    dictionaryStatus: "Dictionary Status",
    dictionaryCode: "Dictionary Code",
    dictionaryLabel: "Dictionary Label",
    dictionaryNum: "Dictionary Code",
    dictionaryKey: "Dictionary Key",
    dictionaryOrder: "Dictionary Order",
    refreshCache: "Refresh Cache",
    parameterName: "Parameter Name",
    parameterKeyName: "Parameter Key Name",
    systemBuiltIn: "System Built-in",
    parameterPrimaryKey: "Parameter Primary Key",
    parameterKeyValue: "Parameter Key Value",
    logID: "Log ID",
    systemModule: "System Module",
    operationType: "Operation Type",
    requestMethod: "Request Method",
    operator: "Operator",
    host: "Host",
    operationStatus: "Operation Status",
    operationDate: "Operation Date",
    type: "Type",
    clearOperateLog:
      "Are you sure you want to clear all operation log data items?",
    operationLogDetails: "Operation Log Details",
    operationModule: "Operation Module",
    requestAddress: "Request Address",
    operationMethod: "Operation Method",
    requestParameters: "Request Parameters",
    returnParameters: "Return Parameters",
    operationTime: "Operation Time",
    exceptionInformation: "Exception Information",
    loginInfo: "Login Info",
    normal: "Normal",
    failure: "Failure",
    loginAddress: "Login Address",
    loginStatus: "Login Status",
    loginTime: "Login Time",
    accessID: "Access ID",
    address: "Address",
    loginLocation: "Login Location",
    operatingSystem: "Operating System",
    browser: "Browser",
    description: "Description",
    accessTime: "Access Time",
    noDataAvailable: "No data available",
    menuName: "Menu Name",
    icon: "Icon",
    sorting: "Sorting",
    permissionIdentifier: "Permission Identifier",
    componentPath: "Component Path",
    parentMenu: "Parent Menu",
    selectParentMenu: "Select Parent Menu",
    menuType: "Menu Type",
    menuIcon: "Menu Icon",
    directory: "Directory",
    menu: "Menu",
    button: "Button",
    isExternalLink: "Is External Link",
    routeAddress: "Route Address",
    routeParameters: "Route Parameters",
    isCachingEnabled: "Is Caching Enabled",
    displayStatus: "Display Status",
    menuStatus: "Menu Status",
    clickToSelect: "Click To Select Icon",
    selectUser: "Select User",
    setPermissionTag: "Please set the operation permission tag value",
    setRolePerTag: "Please set the role permission tag value",
    clearAllScheduleLog: "Confirm to clear all scheduling logs?",
    clearAllLoginLog: "Confirm to clear all login logs?",
    confirmForceExit: 'Confirm forced exit "',
    notAssigned: "Not yet assigned",
    dataLabel: "Data label",
    dataKeyValue: "Data key-value",
    styleAttribute: "Style attribute",
    echoStyle: "Echo style",
    default: "Default",
    primary: "Primary",
    success: "Success",
    info: "Info",
    warning: "Warning",
    danger: "Danger",
    cache: "Cache",
    noCache: "No cache",
    mainCategory: "Main Category",
    deptName: "Department Name",
  },

  menu: {
    menuName: "Menu name",
  },
  // 营销活动
  marking: {
    addMarketingActivity: "Create Activity campaign",
    withdrawalLevelManagement: "Withdrawal level management",
    activityName: "Activity Name",
    activityType: "Activity Type",
    activityStatus: "Activity Status",
    inviteNewUserActivity: "User acquisition campaign",
    fissionActivity: "Multi-level referrals",
    fissionGrade: "Referral Levels",
    levelReward: "{0} Level Reward",
    singleInviteeRewardUpperLimit: "Upper limit of invitor",
    fissionRewardUpperLimit: "Upper limit of total amount",
    createMarketingActivity: "Create mkt campaign",
    activityTime: "Activity Time",
    isEnabled: "Enabled",
    submitAudit: "Submit for review",
    audit: "Review",
    data: "Data",
    status: {
      draft: "Draft",
      auditing: "Under review",
      rejected: "Rejected",
      notStarted: "Not yet started",
      underway: "Going on",
      ended: "Ended",
    },
    configureMarketingActivity: "Configure mkt campaign",
    auditMarketingActivity: "Review mkt campaign",
    viewMarketingActivity: "View mkt campaign",
    taskDeadline: "Task Deadline",
    hour: "Hours",
    rewardCoin: "Reward currency",
    rewardCondition: "Reward conditions",
    needWallet: "Wallet opening required",
    noNeedWallet: "Wallet opening not required",
    needKyc: "Need to complete KYC verification",
    noNeedKyc: "No need to complete KYC certification",
    newUserReward: "New user rewards",
    limit: "Upper limit",
    lowerLimit: "Lower limit",
    newUserRewardTotalAmountUpperLimit:
      "Upper limit of total amount of new user reward",
    needInvitation: "Number of people to invite",
    rewardAmount: "Reward amount",
    reserveReward: "Assured reward",
    byInvitationRatio: "Rewards issued based on invitee proportion",
    none: "None",
    activityRules: "Rule",
    auditResult: "Review Result",
    pass: "Approved",
    reject: "Rejected",
    rejectReason: "Rejection Reason",
    securityKey: "Code",
    activityData: "Campaign Data",
    historyCumulative: "Historical accumulation",
    issuedAmount: "Issued amount ",
    issuedAmountTip: "Issued amount",
    newUserRewardAmount: "Amount received by new users",
    newUserRewardAmountTip: "Amount received by newly registered users",
    inviteUserRewardAmount: "Amount received by the inviter",
    inviteUserRewardAmountTip: "Amount received by the inviter",
    expectedAmount: "Estimated amount issued",
    expectedAmountTip:
      "For ongoing tasks with enabled assured rewards, the estimated assured reward+completed task amount, but unclaimed rewards",
    participationNumber: "Participants",
    participationNumberTip: "Participants in the campaign",
    inviteNewUser: "Invited new users",
    inviteNewUserTip: "New users via invitation link",
    completedTaskNumber: "Tasks completed by users",
    completedTaskNumberTip: "Number of completed tasks with reward amount ≠ 0",
    activityPageViews: "Campaign page views",
    activityPageViewsTip:
      "Number of visits to the activity page, the same user will not be counted repeatedly within 30 minutes",
    inviteUserNumber: "Invitations",
    inviteUserNumberTip: "Number of clicking [Invited new users]",
    comparedWith1DayAgo: "Compared to",
    comparedWith7DaysAgo: "Compared to",
    comparedWith30DaysAgo: "Compared to",
    activityNameLimit: "The campaign name must be between 1 and 30 characters",
    durationLimit: "The task period must be between 1 and 9999",
    betweenLimit: "{0} must be greater than {1} and less than or equal to {2}",
    betweenLimit1: "{0} must be greater than or equal to {1} and less than or equal to {2}",
    mustGreaterThan: "{0} cannot be less than {1}",
    cannotGreaterThan: "{0} cannot be greater than {1}",
    canNotContainDecimals: "The amount cannot contain decimals",
    targetAudience: "Users",
    targetAudienceTip:
      "Enter the user's phone number, press Enter to complete the line, such as:\n855123456789\n855987654321",
    allUsers: "All",
    specificUsers: "Part",
  },
  // 会员套餐
  vip: {
    type: "Type",
    firstPurchaseDiscount: "First Purchase Discount",
    status: "Status",
    discount: "Discount",
    payableAmount: "Payable Amount",
    crossedAmount: "Crossed Amount",
    monthlyAutoRenewal: "Monthly Auto Renewal",
    quarterlyAutoRenewal: "Quarterly Auto Renewal",
    yearlyAutoRenewal: "Yearly Auto Renewal",
    year: "1 Year",
    quarter: "1 Quarter",
    month: "1 Month",
    package: "VIP-Package",
    checkDiscount: "Integer from 0 to 100",
    checkPayableAmount:
      "Greater than 0 and less than 99999.99 two decimal places",
    checkCrossedAmount:
      "The underlined amount must be greater than the payable amount",
  },

  // 客服管理
  customer: {
    sname: "Name",
    slink: "Link",
    avatar: "Avatar",
    conversationNum: "Chats",
    adminNum: "Staffs",
    name: "Name",
    link: "Link",
    ownerId: "Owner ID",
    ownerName: "Owner Name",
    createTime: "Created Time",
    conversation: "Chats",
    admin: "Staffs",
    type: "Type",
    normal: "Register User",
    guest: "Guest User",
    robot: "Robot",
    customerNum: "customers",
    customer: "Customers",
    joinTime: "Joined Time",
  },

  recommend: {
    status: "Status",
    all: "All",
    audit: "Audit",
    recommend: "Recommend",
    deprecated: "Deprecated",
    channelName: "Channel Name",
    sender: "Sender",
    content: "Content",
    recommendTime: "Recommend Time",
    operate: "Operate",
    detail: "Detail",
    manualReview: "Manual Review",
    createTime: "create Time",
    startTime: "start Time",
    endTime: "end Time",
    reject: "Reject",
    recommendAudit: "Recommend",
    redEnvelopeBlessing: "Red Packet Blessing",
    confirmPush: "Are you sure you want to recommend it?",
    confirmReject: "Are you sure you want to reject the recommend it?",
  },

  // 菜单 （对创建出来的内容可通过该方式进行国际化）
  首页: "home",
  数据统计: "Data Statistics",
  用户留存: "Retention Users",
  通讯录: "Contacts",
  用户管理: "Users Management",
  收费管理: "Charge Management",
  会员套餐: "VIP Package",
  客服管理: "Customer Service",
  客服消息管理: "Messages Management",
  客服号管理: "Customer Service Management",
  游客管理: "Unregistered User",
  群组管理: "Group Management",
  频道管理: "Channel Management",
  群成员管理: "Group Members",
  用户认证审核: "User authentication review",
  频道认证审核: "Channel authentication review",
  营销管理: "Marketing Management",
  营销活动: "Marketing Campaign",
  爬虫管理: "Crawler Management",
  爬虫目标管理: "Crawler Target Management",
  目标管理: "Target Management",
  内容库: "Content Library",
  机器人管理: "Bot Management",
  MOS助手: "Mos Assistant",
  mos助手消息: "Messages of Mos Assistant",
  通知管理: "Notification Management",
  欢迎语管理: "Welcome Management",
  自动回复管理: "Keyword Reply",
  钱包管理: "Wallet Management",
  仪表盘: "Dashboard",
  交易订单: "Orders",
  每日交易数据: "Daily Transaction Data",
  账户管理: "Account Management",
  版本管理: "Version Management",
  聊天信息管理: "Chat Messages",
  消息管理: "Message Management",
  单聊消息管理: "Single Message Management",
  群聊消息管理: "Group Message Management",
  频道消息管理: "Channel Message Management",
  通话记录: "Call Record",
  用户维护: "User Maintenance",
  反馈信息管理: "Feedback Management",
  举报管理: "Report Management",
  常见问题: "FAQ",
  安全设置: "Security Settings",
  敏感词库管理: "Sensitive Words",
  国际区号黑名单管理: "Blacklist for country code",
  注册地限制: "Country code limit",
  系统管理: "System Management",
  协议与隐私: "Protocol and Privacy",
  角色管理: "Role Management",
  字典管理: "Dictionary Management",
  参数设置: "Param Setting",
  日志管理: "Log Management",
  操作日志: "Operation Log",
  登录日志: "LogIn Log",
  菜单管理: "Menu Management",
  系统监控: "System Monitoring",
  服务监控: "Service Monitoring",
  缓存监控: "Cache Monitoring",

  系统工具: "System tools",
  部门管理: "Department management",
  岗位管理: "Position management",
  通知公告: "Notice announcement",
  在线用户: "Online users",
  定时任务: "Scheduled tasks",
  数据监控: "Data monitoring",
  表单构建: "Form building",
  代码生成: "Code generation",
  系统接口: "System interface",
  banner管理: "Banner management",
  客户端设置: "Client settings",
  支付配置: "Payment configuration",
  资金管理: "Fund management",
  账单统计: "Bill statistics",
  充值记录: "Recharge records",
  提现审核: "Withdrawal review",
  提现记录: "Withdrawal records",
  转账记录: "Transfer records",
  反馈管理: "Feedback management",
  话题管理: "Topic management",
  动态管理: "Dynamic management",
  公告管理: "Announcement management",
  组织架构: "Organizational structure",
  组织架构职位管理: "Organizational structure position management",
  组织架构部门管理: "Organizational structure department management",
  服务号发布管理: "Service number release management",
  清空token: "Clear token",
  国号黑名单管理: "Country code blacklist management",
  留言反馈管理: "Message feedback management",
  欢迎语: "Welcome message",
  自动回复设置: "Auto-reply settings",
  爬虫配置: "Crawler configuration",
  频道运营: "Channel operations",
  推荐内容管理: "Recommended content management",

  // admin后台创建的字典
  停用: "Disable",
  正常: "Normal",
  男: "Man",
  女: "Woman",
  未知: "Unknown",
  显示: "Show",
  隐藏: "Hide",
  暂停: "Pause",
  默认: "Default",
  系统: "System",
  是: "Yes",
  否: "No",
  通知: "Notification",
  公告: "Announcement",
  关闭: "Close",
  新增: "Add",
  修改: "Edit",
  删除: "Delete",
  授权: "Authorize",
  导出: "Export",
  导入: "Import",
  强退: "Force Quit",
  生成代码: "Generate Code",
  清空数据: "Clear Data",
  成功: "Success",
  失败: "Failure",
};
