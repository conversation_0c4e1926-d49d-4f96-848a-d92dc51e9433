export default {
  auth: {
    title: "扫码登录",
    authTitle: "请先完成实名认证",
    country: "国家",
    idType: "证件类型",
    IDCard: "身份证",
    passport: "护照",
    certNumber: "证件号码",
    certFrontPhoto: "证件正面照",
    certBackPhoto: "证件背面照",
    holdingCertPhoto: "手持证件照",
    goAuth: "去认证",
    fullName: "真实姓名",
    authenticationType: "认证类型",

    pAuth: "个人认证",
    cAuth: "企业认证",

    enterpriseRegCountry: "企业注册国家",
    enterpriseRegLocation: "企业注册地",
    enterpriseRegCertPhoto: "企业注册证书",
    enterpriseRegCode: "企业唯一注册码",

    submit: "提交",
    reSubmit: "重新提交",

    check: "查看",

    reviewResult: "审核结果",

    reasonRejection: "驳回原因",

    approved: "通过",
    reject: "驳回",
  },
  channelReview: {
    channelId: "频道ID",
    avatar: "频道头像",
    Name: "频道名称",
    Type: "频道类型",
    Link: "频道链接",
    time: "创建时间",
    certType: "认证类型",
    status: "认证状态",

    inReview: "审核中",
    notPassed: "未通过",
    passed: "已通过",
    expired: "已失效",

    personal: "个人认证",
    enterprise: "企业认证",
    weiVerify: "未认证",
    cancelVerify: "取消认证",

    auditing: "审核",

    application: "申请",

    personalApply: "个人认证申请",
    enterpriseApply: "企业认证申请",

    submitSuccess: "提交成功",
    enterpriseInfo: "企业信息",
    contactInfo: "联系人信息",
  },
  login: {
    title: "管理系统",
    systemPrompt: "系统提示",
    logIn: "登 录",
    logIng: "登 录 中...",
    reLogin: "重新登录",
    loginExpired: "登录状态已过期，您可以继续留在该页面，或者重新登录",
    invalidLogin: "无效的会话，或者会话已过期，请重新登录。",
    username: "账号",
    pleaseInputUsername: "请输入您的账号",
    rememberPassword: "记住密码",
    inputPassword: "请输入密码登录",
    pleaseInputPassword: "请输入您的密码",
    verifyCode: "验证码",
    pleaseInputVerifyCode: "请输入验证码",
    register: "立即注册",
    inputNewPassword: "请输入新密码",
    confirmNewPassword: "请再次确认密码",
    passwordNotSame: "两次输入密码不一致",
  },
  common: {
    internalError: "内部服务器错误, 请稍等",
    authError: "认证失败，无法访问系统资源",
    noPermit: "当前操作没有权限",
    resourceNotExist: "访问资源不存在",
    systemError: "系统未知错误，请反馈给管理员",
    reSubmit: "数据正在处理，请勿重复提交",
    select: "请选择",
    pleaseSelectState: "请选择状态",
    pleaseSelectType: "请选择类型",
    pleaseSelectDeleteItem: "请选择删除内容",
    selectDate: "选择日期",
    startDate: "开始日期",
    endDate: "结束日期",
    startTime: "开始时间",
    to: "至",
    endTime: "结束时间",
    all: "全部",
    search: "搜索",
    save: "保存",
    reset: "重置",
    export: "导出",
    cancel: "取消",
    confirm: "确定",
    tips: "提示",
    confirmUnfrozen: "确认要执行解冻操作吗？",
    confirmCancelChannelVerify: "确定要取消此频道的认证吗？",
    confirmCancelUserVerify: "确定要取消此用户的认证吗？",
    confirmLogoutUser: "确认让这个用户退出登录吗？",
    pleaseEnter: "请输入",
    state: "状态",
    operate: "操作",
    phoneNumber: "手机号",
    createdTime: "创建时间",
    delete: "删除",
    confirmDelete: "确定要删除吗",
    add: "新增",
    edit: "编辑",
    clear: "清空",
    success: "成功!",
    failed: "失败!",
    post: "发布",
    details: "详情",
    monday: "周一",
    tuesday: "周二",
    wednesday: "周三",
    thursday: "周四",
    friday: "周五 ",
    saturday: "周六",
    sunday: "周日",
    userId: "用户ID",
    userNickname: "用户昵称",
    caption: "标题",
    hideSearch: "隐藏搜索",
    showSearch: "显示搜索",
    refresh: "刷新",
    text: "文本",
    image: "图片",
    voice: "语音",
    video: "视频",
    file: "文件",
    redEnvelope: "红包",
    other: "其他",
    remark: "备注",
    handleTime: "处理时间",
    handleClientId: "处理对象",
    days: "处理结果",
    submit: "提 交",
    cannotBeEmpty: "不能为空",
    formatError: "格式错误",
    dealWith: "处理",
    return: "返回",
    on: "开启",
    off: "关闭",
    downloadWait: "正在下载数据，请稍候",
    yes: "是",
    no: "否",
    close: "关闭",
    viewMore: "查看更多",
    view: "查看",
    viewDetails: "查看详情",
    freeze: "冻结",
    unfreeze: "解冻",
    freezeMsg: "已冻结",
    unfreezeMsg: "已解冻",
    cancelVerifyMsg: "已取消认证",
    interfaceNetworkError: "后端接口连接异常",
    interfaceTimeout: "系统接口请求超时",
    interfaceException: "系统接口异常：",
    pleaseEnterContent: "请输入内容",
    downloadError: "下载文件出现错误，请联系管理员！",
    pleaseEnterNumber: "请输入数字",
    keywordExceed: "关键字不得超过 300 个字符",
    replyExceed: "回复内容不超过1000字",
    doNotRepost: "请勿重复发布！",
    charactersExceeds: "字符数量超过限制：",
    numberExceeds: "输入数字不在范围内：",
    userCanNotEmpty: "用户不能为空",
    pleaseSelectChannel: "请选择要发布的频道",
    formatIncorrect: "格式不正确",
    pleaseSelectUser: "请选择要分配的用户",
    strLengthRange: "字符串长度区间：",
    confirmBanUser: "确认要对此用户封禁吗",
    confirmPost: "确认要发布吗？",
    enable: "启用",
    disable: "停用",
    content: "内容",
  },
  tool: {
    fileFormatError: "文件格式不正确, 请上传",
    fileSizeLimit: "上传文件大小不能超过",
    fileCountLimit: "上传文件数量不能超过",
    uploadWait: "正在上传，请稍候...",
    uploadFail: "上传文件失败",
    waitSetLayOut: "正在设置布局大小，请稍候...",
    waitSaveLocal: "正在保存到本地，请稍候...",
    waitClearCache: "正在清除设置缓存并刷新，请稍候...",
    waitLoadCacheMonitor: "正在加载缓存监控数据，请稍候！",
    waitLoadServiceMonitor: "正在加载服务监控数据，请稍候！",
    upload: "请上传",
    sizeLimit: "大小不超过",
    formats: "格式为",
    files: "的文件",
  },
  homePage: {
    title: "管理系统",
    homePage: "首页",
    personalCenter: "个人中心",
    layoutSettings: "布局设置",
    logout: "退出登录",
    confirmLogout: "确定注销并退出系统吗？",
    personalInformation: "个人信息",
    userName: "用户名称",
    mobilePhoneNumber: "手机号码",
    userEmail: "用户邮箱",
    department: "所属部门",
    role: "所属角色",
    creationDate: "创建日期",
    basicInformation: "基本资料",
    email: "邮箱",
    sex: "性别",
    select: "选择",
    man: "男",
    woman: "女",
    save: "保存",
    close: "关闭",
    modifyPwd: "修改密码",
    oldPassword: "旧密码",
    newPassword: "新密码",
    confirmPassword: "确认密码",
    pleaseConfirmYourPassword: "请确认密码",
    pwdNotSame: "两次输入的密码不一致",
    changeAvatar: "修改头像",
    uploadAvatar: "点击上传头像",
    authRole: "分配角色",
    authUser: "分配用户",
    dicData: "字典数据",
    jobLog: "调度日志",
    genEdit: "修改生成配置",
    totalUsers: "总用户数",
    newUsersToday: "今日新增用户",
    newUsersYesterday: "昨日新增用户",
    activeUsersToday: "今日活跃用户",
    activeUsersYesterday: "昨日活跃用户",
    registeredUsers: "用户注册数据折线图",
    registeredUsersToday: "今日注册数",
    yesterday: "昨日",
    today: "今日",
    sevenDays: "7 天",
    threeMonths: "3 月",
    oneYear: "1 年",
    lastSevenDays: "近7日",
    lastThirtyDays: "近30日",
    activeUsersEcharts: "用户活跃统计图",
    activeUsers: "用户活跃",
    retentionRate: "用户留存率统计图",
    nextDayRetentionRate: "次日留存率",
    sevenDayRetentionRate: "7日留存率",
    thirtyDayRetentionRate: "30日留存率",
    thirtyDays: "30天",
    ninetyDays: "90天",
    unknown: "未知",
    themeStyleSettings: "主题风格设置",
    themeColor: "主题颜色",
    systemLayoutConfiguration: "系统布局配置",
    turnOn: "开启",
    fix: "固定",
    display: "显示",
    dynamicTitle: "动态标题",
    layoutSize: "布局大小",
    large: "较大",
    default: "默认",
    small: "稍小",
    moreMenu: "更多菜单",
    pwdLengthLimit: "用户密码长度必须介于 5 和 20 之间",
    nameLengthLimit: "用户名称长度必须介于 2 和 20 之间",
    notFoundErr: "404错误!",
    notFoundPage: "找不到网页!",
    returnHome: "返回首页",
    errDesc:
      "对不起，您正在寻找的页面不存在。尝试检查URL的错误，然后按浏览器上的刷新按钮或尝试在我们的应用程序中找到其他内容。",
  },
  userStatistic: {
    dataStatistics: "数据统计",
    retentionUsers: "用户留存",
    source: "来源渠道",
    selectSource: "请选择来源渠道",
    official: "官方",
    unknown: "未知",
    period: "时间段",
    date: "日期",
    sameDay: "当日",
    nextDay: "次日",
    the7thDay: "七日",
    the30thDay: "三十日",
  },
  userManage: {
    contacts: "通讯录",
    usersManagement: "用户管理",
    nickname: "昵称",
    phoneNumber: "手机号",
    pleaseEnter: "请输入",
    state: "状态",
    pleaseSelectStatus: "请选择状态",
    normal: "正常",
    deactivated: "注销",
    appOnlineState: "APP在线状态",
    online: "在线",
    offline: "离线",
    pcOnlineState: "PC在线状态",
    pleaseSelectOnlineStatus: "请选择在线状态",
    registrationTime: "注册时间",
    startDate: "开始日期",
    endDate: "结束日期",
    country: "所属国家",
    avatar: "头像",
    friendCount: "好友数量",
    lastOnlineTime: "最后上线时间",
    FrozenUntil: "冻结至",
    Forever: "永久",
    reportedTimes: "被举报次数",
    operate: "操作",
    registrationRecord: "登录记录",
    logoutUser: "退出登录",
    friendList: "好友列表",
    yes: "是",
    no: "否",
    onlineTime: "上线时间",
    offlineTime: "下线时间",
    loginIp: "登录IP",
    loginIpAddress: "登录IP归属地",
    loginDevice: "登录设备",
    systemVersion: "当前系统版本",
    mosappVersion: "当前mos版本号",
    frozenTime: "冻结时间",
    selectFrozenTime: "请选择冻结时间",
    threeHours: "3小时",
    twelveHours: "12小时",
    oneDay: "1天",
    threeDays: "3天",
    oneWeek: "1周",
    permanent: "永久",
    oneMonth: "1个月",
    threeMonths: "3个月",
    reason: "冻结原因",
    harassmentAndBullying: "骚扰信息",
    inappropriateRemarks: "不当言论",
    violationOfRegulation: "违反条款",
    pornographicViolence: "色情暴力",
    other: "其他",
    firstName: "名",
    lastName: "姓",
    dialCode: "区号",
    password: "密码",
    cancelAuth: "是否取消选中用户授权数据项?",
    userType: "用户类型",
    normalUser: "普通用户",
    guest: "游客",
  },
  groupManage: {
    groupManagement: "群组管理",
    isPaid: "是否付费",
    groupID: "群组ID",
    normal: "正常",
    deleted: "已解散",
    groupAvatar: "群头像",
    groupNickName: "群昵称",
    groupMembers: "群人数",
    encryptionOrNot: "是否加密",
    ordinary: "普通",
    encrypted: "加密",
    private: "私密",
    public: "公开",
    groupStyle: "群类型",
    groupOwnerId: "群主ID",
    memberNickname: "成员昵称",
    mutedState: "禁言状态",
    mute: "关闭",
    unmute: "开启",
    memberID: "成员ID",
    country: "所属国家",
    memberAvatar: "成员头像",
    groupName: "群名称",
    joinedTime: "加群时间",
    role: "角色",
    memberType: "成员类型",
    memberTypeUser: "用户",
    memberTypeRobot: "机器人",
    member: "普通群成员",
    admin: "管理员",
    owner: "群主",
    reportedTimes: "被举报次数",
    banSpeech: "禁言",
    pleaseSelectAuditState: "请选择审核状态",
  },
  channelManage: {
    channelManagement: "频道管理",
    channelType: "频道类型",
    pleaseSelect: "请选择频道类型",
    public: "公开",
    private: "私密",
    channelName: "频道名称",
    channelId: "频道ID",
    channelLink: "频道链接",
    ownerID: "频道主ID",
    ownerName: "频道主名称",
    channelState: "频道状态",
    normal: "正常",
    deleted: "已解散",
    channelAvatar: "频道头像",
    subscriber: "订阅者数",
    postsNumber: "帖子数",
    officialChannelOrNot: "是否官方频道",
    state: "状态",
    operate: "操作",
    subscribers: "订阅者列表",
    userID: "用户ID",
    userName: "用户昵称",
    phoneNumber: "手机号码",
    avatar: "头像",
    noAvatar: "暂无头像",
    country: "国家",
    subscriberTime: "订阅时间",
    applyTime: "申请时间",
    post: "帖子列表",
    sendTime: "发送时间",
    postType: "帖子类型",
    text: "文本",
    image: "图片",
    voice: "语音",
    video: "视频",
    file: "文件",
    postLink: "帖子链接",
    postID: "帖子ID",
    postTime: "发布时间",
    views: "浏览数",
    likes: "点赞数",
    comments: "评论数",
    collections: "收藏数",
    shares: "分享数",
    clickToCopy: "点击复制",
    rewardCount: "打赏次数",
    rewardAmoutn: "打赏金额",
    fee: "服务费",
    actualAmount: "实际到手费用",
  },
  crawlerManage: {
    crawlerManagement: "爬虫管理",
    targetManagement: "目标管理",
    platform: "所属平台",
    channelName: "频道名称",
    enterChannelName: "请输入频道名称",
    enterChannelLink: "请输入频道链接",
    channelLink: "频道链接",
    switchState: "切换状态",
    enabledState: "启用状态",
    contents: "爬取内容数",
    crawlOnce: "爬取一次",
    contentDetails: "爬取内容详情",
    postDate: "发布日期",
    postTime: "发布时间",
    postTimeOriginalPlatform: "原平台发送时间",
    crawlTime: "爬取时间",
    crawlContent: "爬取内容",
    viewDetails: "查看详情",
    video: "【视频】",
    file: "文件",
    sureToDeleteIt: "确定要删除吗",
    contentLibrary: "内容库",
    belongChannel: "所属频道",
    sureToDeleteThisMessage: "确定要删除这条信息吗",
    postedTimes: "使用次数",
    messageContent: "消息内容",
    attachment: "文件",
    postRecord: "发布记录",
    subscribersCount: "频道订阅者数量",
    selectChannel: "选择频道",
    subscribers: "订阅者数",
    sureToPostThisMessage: "确定要发布这条信息吗",
    botManagement: "机器人管理",
    mosappChannelName: "mos频道名称",
    postingRule: "发布规则",
    inTime: "及时发布",
    regularly: "定时发布",
    addTime: "添加时间",
    enalbedState: "启用状态",
    posts: "发布内容数",
    channel: "频道",
    source: "来源目标",
    isEnabled: "是否启用",
    postState: "发布状态",
    successfully: "发布成功",
    posting: "发布中",
    failed: "发布失败",
    addTarget: "添加目标",
    selectPlatform: "请选择平台",
    confirmGetChannelData: "确定要获取该频道数据吗?",
    successAndWait: "开始爬取频道内容，可能需要一两分钟，请稍候",
    addPostTime: "添加发布时间",
  },
  mosAssist: {
    mosAssistant: "Mos助手",
    messagesOfMosAssistant: "Mos助手消息",
    messageDeleted: "[消息已删除]",
    userName: "用户昵称",
    userID: "用户ID",
    state: "状态",
    pending: "待处理",
    processed: "已处理",
    feedbackTime: "反馈时间",
    feedbackMessage: "反馈信息",
    viewMore: "查看更多",
    dealWith: "处理",
    hint: "提示",
    sureToMarkAsProcessed: "确认执行已处理操作吗",
    text: "文本",
    imageMessage: "Image Message",
    notificationManagement: "通知管理",
    notificationType: "通知类型",
    createdTimeRange: "创建时间范围",
    notificationCaption: "通知标题",
    language: "语言",
    receiver: "接收方",
    viewDetails: "查看",
    likes: "点赞",
    views: "阅读数",
    publishedState: "发布状态",
    progress: "进度",
    exception: "异常",
    continue: "继续",
    author: "作者",
    publish: "发布",
    allUsers: "全部用户",
    partOfUsers: "部分用户",
    selectUsers: "选择用户",
    selected: "已选择",
    users: "个用户",
    device: "用户系统",
    mosappVersion: "使用的版本号",
    useLanguage: "使用的语言",
    countryCode: "所属国家区号",
    registeredTime: "注册时间",
    lastOnlineTime: "最后登录时间",
    selectAll: "全选",
    nickname: "昵称",
    country: "所属国家",
    coverPhoto: "封面图片",
    sureToPublishThisAnnouncement: "确认要发布该公告吗?",
    welcomeManagement: "欢迎语管理",
    welcomeType: "欢迎语类型",
    image: "图文",
    all: "全部",
    chinese: "中文",
    english: "英文",
    creator: "创建者",
    keywordReply: "自动回复管理",
    keyword: "关键字",
    replyContent: "回复内容",
    off: "未启用",
    on: "启用",
    addTime: "添加时间",
    unpublished: "未发布",
    published: "已发布",
    abstract: "摘要",
    content: "内容",
    close: "关 闭",
    done: "完成",
    persons: "人",
    toReceiverTip: "满足以下条件的用户为接收人",
    version: "版本号",
    registerTimeRange: "注册时间范围",
    lastLoginTimeRange: "最后登入时间范围",
  },
  version: {
    versionManagement: "版本管理",
    versionNumber: "版本编号",
    versionFormatError: "版本编号格式不正确. 正确格式: vxx.xx.xx",
    platform: "平台",
    versionType: "版本类型",
    noNeedUpdate: "无需更新",
    optionalUpdate: "可选更新",
    forceUpdate: "强制更新",
    enabledState: "启用状态",
    on: "开启",
    off: "关闭",
    versionLink: "版本链接",
    updateContent: "更新内容",
    updateContentEn: "英文更新内容",
  },
  msg: {
    rtfMessage: "富文本消息",
    rtf: "富文本",
    forwardRtfMessage: "富文本转发消息",
    linkPictureMessage: "图片链接消息",
    linkPicture: "图片链接",
    linkVideoMessage: "视频链接消息",
    linkVideo: "视频链接",
    linkForwardPictureMessage: "图片链接转发消息",
    linkForwardVideoMessage: "视频链接转发消息",
    chatMessages: "聊天信息管理",
    singleMessageManagement: "单聊消息管理",
    groupMessageManagement: "群聊消息管理",
    channelMessageManagement: "频道消息管理",
    messageManagement: "消息管理",
    chatID_GroupName: "会话ID或群昵称",
    messageType: "消息类型",
    namecard: "名片",
    position: "定位",
    reply: "回复",
    merge_forwardedMessage: "合并转发",
    customizedEmoticon: "自定义表情",
    externalCustomizedEmoticon: "外部自定义表情",
    channelTextForward: "频道文字转发",
    channelVoiceForward: "频道语音转发",
    channelPictureForward: "频道图片转发",
    channelVideoForward: "频道视频转发",
    channelFileForward: "频道文件转发",
    channelRedEnvelopeForward: "频道红包转发",
    channelLink: "频道邀请链接",
    mosAssistant: "mos助手",
    multiPersonCall: "多人音视频",
    AImessage: "AI客服消息",
    encryptedType: "加密类型",
    encrypted: "加密",
    unencrypted: "未加密",
    sendTime: "发送时间",
    sendTimeRange: "发送时间范围",
    senderNickname: "发送者昵称",
    receiverNickname: "接受者昵称",
    groupNickName: "群昵称",
    encryptionGroup: "加密群",
    messageContent: "消息内容",
    notShow: "加密内容不能显示",
    view: "查看",
    copy: "复制",
    chatType: "会话类型",
    privateChat: "单聊",
    ordinaryGroup: "普通群",
    thousandGroup: "万人群",
    temporaryConversation: "临时会话",
    systemChat: "系统会话",
    channel: "频道",
    AdvancedGroupedConversation: "高级分组会话",
    readState: "已读状态",
    deleteState: "删除状态",
    recallState: "撤回状态",
    callRecord: "通话记录",
    sender: "发送者",
    receiver: "接收者",
    roomID: "房间ID",
    folderId: "分组ID",
    folderName: "分组名称",
    type: "类型",
    video: "视频",
    audio: "音频",
    conversationId: "会话id",
    senderID: "发送者ID",
    senderName: "发送者昵称",
    receiverID: "接收者ID",
    receiverName: "接收者昵称",
    joinTime: "加入时间",
    endTime: "结束时间",
    startTime: "发起时间",
    duration: "时长",
    unread: "未读",
    read: "已读",
    notDeleted: "未删除",
    deleted: "已删除",
    notWithdrawn: "未撤回",
    Withdrawn: "已撤回",
    copied: "已复制",
    audioVideo: "【音视频通话】",
    chatHistory: "聊天记录",
  },
  user: {
    userMaintenance: "用户维护",
    feedbackManagement: "反馈信息管理",
    feedbackTime: "反馈时间",
    feedbackType: "反馈类型",
    appeal: "申诉",
    advice: "建议",
    websiteCv: "官方网站-简历",
    websiteConectus: "官网网站-联系我们",
    pending: "待处理",
    processed: "已处理",
    feedbackContent: "反馈信息",
    close: "关闭窗口",
    reportManagement: "举报管理",
    date: "日期",
    sameDay: "当天",
    yesterday: "昨天",
    servenDays: "7天内",
    oneMonth: "一月内",
    reportFrom: "举报来源",
    user: "用户",
    group: "群组",
    channel: "频道",
    customer: "客服",
    moment: "动态",
    reportType: "举报类型",
    processingState: "处理状态",
    ignored: "已忽略",
    reportUser: "举报用户",
    reportedTarget: "被举报对象",
    reportDescription: "举报描述",
    reportDetails: "举报详情",
    remark: "处理备注",
    frozenTime: "冻结时间",
    ignore: "忽略",
    faq: "常见问题",
    simplifiedChinese: "中文简体",
    traditionalChinese: "中文繁体",
    english: "英文",
    cambodian: "柬文",
    addTime: "添加时间",
    description: "描述",
    download: "下载",
    sureToDeal: "确认处理该条反馈吗",
    sureToIgnore: "确认要执行忽略操作吗",
    sureToDelete: "确认要删除该问题吗",
  },
  safe: {
    securitySettings: "安全设置",
    sensitiveWords: "敏感词库管理",
    chineseIP: "中国IP",
    cambodianIP: "柬埔寨IP",
    otherIP: "其他IP",
    addTime: "添加时间",
    blacklistForCountryCode: "国际区号黑名单管理",
    restrictedCountry: "被限制的国家",
    countryCode: "国际区号",
    restrictedByCountryCode: "根据国际区号限制",
    ipRule: "IP规则",
    none: "无",
    have: "有",
    restryctedByIP: "根据IP限制",
    no: "否",
    yes: "是",
    enabledState: "启用状态",
    sureToDelete: "确认要删除此行吗",
    bothRestrictionsCanNotBeEmpty: "区号限制和IP限制, 两者都不能为否",
    selectCountryFirst: "请先选择国家, 两者都不能为否",
  },
  wallet: {
    walletManagement: "钱包管理",
    dashboard: "仪表盘",
    selectWallet: "选择钱包",
    mosappWallet: "MosApp钱包",
    uPayWallet: "U-Pay钱包",
    selectCurrency: "选择币种",
    selectDate: "选择时间段",
    yesterday: "昨日",
    today: "今日",
    sevenDays: "7 天",
    fifteenDays: "15天",
    oneMonth: "1 月",
    threeMonths: "3 月",
    oneYear: "1 年",
    accountBalance: "账户金额",
    userTopUpAmount: "用户充值金额",
    userTransferAmount: "用户转账金额",
    redPacketAmount: "交易红包金额",
    transactionTimes: "交易次数",
    userTopUpTimes: "用户充值次数",
    userTransferTimes: "用户转账次数",
    redPacketTimes: "交易红包次数",
    rewardAmount: "用户打赏金额",
    rewardCount: "用户打赏次数",
    users: "用户数",
    basicUsers: "普通用户数",
    kycUsers: "KYC认证用户数",
    fullKYCUsers: "Full-KYC认证用户数",
    addedYesterday: "昨日新增",
    orders: "交易订单",
    orderID: "订单编号",
    orderType: "订单类型",
    topUp: "充值订单",
    transfer: "转账订单",
    redPacket: "群组红包订单",
    channelRedPacket: "频道红包订单",
    referralBonus: "推广奖励",
    usersInformation: "用户信息",
    transactionTime: "交易时间",
    orderState: "订单状态",
    succesfully: "交易成功",
    inProgress: "交易中",
    failed: "交易失败",
    details: "详情",
    orderDetails: "订单详情",
    country: "国家",
    currency: "币种",
    transactionAmount: "交易金额",
    totalAmount: "累计",
    paymentMethod: "支付方式",
    paymentAccount: "支付账号",
    paymentAccountName: "支付账号名称",
    dailyTransactionData: "每日交易数据",
    date: "日期",
    totalAccountAmount: "累计账户金额",
    addedAccountAmount: "新增账户金额",
    addedTopUpAmount: "用户新增充值金额",
    addedTopUpTimes: "用户新增充值次数",
    addedRedPacketAmount: "新增交易红包金额",
    addedRedPacketTimes: "新增交易红包次数",
    addedTransferAmount: "新增交易转账金额",
    addedTransferTimes: "新增交易转账次数",
    totalUsers: "累计用户数",
    addedUsers: "新增用户数",
    usersAccountID: "用户账号编号",
    usersState: "用户状态",
    usersLevel: "用户等级",
    accountAmount: "当前账户金额",
    totalReceiveAmount: "累计收款金额",
    totalTransferAmount: "累计转账金额",
    totalsendRedPacketAmount: "累计发红包金额",
    exportTimeLimit: "导出的时间间隔不能大于35天",
    selectTimeLimit: "请选择时间间隔小于35天的交易时间",
    userAttr: "昵称/ID/手机号",
    wallet: "钱包",
    targetUserNickname: "目标用户昵称",
    targetUserId: "目标用户ID",
    targetUserPhoneNumber: "目标手机号码",
    envelopeType: "红包类型",
    envelopeCount: "红包个数",
    envelopeTotal: "红包总金额",
    grabTime: "抢红包时间",
    grabAmount: "红包金额",
    unknown: "未知",
    ordinary: "均分",
    lucky: "拼手气",
    normalUser: "普通用户",
    payAccount: "支付帐号",
    payAccountName: "支付帐号名称",
    KHQR收款: "KHQR收款",
    Upay账户充值: "Upay账户充值",
    selectTotal: "选中合计",
    pageTotal: "本页合计",
    VIP_SUBSCRIBE_PAY: "vip订阅支付",
    PAID_GROUP_PAY: "付费群支付",
    CARD_APPLY: "卡申请",
    MINI_APP_PAY: "小程序支付",
    KHQR_WITHDRAW: "khqr提现",
    KHQR_PAY: "khqr转账",
    KHQR_PAY_CENTRAL: "khqr转账央行",
    rewardOrder: "打赏订单",
    userAddRewardAmount: '用户新增打赏金额',
    userAddRewardCount: '用户新增打赏次数'
  },
  monitor: {
    attribute: "属性",
    value: "值",
    coreCount: "核心数",
    userUsageRate: "用户使用率",
    systemUsageRate: "系统使用率",
    currentIdleRate: "当前空闲率",
    property: "属性",
    memory: "内存",
    totalMemory: "总内存",
    usedMemory: "已用内存",
    remainingMemory: "剩余内存",
    usageRate: "使用率",
    serverInformation: "服务器信息",
    serverName: "服务器名称",
    operatingSystem: "操作系统",
    serverIP: "服务器IP",
    systemArchitecture: "系统架构",
    javaVirtualMachineInformation: "Java虚拟机信息",
    javaName: "Java名称",
    javaVersion: "Java版本",
    startupTime: "启动时间",
    uptime: "运行时长",
    installationPath: "安装路径",
    projectPath: "项目路径",
    runtimeParameters: "运行参数",
    diskStatus: "磁盘状态",
    diskDrivePath: "盘符路径",
    fileSystem: "文件系统",
    driveType: "盘符类型",
    totalSize: "总大小",
    availableSize: "可用大小",
    usedSize: "已用大小",
    usedPercentage: "已用百分比",
    basicInformation: "基本信息",
    redisVersion: "Redis版本",
    runningMode: "运行模式",
    standalone: "单机",
    cluster: "集群",
    port: "端口",
    clientCount: "客户端数",
    uptimeDays: "运行时间(天)",
    usedCPU: "使用CPU",
    memoryConfiguration: "内存配置",
    aofEnabled: "AOF是否开启",
    rdbSuccessful: "RDB是否成功",
    keyCount: "Key数量",
    networkInOut: "网络入口/出口",
    commandStatistics: "命令统计",
    memoryInformation: "内存信息",
    memoryConsumption: "内存消耗",
    peak: "峰值",
    command: "命令",
  },
  system: {
    userAgreement: "用户协议",
    privacyPolicy: "隐私政策",
    roleName: "角色名称",
    permissionCharacter: "权限字符",
    roleStatus: "角色状态",
    roleCode: "角色编号",
    displayOrder: "显示顺序",
    dataPermissions: "数据权限",
    assignUsers: "分配用户",
    roleOrder: "角色顺序",
    menuPermissions: "菜单权限",
    permissionScope: "权限范围",
    loading: "加载中，请稍候",
    expandCollapse: "展开/折叠",
    selectAllDeselectAll: "全选/全不选",
    parentChildLinkage: "父子联动",
    confirmDelete: "确定要删除这一项吗：",
    allocateDataPermissions: "分配数据权限",
    allDataPermissions: "全部数据权限",
    customDataPermissions: "自定数据权限",
    departmentDataPermissions: "本部门数据权限",
    departmentandBelowDataPermissions: "本部门及以下数据权限",
    onlyMyDataPermissions: "仅本人数据权限",
    batchRevokeAuthorization: "批量取消授权",
    revokeAuthorization: "取消授权",
    confirmRevokeAuthorization: "确认要取消授权该用户吗：",
    userName: "用户名称",
    userID: "用户编号",
    role: "角色",
    lastLoginTime: "最后登录时间",
    resetPassword: "重置密码",
    newPassword: '"的新密码',
    dictionaryName: "字典名称",
    dictionaryType: "字典类型",
    dictionaryStatus: "字典状态",
    dictionaryCode: "字典编号",
    dictionaryLabel: "字典标签",
    dictionaryNum: "字典编码",
    dictionaryKey: "字典键值",
    dictionaryOrder: "字典排序",
    refreshCache: "刷新缓存",
    parameterName: "参数名称",
    parameterKeyName: "参数键名",
    systemBuiltIn: "系统内置",
    parameterPrimaryKey: "参数主键",
    parameterKeyValue: "参数键值",
    logID: "日志编号",
    systemModule: "系统模块",
    operationType: "操作类型",
    requestMethod: "请求方式",
    operator: "操作人员",
    host: "主机",
    operationStatus: "操作状态",
    operationDate: "操作日期",
    type: "类型",
    clearOperateLog: "是否确认清空所有操作日志数据项",
    operationLogDetails: "操作日志详细",
    operationModule: "操作模块",
    requestAddress: "请求地址",
    operationMethod: "操作方法",
    requestParameters: "请求参数",
    returnParameters: "返回参数",
    operationTime: "操作时间",
    exceptionInformation: "异常信息",
    loginInfo: "登录信息",
    normal: "正常",
    failure: "失败",
    loginAddress: "登录地址",
    loginStatus: "登录状态",
    loginTime: "登录时间",
    accessID: "访问编号",
    address: "地址",
    loginLocation: "登录地点",
    operatingSystem: "操作系统",
    browser: "浏览器",
    description: "描述",
    accessTime: "访问时间",
    noDataAvailable: "暂无数据",
    menuName: "菜单名称",
    icon: "图标",
    sorting: "排序",
    permissionIdentifier: "权限标识",
    componentPath: "组件路径",
    parentMenu: "上级菜单",
    selectParentMenu: "选择上级菜单",
    menuType: "菜单类型",
    menuIcon: "菜单图标",
    directory: "目录",
    menu: "菜单",
    button: "按钮",
    isExternalLink: "是否外链",
    routeAddress: "路由地址",
    routeParameters: "路由参数",
    isCachingEnabled: "是否缓存",
    displayStatus: "显示状态",
    menuStatus: "菜单状态",
    clickToSelect: "点击选择图标",
    selectUser: "选择用户",
    setPermissionTag: "请设置操作权限标签值",
    setRolePerTag: "请设置角色权限标签值",
    clearAllScheduleLog: "是否确认清空所有调度日志数据项?",
    clearAllLoginLog: "是否确认清空所有登录日志数据项?",
    confirmForceExit: '是否确认强制退出 "',
    notAssigned: "还未分配角色",
    dataLabel: "数据标签",
    dataKeyValue: "数据键值",
    styleAttribute: "样式属性",
    echoStyle: "回显样式",
    default: "默认",
    primary: "主要",
    success: "成功",
    info: "信息",
    warning: "警告",
    danger: "危险",
    cache: "缓存",
    noCache: "不缓存",
    mainCategory: "主类目",
    deptName: "部门名称",
  },

  menu: {
    menuName: "菜单名称",
  },
  // 营销活动
  marking: {
    addMarketingActivity: "新建营销活动",
    withdrawalLevelManagement: "提现档位管理",
    activityName: "活动名称",
    activityType: "活动类型",
    activityStatus: "活动状态",
    inviteNewUserActivity: "拉新活动",
    fissionActivity: "裂变活动",
    fissionGrade: "裂变级数",
    levelReward: "{0}级拉新奖励",
    singleInviteeRewardUpperLimit: "单个邀请者奖励上限",
    fissionRewardUpperLimit: "裂变奖励上限",
    createMarketingActivity: "新建营销活动",
    activityTime: "活动时间",
    isEnabled: "是否启用",
    submitAudit: "提交审核",
    audit: "审核",
    data: "数据",
    status: {
      draft: "草稿",
      auditing: "审核中",
      rejected: "被驳回",
      notStarted: "未开始",
      underway: "进行中",
      ended: "已结束",
    },
    configureMarketingActivity: "配置营销活动",
    auditMarketingActivity: "审核营销活动",
    viewMarketingActivity: "查看营销活动",
    taskDeadline: "任务期限",
    hour: "小时",
    rewardCoin: "奖励币种",
    rewardCondition: "奖励条件",
    needWallet: "需开通钱包",
    noNeedWallet: "无需开通钱包",
    needKyc: "需完成KYC认证",
    noNeedKyc: "无需完成KYC认证",
    newUserReward: "新用户奖励",
    limit: "上限",
    lowerLimit: "下限",
    newUserRewardTotalAmountUpperLimit: "新用户奖励总金额上限",
    needInvitation: "需邀请人数",
    rewardAmount: "奖励金额",
    reserveReward: "保底奖励",
    byInvitationRatio: "按邀请人数比例发放奖励",
    none: "无",
    activityRules: "活动规则",
    auditResult: "审核结果",
    pass: "通过",
    reject: "驳回",
    rejectReason: "驳回原因",
    securityKey: "验证码",
    activityData: "活动数据",
    historyCumulative: "历史累计",
    issuedAmount: "已发放金额",
    issuedAmountTip: "已经发放的奖励金额",
    newUserRewardAmount: "新用户领取金额",
    newUserRewardAmountTip: "新注册的用户领取的金额",
    inviteUserRewardAmount: "邀请者领取金额",
    inviteUserRewardAmountTip: "邀请者领取的金额",
    expectedAmount: "预计发放金额",
    expectedAmountTip:
      "进行中的任务，如果有开启保底，预计的保底奖励金额+完成任务后但是未领取的奖励金额",
    participationNumber: "参与人次",
    participationNumberTip: "参与活动的人次",
    inviteNewUser: "邀请新用户",
    inviteNewUserTip: "通过邀请链接注册的新用户数",
    completedTaskNumber: "完成任务人次",
    completedTaskNumberTip: "有完成任务且奖励金额≠0的数量",
    activityPageViews: "活动页浏览次数",
    activityPageViewsTip: "访问活动页的次数，同一用户30分钟内不重复计算",
    inviteUserNumber: "邀请次数",
    inviteUserNumberTip: "点击【邀请新用户】的次数",
    comparedWith1DayAgo: "较前天",
    comparedWith7DaysAgo: "较前7日",
    comparedWith30DaysAgo: "较前30日",
    activityNameLimit: "活动名称需在1-30个字符之间",
    durationLimit: "任务期限需在1-9999范围内",
    betweenLimit: "{0}需大于{1}，小于等于{2}",
    betweenLimit1: "{0}需大于等于{1}，小于等于{2}",
    mustGreaterThan: "{0}不能小于{1}",
    cannotGreaterThan: "{0}不能大于{1}",
    canNotContainDecimals: "金额不能包含小数",
    targetAudience: "适用人群",
    targetAudienceTip:
      "请输入用户手机号,按回车换行,如:\n855123456789\n855987654321",
    allUsers: "全部用户",
    specificUsers: "指定用户",
  },

  // 会员套餐
  vip: {
    type: "套餐类型",
    firstPurchaseDiscount: "首购折扣",
    status: "启用状态",
    discount: "减免折扣",
    payableAmount: "应付金额",
    crossedAmount: "划线金额",
    monthlyAutoRenewal: "连续包月",
    quarterlyAutoRenewal: "连续包季",
    yearlyAutoRenewal: "连续包年",
    year: "1年",
    quarter: "1季",
    month: "1月",
    package: "会员套餐",
    checkDiscount: "1-100整数",
    checkPayableAmount: "大于0且小于99999.99两位小数",
    checkCrossedAmount: "划线金额需大于应付金额",
  },

  // 客服管理
  customer: {
    sname: "客服号名称",
    slink: "客服号链接",
    avatar: "头像",
    conversationNum: "会话数",
    adminNum: "坐席数",
    name: "名称",
    link: "链接",
    ownerId: "拥有者ID",
    ownerName: "拥有者名称",
    createTime: "创建时间",
    conversation: "群组会话",
    admin: "客服坐席",
    type: "类型",
    normal: "用户",
    guest: "游客",
    robot: "机器人",
    customerNum: "客户数",
    customer: "客户",
    joinTime: "加入时间",
  },

  // 推荐内容管理
  recommend: {
    status: "状态",
    all: "全部",
    audit: "待人工审核",
    recommend: "已推荐",
    deprecated: "不推荐",
    channelName: "频道名称",
    sender: "发布者",
    content: "内容",
    recommendTime: "推送时间",
    operate: "操作",
    detail: "详情",
    manualReview: "人工审核",
    createTime: "创建时间",
    startTime: "开始时间",
    endTime: "结束时间",
    reject: "拒绝",
    recommendAudit: "推荐",
    redEnvelopeBlessing: "红包祝福语",
    confirmPush: "确定要推送该帖子吗？",
    confirmReject: "确定要拒绝推送该帖子吗？",
  },

  // 菜单 （对创建出来的内容可通过该方式进行国际化）
  首页: "首页",
  数据统计: "数据统计",
  用户留存: "用户留存",
  通讯录: "通讯录",
  用户管理: "用户管理",
  收费管理: "收费管理",
  会员套餐: "会员套餐",
  客服管理: "客服管理",
  客服消息管理: "客服消息管理",
  客服号管理: "客服号管理",
  游客管理: "游客管理",
  群组管理: "群组管理",
  频道管理: "频道管理",
  群成员管理: "群成员管理",
  频道认证审核: "频道认证审核",
  营销管理: "营销管理",
  营销活动: "营销活动",
  爬虫管理: "爬虫管理",
  目标管理: "目标管理",
  内容库: "内容库",
  机器人管理: "机器人管理",
  MOS助手: "MOS助手",
  mos助手消息: "mos助手消息",
  通知管理: "通知管理",
  欢迎语管理: "欢迎语管理",
  自动回复管理: "自动回复管理",
  钱包管理: "钱包管理",
  仪表盘: "仪表盘",
  交易订单: "交易订单",
  每日交易数据: "每日交易数据",
  账户管理: "账户管理",
  版本管理: "版本管理",
  聊天信息管理: "聊天信息管理",
  消息管理: "消息管理",
  通话记录: "通话记录",
  用户维护: "用户维护",
  反馈信息管理: "反馈信息管理",
  举报管理: "举报管理",
  常见问题: "常见问题",
  安全设置: "安全设置",
  敏感词库管理: "敏感词库管理",
  国际区号黑名单管理: "国际区号黑名单管理",
  系统管理: "系统管理",
  协议与隐私: "协议与隐私",
  角色管理: "角色管理",
  字典管理: "字典管理",
  参数设置: "参数设置",
  日志管理: "日志管理",
  操作日志: "操作日志",
  登录日志: "登录日志",
  菜单管理: "菜单管理",
  系统监控: "系统监控",
  服务监控: "服务监控",
  缓存监控: "缓存监控",

  系统工具: "系统工具",
  部门管理: "部门管理",
  岗位管理: "岗位管理",
  通知公告: "通知公告",
  在线用户: "在线用户",
  定时任务: "定时任务",
  数据监控: "数据监控",
  表单构建: "表单构建",
  代码生成: "代码生成",
  系统接口: "系统接口",
  banner管理: "banner管理",
  客户端设置: "客户端设置",
  支付配置: "支付配置",
  单聊消息管理: "单聊消息管理",
  群聊消息管理: "群聊消息管理",
  资金管理: "资金管理",
  账单统计: "账单统计",
  充值记录: "充值记录",
  提现审核: "提现审核",
  提现记录: "提现记录",
  转账记录: "转账记录",
  反馈管理: "反馈管理",
  话题管理: "话题管理",
  动态管理: "动态管理",
  公告管理: "公告管理",
  组织架构: "组织架构",
  组织架构职位管理: "组织架构职位管理",
  组织架构部门管理: "组织架构部门管理",
  服务号发布管理: "服务号发布管理",
  清空token: "清空token",
  国号黑名单管理: "国号黑名单管理",
  留言反馈管理: "留言反馈管理",
  欢迎语: "欢迎语",
  自动回复设置: "自动回复设置",
  爬虫配置: "爬虫配置",
  频道运营: "频道运营",
  频道消息管理: "频道消息管理",
  推荐内容管理: "推荐内容管理",

  // admin后台创建的字典
  停用: "停用",
  正常: "正常",
  男: "男",
  女: "女",
  未知: "未知",
  显示: "显示",
  隐藏: "隐藏",
  暂停: "暂停",
  默认: "默认",
  系统: "系统",
  是: "是",
  否: "否",
  通知: "通知",
  公告: "公告",
  关闭: "关闭",
  新增: "新增",
  修改: "修改",
  删除: "删除",
  授权: "授权",
  导出: "导出",
  导入: "导入",
  强退: "强退",
  生成代码: "生成代码",
  清空数据: "清空数据",
  成功: "成功",
  失败: "失败",
};
