import request from '@/utils/request'

export function listVersion (params) {
  return request({
    url: '/versions',
    method: 'get',
    params: params
  })
}

export function updateVersion (data) {
  return request({
    url: '/versions',
    method: 'put',
    data: data
  })
}

export function createVersion (data) {
  return request({
    url: '/versions',
    method: 'post',
    data: data
  })
}

export function updateVersionStatus (data) {
  return request({
    url: '/versions/status',
    method: 'put',
    data: { status: !data.status, versionId: data.versionId }
  })
}
