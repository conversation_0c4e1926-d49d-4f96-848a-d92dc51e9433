import request from '@/utils/request'

// 查询菜单列表
export function pageMoments (data) {
    return request({
      url: '/article/userFollowPage',
      method: 'post',
      data: data
    })
  }

// 查看详情
export function infoMoments (data) {
  return request({
    url: '/article/info',
    method: 'post',
    data: data
  })
}

// 删除动态
export function deleteMoments (data) {
  return request({
    url: '/article/delete',
    method: 'post',
    data: data
  })
}

