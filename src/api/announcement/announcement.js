import request from '@/utils/request'

// 查询公告列表
export function getNoticeList (data) {
  return request({
    url: '/notice/list',
    method: 'post',
    data: data
  })
}
// 查询公告列表
export function getNoticeUserFilter (data) {
  return request({
    url: '/notice/getNoticeUserFilter',
    method: 'post',
    data: data
  })
}

// 查询公告详情
export function getNoticeDetail (id) {
  return request({
    url: '/notice/detail?id=' + id,
    method: 'get'
  })
}
// 新增公告
export function addNotice (data) {
  return request({
    url: '/notice/add',
    method: 'post',
    data: data
  })
}

export function editNotice (data) {
  return request({
    url: '/notice/edit',
    method: 'post',
    data: data
  })
}

// 发布公告
export function release (id) {
  return request({
    url: '/notice/release?id=' + id,
    method: 'get'
  })
}

// 删除公告
export function deleteNotice (data) {
  return request({
    url: '/notice/delete',
    method: 'post',
    data: data
  })
}

// 新增时获取接收方列表
export function receivers (data) {
  return request({
    url: '/notice/receivers',
    method: 'post',
    data: data
  })
}

// 获取接收方详情
export function receiversInfo (data) {
  return request({
    url: '/notice/receiversInfo',
    method: 'get',
    params: data
  })
}

// 发布进度人员列表
export function sendList (data) {
  return request({
    url: '/notice/sendList',
    method: 'post',
    data: data
  })
}

// 重发公告
export function resend (id) {
  return request({
    url: '/notice/resend?id=' + id,
    method: 'get'
  })
}
