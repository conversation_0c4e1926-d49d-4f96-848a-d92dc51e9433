import request from '@/utils/request'

// 获取用户注册统计图
export const getUserRegistry = (params) => {
  return request({
    url: '/index/userRegistryCount',
    method: 'get',
    params
  })
}

// 获取单聊统计图
export const getSingleMessage = (params) => {
  return request({
    url: '/index/singleMessage',
    method: 'get',
    params
  })
}

// 获取建群统计图
export const getCreateGroup = (params) => {
  return request({
    url: '/index/groupCreate',
    method: 'get',
    params
  })
}

// 获取建群统计图
export const groupCount = () => {
  return request({
    url: '/index/count',
    method: 'get'
  })
}

// 获取用户活跃统计图
export const userActiveCount = (params) => {
  return request({
    url: '/index/userActiveCount',
    method: 'get',
    params
  })
}

// 获取用户留存率计图
export const userRetentionCount = (params) => {
  return request({
    url: '/index/userRetentionCount',
    method: 'get',
    params
  })
}
