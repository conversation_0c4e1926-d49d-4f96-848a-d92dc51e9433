import request from '@/utils/request'
export function userAuth (data) {
  return request({
    url: '/imUser/userAuth',
    method: 'post',
    data: data
  })
}
export function getAuthRecord (data) {
  return request({
    url: '/imUser/getAuthRecord',
    method: 'post',
    data: data
  })
}

export function cancelAuth (data) {
  return request({
    url: '/imUser/cancelAuth',
    method: 'post',
    data: data
  })
}
