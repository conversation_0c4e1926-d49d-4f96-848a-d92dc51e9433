import request from '@/utils/request'

// 查询banner列表
export function getConfigList (data) {
  return request({
    url: '/tg/configPage',
    method: 'post',
    data: data
  })
}

// 新增banner
export function addConfig (data) {
  return request({
    url: '/tg/addConfig',
    method: 'post',
    data: data
  })
}

// 删除
export function deleteConfig (data) {
  return request({
    url: '/tg/deleteConfig',
    method: 'post',
    data: data
  })
}

// 使用改配置进行爬虫
export function startSpider (data) {
  return request({
    url: '/tg/start',
    method: 'get',
    params: data
  })
}

// 执行python脚本
export function startPython (data) {
  return request({
    url: '/tg/startPython',
    method: 'post',
    data: data
  })
}

// 切换状态
export function changeStatus (data) {
  return request({
    url: '/tg/changeStatus',
    method: 'post',
    data: data
  })
}
