import request from '@/utils/request'

// 查询部门下拉树结构
export function treeSelect () {
  return request({
    url: '/organization/treeSelect',
    method: 'get'
  })
}

// 查询部门列表
export function listDept (query) {
  return request({
    url: '/organization/dept/list',
    method: 'get',
    params: query
  })
}

// 查询部门详细
export function getDept (deptId) {
  return request({
    url: '/organization/dept/' + deptId,
    method: 'get'
  })
}


// 新增部门
export function addDept (data) {
  console.log(data);
  return request({
    url: '/organization/dept',
    method: 'post',
    data: data
  })
}

// 修改部门
export function updateDept (data) {
  return request({
    url: '/organization/dept',
    method: 'put',
    data: data
  })
}

// 删除部门
export function delDept (deptId) {
  return request({
    url: '/organization/dept/' + deptId,
    method: 'delete'
  })
}
