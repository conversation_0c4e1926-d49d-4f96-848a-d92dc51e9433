import request from '@/utils/request'

// 查询职位列表
export function positionSelect (query) {
  return request({
    url: '/organizationPosition/list',
    method: 'get',
    params: query
  })
}

//保存职位列表
export function addPosition (data) {
  return request({
    url: '/organizationPosition',
    method: 'post',
    data: data
  })
}

// 删除职位列表
export function delPosition (id) {
  return request({
    url: '/organizationPosition/'+id,
    method: 'delete'
  })
}
