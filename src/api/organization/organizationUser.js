import request from '@/utils/request'

// 查询用户列表
export function listOrganizationUser (query) {
  return request({
    url: '/organization/list',
    method: 'get',
    params: query
  })
}

// 查询用户详情
export function getOrganizationUser (query) {
  console.log(query);
  return request({
    url: '/organization/userInfo/'+query,
    method: 'get'
  })
}



// 切换部门
export function switchDept (data) {
  return request({
    url: '/organization/switchDept',
    method: 'put',
    data: data
  })
}