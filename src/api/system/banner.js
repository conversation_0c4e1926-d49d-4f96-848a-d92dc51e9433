import request from '@/utils/request'

// 查询banner列表
export function getBannerList (data) {
  return request({
    url: '/imSystem/listBanner',
    method: 'post',
    data: data
  })
}

// 新增banner
export function addB (data) {

  return request({
    url: '/imSystem/addBanner',
    method: 'post',
    data: data
  })
}

// 删除banner
export function deleteNotice (data) {
  return request({
    url: '/imSystem/delBanner',
    method: 'post',
    data: data
  })
}

// 修改banner的开启状态
export function changeBanner (data) {
  return request({
    url: '/imSystem/changeState',
    method: 'post',
    data: data
  })
}
