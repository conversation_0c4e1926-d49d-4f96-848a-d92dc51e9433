import request from '@/utils/request'

export function pageVipPackageList (data) {
  return request({
    url: '/vipLevel/pageVipLevel',
    method: 'post',
    data: data
  })
}


export function addVipPackage (data) {
  return request({
    url: '/vipLevel/addVipLevel',
    method: 'post',
    data: data
  })
}

export function editVipPackage (data) {
  return request({
    url: '/vipLevel/editVipLevel',
    method: 'post',
    data: data
  })
}


export function deleteVipPackage (data) {
  return request({
    url: '/vipLevel/deleteVipLevel',
    method: 'get',
    params: data
  })
}


export function enableLevel (data) {
  return request({
    url: '/vipLevel/enableLevel',
    method: 'get',
    params: data
  })
}


export function enableDiscount (data) {
  return request({
    url: '/vipLevel/enableDiscount',
    method: 'get',
    params: data
  })
}
