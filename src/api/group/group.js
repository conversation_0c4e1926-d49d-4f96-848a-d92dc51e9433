import request from '@/utils/request'

// 查询菜单列表
export function pageGroupList (data) {
    return request({
      url: '/group/pageGroupList',
      method: 'post',
      data: data
    })
  }

// 封禁或解封群聊
export function unsealOrBanGroup (data) {
  return request({
    url: '/group/unsealOrBanGroup',
    method: 'post',
    data: data
  })
}

// 查看群成员
export function pageGroupMember (data) {
  return request({
    url: '/group/pageGroupMember',
    method: 'post',
    data: data
  })
}
