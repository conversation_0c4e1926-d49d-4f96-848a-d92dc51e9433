import request from '@/utils/request'

export function listData (params) {
  return request({
    url: '/blacklists',
    method: 'get',
    params
  })
}

export function createData (data) {
  console.log('submit', data);
  return request({
    url: '/blacklists',
    method: 'post',
    data
  })
}

export function updateStatus (data) {
  return request({
    url: '/blacklists/status',
    method: 'put',
    data: { id: data.id, status: !data.status }
  })
}

export function updateData (data) {
  return request({
    url: '/blacklists',
    method: 'put',
    data: data
  })
}

export function deleteData (data) {
  return request({
    url: `/blacklists/${data.id}`,
    method: 'delete'
  })
}

export function bulkDelete (items) {
  const ids = items.map(item => item.id)
  return request({
    url: `/blacklists/${ids}`,
    method: 'delete'
  })
}

export function countryIp (data) {
  return request({
    url: '/country-ip',
    method: 'get',
    params: data
  })
}
