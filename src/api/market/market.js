/*
 * @Author: Sky
 * @Date: 2024-07-16 11:53:56
 * @LastEditors: Sky
 * @LastEditTime: 2024-07-17 15:09:21
 * @Description: 营销页面相关接口
 */
import request from '@/utils/request'

// 查询列表
export function pageMarketList (data) {
  return request({
    url: '/market/activity/page',
    method: 'post',
    data: data
  })
}

// 新增
export function addMarket (data) {
  return request({
    url: '/market/activity/add',
    method: 'post',
    data: data
  })
}

// 审核
export function audit (data) {
  return request({
    url: '/market/activity/audit',
    method: 'post',
    data: data
  })
}

// 删除活动
export function deleteActivity (data) {
  return request({
    url: '/market/activity/delete',
    method: 'get',
    params: data
  })
}

// 编辑活动
export function editActivity (data) {
  return request({
    url: '/market/activity/edit',
    method: 'post',
    data: data
  })
}

// 启用-关闭活动
export function enableActivity (data) {
  return request({
    url: '/market/activity/enable',
    method: 'get',
    params: data
  })
}

// 查看活动详情
export function getInfoActivity (data) {
  return request({
    url: '/market/activity/getInfo',
    method: 'get',
    params: data
  })
}

// 提交审核
export function submitAudit (data) {
  return request({
    url: '/market/activity/submitAudit',
    method: 'get',
    params: data
  })
}

// 获取活动统计数据
export function getActivityStatics (data) {
  return request({
    url: '/market/activity/getActivityStatics',
    method: 'post',
    data: data
  })
}

// 获取活动统计折线图数据
export function getActivityStaticsLineChart (data) {
  return request({
    url: '/market/activity/getActivityStaticsLineChart',
    method: 'post',
    data: data
  })
}
