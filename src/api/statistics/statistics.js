import request from '@/utils/request'
import { parseStrEmpty } from '@/utils/ruoyi'

// 查询用户列表
export function listUser (data) {
  return request({
    url: '/imUser/list',
    method: 'post',
    data: data
  })
}
// 查询用户列表
export function pageNotice (data) {
  return request({
    url: '/retention/list',
    method: 'post',
    data: data
  })
}
// 添加用户
export function saveUser (data) {
  return request({
    url: '/imUser/add',
    method: 'post',
    data: data
  })
}
// 修改用户密码
export function modifyPassword (data) {
  return request({
    url: '/imUser/modifyPassword',
    method: 'post',
    data: data
  })
}
// 查看登录记录
export function loginHistory (data) {
  return request({
    url: '/imUser/registerHistory',
    method: 'post',
    data: data
  })
}
// 手动充值
export function artificialRecharge (data) {
  return request({
    url: '/imUser/artificialRecharge',
    method: 'post',
    data: data
  })
}
// 查询银行卡列表
export function listBankCard (data) {
  return request({
    url: '/pay/listBankCard',
    method: 'post',
    data: data
  })
}
// 手动提现
export function artificialDraw (data) {
  return request({
    url: '/imUser/artificialDraw',
    method: 'post',
    data: data
  })
}
// 查看用户账单
export function listBill (data) {
  return request({
    url: '/pay/listBill',
    method: 'post',
    data: data
  })
}
// 用户设置客服
export function modifyCusService (data) {
  return request({
    url: '/imUser/modifyCusService',
    method: 'post',
    data: data
  })
}
// 设置提现审核
export function modifyCheckDraw (data) {
  return request({
    url: '/imUser/modifyCheckDraw',
    method: 'post',
    data: data
  })
}
// 查看客服信息和提现审核信息
export function selectUserExpand (data) {
  return request({
    url: '/imUser/selectUserExpand',
    method: 'post',
    data: data
  })
}
// 封号或解封
export function userBan (data) {
  return request({
    url: '/imUser/userBan',
    method: 'post',
    data: data
  })
}

// 用户冻结或解冻
export function userFreeze (data) {
  return request({
    url: '/imUser/freeze',
    method: 'post',
    data: data
  })
}

// 群组冻结或解冻
export function groupFreeze (data) {
  return request({
    url: '/group/freeze',
    method: 'post',
    data: data
  })
}

// 发送消息
export function sendMessage (data) {
  return request({
    url: '/imUser/sendMessage',
    method: 'post',
    data: data
  })
}
// 导出excel
export function exportRetention (data) {
  return request({
    url: '/retention/export',
    method: 'post',
    data: data
  })
}

// 查询用户详细
export function getUser (userId) {
  return request({
    url: '/system/user/' + parseStrEmpty(userId),
    method: 'get'
  })
}

// 新增用户
export function addUser (data) {
  return request({
    url: '/system/user',
    method: 'post',
    data: data
  })
}

// 修改用户
export function updateUser (data) {
  return request({
    url: '/system/user',
    method: 'put',
    data: data
  })
}

// 删除用户
export function delUser (userId) {
  return request({
    url: '/system/user/' + userId,
    method: 'delete'
  })
}

// 用户密码重置
export function resetUserPwd (userId, password) {
  const data = {
    userId,
    password
  }
  return request({
    url: '/system/user/resetPwd',
    method: 'put',
    data: data
  })
}

// 用户状态修改
export function changeUserStatus (userId, status) {
  const data = {
    userId,
    status
  }
  return request({
    url: '/system/user/changeStatus',
    method: 'put',
    data: data
  })
}

// 查询用户个人信息
export function getUserProfile () {
  return request({
    url: '/system/user/profile',
    method: 'get'
  })
}

// 修改用户个人信息
export function updateUserProfile (data) {
  return request({
    url: '/system/user/profile',
    method: 'put',
    data: data
  })
}

// 用户密码重置
export function updateUserPwd (oldPassword, newPassword) {
  const data = {
    oldPassword,
    newPassword
  }
  return request({
    url: '/system/user/profile/updatePwd',
    method: 'put',
    params: data
  })
}

// 用户头像上传
export function uploadAvatar (data) {
  return request({
    url: '/system/user/profile/avatar',
    method: 'post',
    data: data
  })
}

// 查询授权角色
export function getAuthRole (userId) {
  return request({
    url: '/system/user/authRole/' + userId,
    method: 'get'
  })
}

// 保存授权角色
export function updateAuthRole (data) {
  return request({
    url: '/system/user/authRole',
    method: 'put',
    params: data
  })
}

// 获取账号信息
export function getListByIds (data) {
  return request({
    url: '/imUser/getListByIds',
    method: 'post',
    data
  })
}

// 查询用好友列表
export function getFriend (data) {
  return request({
    url: '/imUser/getFriend',
    method: 'post',
    data: data
  })
}
