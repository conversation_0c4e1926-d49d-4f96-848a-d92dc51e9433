import request from '@/utils/request'
export function getWalletStatistics (params) {
  return request({
    url: '/wallet/getWalletStatistics',
    method: 'post',
    data:params
  })
}
export function getWalletStatisticsYesterday (params) {
  return request({
    url: '/wallet/getWalletStatisticsYesterday',
    method: 'post',
    data:params
  })
}
export function getWalletBalanceLineChart (params) {
  return request({
    url: '/wallet/getWalletBalanceLineChart',
    method: 'post',
    data:params
  })
}
export function getWalletUserCountLineChart (params) {
  return request({
    url: '/wallet/getWalletUserCountLineChart',
    method: 'post',
    data:params
  })
}

export function getTransactionOrderList (params) {
  return request({
    url: '/wallet/getTransactionOrderList',
    method: 'post',
    data:params
  })
}

export function getRechargeOrderInfo (params) {
  return request({
    url: '/wallet/getRechargeOrderInfo',
    method: 'get',
    params
  })
}

export function getTransferOrderInfo (params) {
  return request({
    url: '/wallet/getTransferOrderInfo',
    method: 'get',
    params
  })
}
export function getRedEnvelopeOrderInfo (params) {
  return request({
    url: '/wallet/getRedEnvelopeOrderInfo',
    method: 'get',
    params
  })
}

export function getWalletStatisticsPage (params) {
  return request({
    url: '/wallet/getWalletStatisticsPage',
    method: 'post',
    data:params
  })
}

export function getWalletAccountList (params) {
  return request({
    url: '/wallet/getWalletAccountList',
    method: 'get',
    params
  })
}
