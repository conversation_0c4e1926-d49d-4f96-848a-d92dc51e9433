import request from '@/utils/request'

// 查询服务号消息列表
export function listNumber(query) {
    return request({
        url: '/serviceNumber/list',
        method: 'get',
        params: query
    })
}

// 查询服务号消息详细
export function getNumber(id) {
    return request({
        url: '/serviceNumber/' + id,
        method: 'get'
    })
}

// 新增服务号消息
export function addNumber(data) {
    return request({
        url: '/serviceNumber',
        method: 'post',
        data: data
    })
}

// 修改服务号消息
export function updateNumber(data) {
    return request({
        url: '/serviceNumber',
        method: 'put',
        data: data
    })
}

// 发布服务号消息
export function editStatus(id) {
    return request({
        url: '/serviceNumber/' + id,
        method: 'put'
    })
}

// 删除服务号消息
export function delNumber(id) {
    return request({
        url: '/serviceNumber/' + id,
        method: 'delete'
    })
}
