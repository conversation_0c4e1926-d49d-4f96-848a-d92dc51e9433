import request from '@/utils/request'
export function pageChannelList (data) {
  return request({
    url: '/group/pageChannelList',
    method: 'post',
    data: data
  })
}

export function pageChannelMember (data) {
  return request({
    url: '/group/pageChannelMember',
    method: 'post',
    data: data
  })
}

export function msgList (data) {
  return request({
    url: '/group/msgList',
    method: 'post',
    data: data
  })
}

export function changeOfficial (data) {
  return request({
    url: '/group/changeOfficial',
    method: 'post',
    data: data
  })
}

export function cancelAuth (data) {
  return request({
    url: '/group/cancelAuth',
    method: 'post',
    data: data
  })
}
