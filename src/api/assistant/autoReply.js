import request from '@/utils/request'

export const getLists =(data)=> {
  return request({
    url: `/automatic-response?keyword=${data.Bykeyword}&pageNum=${data.pageNum}&pageSize=${data.pageSize}`,
    method: 'GET',
  })
}

export const AddData =(data, method)=>{
  return request ({
    url: `automatic-response`,
    method:method,
    data:data
  })
}

export const makeDelete =(data)=>{
  return request ({
    url: `automatic-response/${data}`,
    method:'DELETE',
  })
}
