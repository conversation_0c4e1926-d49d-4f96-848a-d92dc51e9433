import request from '@/utils/request'

// 查询公告列表
export function getGreenting (data) {
  return request({
    url: '/greeting/page',
    method: 'POST',
    data: data
  })
}

// 查询公告详情
export function getGreetingDetail (id) {
  return request({
    url: '/notice/detail?id=' + id,
    method: 'GET'
  })
}
// 新增公告
export function addGreeting (data, method) {
  return request({
    url: '/greeting',
    method: method,
    data: data
  })
}

// 发布公告
export function releaseGreenting (data) {
  return request({
    url: `/greeting/${data}/release`,
    method: 'POST'
  })
}

// 删除公告
export function deleteGreenting (data) {
  return request({
    url: `/greeting?ids=${data}`,
    method: 'DELETE'
  })
}
