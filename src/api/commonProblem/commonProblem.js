import request from '@/utils/request'

// 查询菜单列表
export function pageCommonProblem (data) {
  return request({
    url: '/im/im-common-problem/list',
    method: 'post',
    data: data
  })
}

export function commonProblemDetail (data) {
  return request({
    url: '/im/im-common-problem/getById',
    method: 'post',
    data: data
  })
}

export function saveCommonProblem (data) {
  return request({
    url: '/im/im-common-problem/saveOrUpdate',
    method: 'post',
    data: data
  })
}

export function deleteCommonProblem (data) {
  return request({
    url: '/im/im-common-problem/removeById?id=' + data,
    method: 'get',
  })
}

