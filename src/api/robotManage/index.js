import request from '@/utils/request'
// 添加机器人
export function addRobot (data) {
  return request({
    url: '/robot/add',
    method: 'post',
    data: data
  })
}

// 列表
export function getLists (data) {
  return request({
    url: '/robot/getPage',
    method: 'post',
    data: data
  })
}

export function deleteRobot (data) {
  return request({
    url: '/robot/delete',
    method: 'post',
    data: data
  })
}
// 切换状态
export function changeStatus (data) {
  return request({
    url: '/robot/changeStatus',
    method: 'post',
    data: data
  })
}

// 详情
export function robotDetail (data) {
  return request({
    url: '/robot/detail',
    method: 'get',
    params: data
  })
}
// 编辑
export function editRobot (data) {
  return request({
    url: '/robot/edit',
    method: 'post',
    data: data
  })
}
// 模糊查询频道名称
export function channelNameSearch(data){
  return request({
    url: '/robot/channelNameSearch',
    method: 'post',
    data: data
  })
}
