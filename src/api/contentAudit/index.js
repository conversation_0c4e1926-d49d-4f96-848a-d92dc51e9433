import request from '@/utils/request'

export function pageDiscoverContent (params) {
  return request({
    url: '/message/pageDiscoverContent',
    method: 'post',
    data: params
  })
}

export function getRtfContent (data) {
  return request({
    url: '/message/getRtfContent',
    method: 'get',
    params: data
  })
}

export function getRedEnvelopInfo (data) {
  return request({
    url: '/message/getRedEnvelopInfo',
    method: 'get',
    params: data
  })
}

export function discoverContentAudit (data) {
  return request({
    url: '/message/discoverContentAudit',
    method: 'post',
    data: data
  })
}

