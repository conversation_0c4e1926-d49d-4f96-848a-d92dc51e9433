import request from '@/utils/request'

export function listData (query) {
  const { date, ...rest } = query || {}
  return request({
    url: '/reports',
    method: 'post',
    data: rest
  })
}

export function getData (id) {
  return request({
    url: `/reports/${id}`,
    method: 'get'
  })
}

export function getDataDetail (id) {
  return request({
    url: `/reports/${id}/conversations`,
    method: 'get'
  })
}

export function createData (data) {
  return request({
    url: '/reports/resolve',
    method: 'post',
    data
  })
}

export function ignoreData (data) {
  const { id, reporterClientId } = data
  return request({
    url: '/reports/ignore',
    method: 'put',
    data: { id, reporterClientId }
  })
}
