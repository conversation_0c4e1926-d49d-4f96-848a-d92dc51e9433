import request from '@/utils/request'

// 查询菜单列表
export function pageTopic (data) {
    return request({
      url: '/topic/pageTopic',
      method: 'post',
      data: data
    })
  }

  export function topicDetail (data) {
    return request({
      url: '/topic/getById',
      method: 'post',
      data: data
    })
  }

  export function saveTopic (data) {
    return request({
      url: '/topic/saveTag',
      method: 'post',
      data: data
    })
  }

  export function deleteTopic (data) {
    return request({
      url: '/topic/deleteTag',
      method: 'post',
      data: data
    })
  }

