import request from '@/utils/request'

// 查询菜单列表
export function pageSensitiveWord (data) {
  return request({
    url: '/im/im-sensitive-words/list',
    method: 'post',
    data: data
  })
}

export function sensitiveWordDetail (data) {
  return request({
    url: '/im/im-sensitive-words/getById',
    method: 'post',
    data: data
  })
}

export function saveSensitiveWord (data) {
  return request({
    url: '/im/im-sensitive-words/saveOrUpdate',
    method: 'post',
    data: data
  })
}

export function deleteSensitiveWord (data) {
  return request({
    url: '/im/im-sensitive-words/removeById?id=' + data.id,
    method: 'get',
  })
}

