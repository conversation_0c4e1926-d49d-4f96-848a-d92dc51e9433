import request from '@/utils/request'

// 查询公告列表
export function getLists (data) {
  return request({
    url: '/tg/dataPage',
    method: 'post',
    data: data
  })
}

// 删除公告
export function deleteMessage (data) {
  return request({
    url: '/tg/deleteData',
    method: 'post',
    data: data
  })
}
// 删除消息
export function release (data) {
  return request({
    url: '/tg/release',
    method: 'post',
    data: data
  })
}
export function AddData (data) {
  return request({
    url: '/tg/editData',
    method: 'post',
    data: data
  })
}

export function pageChannelList (data) {
  return request({
    url: '/group/spiderPageChannelList',
    method: 'post',
    data: data
  })
}

export function pageChannelMember (data) {
  return request({
    url: '/group/pageChannelMember',
    method: 'post',
    data: data
  })
}

// 发布记录
export function getSendRecords (data) {
  return request({
    url: '/spiderReleaseHistory/getPage',
    method: 'post',
    data: data
  })
}
