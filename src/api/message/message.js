import request from '@/utils/request'

// 查询单聊列表
export function pagePrivateChat (data) {
  return request({
    url: '/message/pagePrivateChat',
    method: 'post',
    data: data
  })
}

// 查询群聊列表(2023.8.3查询全部消息列表)
export function pageGroupChat (data) {
  return request({
    url: '/message/pageGroupChat',
    method: 'post',
    data: data
  })
}

// 根据群名称或者群id查询详情
export function getConversationNameAndId (data) {
  return request({
    url: '/message/getConversationNameAndId',
    method: 'post',
    data: data
  })
}

// 删除消息记录
export function delMessage (data) {
  return request({
    url: '/message/delMessage',
    method: 'post',
    data: data
  })
}

// 通话记录列表
export function pageRtcRecords (data) {
  return request({
    url: '/message/pageRtcRecords',
    method: 'post',
    data: data
  })
}

// 助手列表
export function helperList (data) {
  return request({
    url: '/imUser/helper',
    method: 'post',
    data: data
  })
}

// 助手列表处理
export function dealHelper (data) {
  return request({
    url: '/imUser/helperHandle',
    method: 'post',
    data: data
  })
}

// 助手聊天记录
export function historyHelperMessage (data) {
  return request({
    url: '/message/historyHelperMessage',
    method: 'post',
    data: data
  })
}

// 助手回复用户
export function helperReplay (data) {
  return request({
    url: '/imUser/helperReplay',
    method: 'post',
    data: data
  })
}
