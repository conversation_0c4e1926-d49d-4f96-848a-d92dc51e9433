<template>
  <div class="navbar">
    <hamburger id="hamburger-container" :is-active="getters.sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar" />
    <breadcrumb id="breadcrumb-container" class="breadcrumb-container" v-if="!$store.state.settings.topNav" />
    <top-nav id="topmenu-container" class="topmenu-container" v-if="$store.state.settings.topNav" />

    <div class="right-menu">
      <div class="avatar-container">
        <el-dropdown @command="handleSetLanguage" class="international" trigger="click">
          <div class="avatar-wrapper">
            <span class="name">{{ getters.language === 'zh' ? "中文" : "English" }}</span>
            <el-icon>
              <caret-bottom/>
            </el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item :disabled="getters.language==='zh'" command="zh">
                中文
              </el-dropdown-item>
              <el-dropdown-item :disabled="getters.language==='en'" command="en">
                English
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>

      <template v-if="getters.device !== 'mobile'">
        <header-search id="header-search" class="right-menu-item" />

        <screenfull id="screenfull" class="right-menu-item hover-effect" />

        <el-tooltip :content="$t('homePage.layoutSize')" effect="dark" placement="bottom">
          <size-select id="size-select" class="right-menu-item hover-effect" />
        </el-tooltip>
      </template>
      <div class="avatar-container">
        <el-dropdown @command="handleCommand" class="right-menu-item hover-effect" trigger="click">
          <div class="avatar-wrapper">
            <img :src="getters.avatar" class="user-avatar" />
            <span class="name">{{ getters.name }}</span>
            <el-icon>
              <caret-bottom />
            </el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <router-link to="/user/profile">
                <el-dropdown-item>{{$t('homePage.personalCenter')}}</el-dropdown-item>
              </router-link>
              <el-dropdown-item command="setLayout">
                <span>{{$t('homePage.layoutSettings')}}</span>
              </el-dropdown-item>
              <el-dropdown-item divided command="logout">
                <span>{{$t('homePage.logout')}}</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ElMessageBox } from 'element-plus'
import Breadcrumb from '@/components/Breadcrumb'
import TopNav from '@/components/TopNav'
import Hamburger from '@/components/Hamburger'
import Screenfull from '@/components/Screenfull'
import SizeSelect from '@/components/SizeSelect'
import HeaderSearch from '@/components/HeaderSearch'
import RuoYiGit from '@/components/RuoYi/Git'
import RuoYiDoc from '@/components/RuoYi/Doc'
import i18n from "../../i18n";

const store = useStore()
const getters = computed(() => store.getters)

function toggleSideBar () {
  store.dispatch('app/toggleSideBar')
}

function handleCommand (command) {
  switch (command) {
    case 'setLayout':
      setLayout()
      break
    case 'logout':
      logout()
      break
    default:
      break
  }
}

function handleSetLanguage(lang) {
  store.dispatch('app/setLanguage', lang)
  location.reload()
}

function logout () {
  ElMessageBox.confirm(i18n.global.t('homePage.confirmLogout'), i18n.global.t('common.tips'), {
    confirmButtonText: i18n.global.t('common.confirm'),
    cancelButtonText: i18n.global.t('common.cancel'),
    type: 'warning'
  })
    .then(() => {
      store.dispatch('LogOut').then(() => {
        location.href = '/'
      })
    })
    .catch(() => {})
}

const emits = defineEmits(['setLayout'])
function setLayout () {
  emits('setLayout')
}
</script>

<style lang='scss' scoped>
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .topmenu-container {
    position: absolute;
    left: 50px;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;
    display: flex;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 14px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }

    .avatar-container {
      margin-right: 15px;

      .avatar-wrapper {
        height: 50px;
        display: flex;
        align-items: center;
        cursor: pointer;
        .user-avatar {
          width: 26px;
          height: 26px;
          border-radius: 50%;
          margin-right: 8px;
        }
        .name {
          margin-right: 8px;
        }
        i {
          font-size: 14px;
        }
      }
    }
  }
}
</style>
