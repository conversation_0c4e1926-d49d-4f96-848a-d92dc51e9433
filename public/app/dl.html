<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta http-equiv="Expires" content="0">
  <meta http-equiv="Pragma" content="no-cache">
  <meta http-equiv="Cache-control" content="no-cache">
  <meta http-equiv="Cache" content="no-cache">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>download</title>

  <style>
    html,
    body,
    div,
    span,
    applet,
    object,
    iframe,
    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    p,
    blockquote,
    pre,
    a,
    abbr,
    acronym,
    address,
    big,
    cite,
    code,
    del,
    dfn,
    em,
    img,
    ins,
    kbd,
    q,
    s,
    samp,
    small,
    strike,
    strong,
    sub,
    sup,
    tt,
    var,
    b,
    u,
    i,
    center,
    dl,
    dt,
    dd,
    ol,
    ul,
    li,
    fieldset,
    form,
    label,
    legend,
    table,
    caption,
    tbody,
    tfoot,
    thead,
    tr,
    th,
    td,
    article,
    aside,
    canvas,
    details,
    embed,
    figure,
    figcaption,
    footer,
    header,
    hgroup,
    menu,
    nav,
    output,
    ruby,
    section,
    summary,
    time,
    mark,
    audio,
    video {
      margin: 0;
      padding: 0;
      border: 0;
      font-size: 100%;
      font: inherit;
      vertical-align: baseline;
    }

    /* HTML5 display-role reset for older browsers */
    article,
    aside,
    details,
    figcaption,
    figure,
    footer,
    header,
    hgroup,
    menu,
    nav,
    section {
      display: block;
    }

    ol,
    ul {
      list-style: none;
    }

    blockquote,
    q {
      quotes: none;
    }

    blockquote:before,
    blockquote:after,
    q:before,
    q:after {
      content: "";
      content: none;
    }

    table {
      border-collapse: collapse;
      border-spacing: 0;
    }

    html {
      font-size: 20px;
      font-size: 5.33333vw;
      font-family: PingFangSC-Regular, PingFang SC, Hiragino Sans GB,
        Microsoft YaHei;
    }

    @media screen and (max-width: 320px) {
      html {
        font-size: 17.06667px;
      }
    }

    @media screen and (min-width: 750px) {
      html {
        font-size: 40px;
      }
    }

    body {
      /* width: 18.75rem; */
      max-width: 750px;
      box-sizing: border-box;
      margin: auto;
      position: relative;
      color: #333333;
      line-height: 1.5;
      -webkit-tap-highlight-color: rgb(255, 255, 255, 0.5);
    }

    .overflow-ellipsis-1 {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .overflow-ellipsis-2 {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      max-height: 3em;
      overflow: hidden;
    }

    .avatar {
      object-fit: cover;
    }

    a {
      text-decoration: none;
      color: #333333;
    }


    /* my---- style */

    html,
    body {
      height: 100%;
    }

    .container {
      text-align: center;
      background: url('./image/bg.png') no-repeat top;
      background-size: contain;
      min-height: 100%;
      box-sizing: border-box;
      padding-bottom: 1em;
    }

    #tip {
      background-color: #333;
      color: white;
      text-align: left;
      padding: 0.5em;
      font-size: 13px;
      display: none;
    }

    .container .app-icon-wrap {
      padding-top: 16em;

    }

    .container .app-icon {
      height: 74px;
      width: 74px;
    }

    .download-btn {
      font-size: 0.8em;
      color: #fff;
      padding: 0.5em;
      margin: 4em 1.5em;
      border-radius: 1.2em;
      background: linear-gradient(to right, #ef5a88, #fb9b89);
    }
  </style>
</head>

<body>
  <div id="tip">
    Click the button in the top-right corner, then choose "Open in browser" in
    the pop-up window to install.
  </div>
  <div class="container">
    <div class="app-icon-wrap">
      <img class="app-icon" src="image/app-icon1.png" alt="">
      <div onclick="downloadApp()" class="download-btn">下载</div>
    </div>
  </div>

</body>

<script>
  var downloadLinks = {
    appName: '',
    android: 'https://oss.wecloud.cn/wecloud-download/public/xiaolandou-android-latest.apk',
    ios: 'https://apps.apple.com/cn/app/%E5%B0%8F%E8%93%9D%E8%B1%86/id1512372524'
  }
  var ua = navigator.userAgent || "";
  var isAndroid = /android/i.test( ua );
  var isIos = /iphone|ipad|ipod/i.test( ua );
  var isWechat = /micromessenger\/([\d.]+)/i.test( ua );

  if ( isWechat ) {
    document.getElementById( 'tip' ).style.display = 'block'
  }

  // 下载
  function downloadApp () {
    if ( isAndroid ) {
      const a = document.createElement( 'a' )
      a.href = downloadLinks.android
      a.download = downloadLinks.appName
      a.click()
    } else if ( isIos ) {
      location.href = downloadLinks.ios
    } else {
      alert( '请使用移动设备打开此链接' )
    }
  }
</script>

</html>
