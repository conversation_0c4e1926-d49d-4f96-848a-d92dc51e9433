<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="renderer" content="webkit">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
  <link rel="icon" href="/favicon.ico">
  <title></title>
  <!--[if lt IE 11]><script>window.location.href='/html/ie.html';</script><![endif]-->
  <style>
    .loading-wrapper {
      position: absolute;
      top: 45%;
      left: 50%;
      transform: translate(-50%, -50%);
    }

    .loading-wrapper .loading-tip {
      margin-top: 14px;
      font-size: 14px;
      color: #777;
    }

    .loading-wrapper .loading,
    .loading-wrapper .loading>div {
      position: relative;
      box-sizing: border-box;
    }

    .loading-wrapper .loading {
      display: block;
      font-size: 0;
      color: #000;
    }

    .loading-wrapper .loading.la-dark {
      color: #333;
    }

    .loading-wrapper .loading>div {
      display: inline-block;
      float: none;
      background-color: currentColor;
      border: 0 solid currentColor;
    }

    .loading-wrapper .loading {
      width: 42px;
      height: 32px;
    }

    .loading-wrapper .loading>div:nth-child(1) {
      position: absolute;
      bottom: 32%;
      left: 18%;
      width: 14px;
      height: 14px;
      border-radius: 100%;
      transform-origin: center bottom;
      animation: ball-climbing-dot-jump 0.6s ease-in-out infinite;
    }

    .loading-wrapper .loading>div:not(:nth-child(1)) {
      position: absolute;
      top: 0;
      right: 0;
      width: 14px;
      height: 2px;
      border-radius: 0;
      transform: translate(60%, 0);
      animation: ball-climbing-dot-steps 1.8s linear infinite;
    }

    .loading-wrapper .loading>div:not(:nth-child(1)):nth-child(2) {
      animation-delay: 0ms;
    }

    .loading-wrapper .loading>div:not(:nth-child(1)):nth-child(3) {
      animation-delay: -600ms;
    }

    .loading-wrapper .loading>div:not(:nth-child(1)):nth-child(4) {
      animation-delay: -1200ms;
    }

    @keyframes ball-climbing-dot-jump {
      0% {
        transform: scale(1, 0.7);
      }

      20% {
        transform: scale(0.7, 1.2);
      }

      40% {
        transform: scale(1, 1);
      }

      50% {
        bottom: 125%;
      }

      46% {
        transform: scale(1, 1);
      }

      80% {
        transform: scale(0.7, 1.2);
      }

      90% {
        transform: scale(0.7, 1.2);
      }

      100% {
        transform: scale(1, 0.7);
      }
    }

    @keyframes ball-climbing-dot-steps {
      0% {
        top: 0;
        right: 0;
        opacity: 0;
      }

      50% {
        opacity: 1;
      }

      100% {
        top: 100%;
        right: 100%;
        opacity: 0;
      }
    }
  </style>
</head>

<body>
  <div id="app">
    <div class="loading-wrapper">
      <div class="loading">
        <div></div>
        <div></div>
        <div></div>
        <div></div>
      </div>
      <div class="loading-tip">loading...</div>
    </div>
  </div>
  <script type="module" src="/src/main.js"></script>
</body>

</html>
