variables:
  REGISTRY: nexus.metathought.cc:5000
  PORT: 3001
  APP_NAME: mos-fe-admin
  # 完整镜像名称: [仓库地址]/[命名空间]/[应用名]:[分支名]
  IMAGE_FULL: nexus.metathought.cc:5000/mos-fe/mos-admin:$CI_COMMIT_REF_NAME

workflow:
  rules:
    # 测试环境触发: test 分支变更
    - if: $CI_COMMIT_BRANCH == "test"
      variables:
        ENV: test
    # 生产环境: 打 tag
    - if: $CI_COMMIT_TAG
      variables:
        ENV: prod
    - when: never

stages:
  - build
  - deploy

# 构建
node-build:
  stage: build
  image: docker:23.0.3
  script:
    - >-
      DOCKER_BUILDKIT=0
      docker build --build-arg ENV=$ENV -t $IMAGE_FULL .
    - docker push $IMAGE_FULL
    # - docker rmi $IMAGE_FULL
  after_script:
    - docker images | grep none | awk '{print $3}' | xargs -i docker rmi -f {}
    - false || exit_code=$?
    - if [ $exit_code -ne 0 ]; then echo "Previous command failed"; fi;
  tags:
    - $ENV
  when: manual

# 部署
docker-deploy:
  stage: deploy
  image: docker:23.0.3
  script:
    - docker rm -f $APP_NAME
    - docker run -d --name $APP_NAME -p $PORT:80 $IMAGE_FULL
  tags:
    - $ENV
  when: manual
