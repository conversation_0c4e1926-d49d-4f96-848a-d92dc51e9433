{"name": "mos-web", "version": "3.8.1", "description": "", "author": "", "scripts": {"dev": "vite", "qa": "vite --mode qa", "lint": "vue-cli-service lint", "build:prod": "vite build", "build:test": "vite build --mode test", "build:qa": "vite build --mode qa", "build:stage": "vite build --mode staging", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "0.2.6", "@vueuse/core": "^8.2.6", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "0.24.0", "crypto-js": "4.1.1", "echarts": "^5.2.2", "element-plus": "2.0.1", "file-saver": "2.0.5", "fuse.js": "6.4.6", "js-cookie": "3.0.1", "jsencrypt": "3.2.1", "json-bigint": "^1.0.0", "nprogress": "0.2.0", "quill": "^1.3.7", "vue": "3.2.26", "vue-cropper": "1.0.2", "vue-i18n": "^9.9.0", "vue-router": "4.0.12", "vuex": "4.0.2"}, "devDependencies": {"@vitejs/plugin-vue": "^1.9.4", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/compiler-sfc": "3.2.22", "@vue/eslint-config-standard": "^6.1.0", "eslint": "^7.32.0", "eslint-plugin-import": "^2.25.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.0", "eslint-plugin-vue": "^8.0.3", "lint-staged": "^11.1.2", "sass": "1.45.0", "unplugin-auto-import": "0.5.3", "vite": "2.6.14", "vite-plugin-compression": "0.3.6", "vite-plugin-svg-icons": "1.0.5", "vite-plugin-vue-setup-extend": "0.1.0"}, "license": "MIT", "repository": {"type": "git", "url": "https://gitee.com/y_project/RuoYi-Vue.git"}}